import * as fs from "fs";
import { generateZodClientFromOpenAPI } from "openapi-zod-client";
import * as path from "path";
import { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function generateTypes() {
  try {
    // Read local OpenAPI schema
    const schemaPath = path.resolve(__dirname, "../openapi.yaml");
    const schemaContent = fs.readFileSync(schemaPath, "utf-8");

    // Parse YAML schema
    const { load } = await import("js-yaml");
    const schema = load(schemaContent) as any;
    const apiUrl = "https://api.mulapin.com/v2";

    // Generate types
    const zodClient = await generateZodClientFromOpenAPI({
      openApiDoc: schema,
      options: {
        withImplicitRequiredProps: true,
        baseUrl: apiUrl,
      },
      disableWriteToFile: true,
    });

    // Create output directory
    const outputDir = path.resolve(__dirname, "../src/types/api");
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Extract schema names from the generated code to automatically create type exports
    const extractSchemaNames = (code: string): string[] => {
      // Pattern to match all const declarations in the generated code
      const schemaPattern = /^const (\w+) = /gm;
      const schemaNames: string[] = [];
      let match: RegExpExecArray | null;
      
      while ((match = schemaPattern.exec(code)) !== null) {
        const schemaName = match[1];
        // Exclude non-schema constants like endpoints, api, etc.
        if (schemaName && 
            schemaName !== 'endpoints' && 
            schemaName !== '__filename' && 
            schemaName !== '__dirname' &&
            !schemaNames.includes(schemaName)) {
          schemaNames.push(schemaName);
        }
      }
      
      return schemaNames;
    };

    // Generate type name mappings for cleaner names
    const generateTypeMapping = (schemaNames: string[]): { [key: string]: string } => {
      const typeMapping: { [key: string]: string } = {};
      
      schemaNames.forEach(schemaName => {
        // Create cleaner type names
        if (schemaName === 'CognitoAuthRequest') {
          typeMapping[schemaName] = 'LoginRequest';
        } else if (schemaName === 'Error') {
          typeMapping[schemaName] = 'ApiError';
        } else if (schemaName === 'User') {
          typeMapping[schemaName] = 'UserProfile';
        } else if (schemaName.startsWith('post') && schemaName.includes('_Body')) {
          // Convert post endpoint body schemas to cleaner names
          const cleanName = schemaName
            .replace(/^post/, '')
            .replace(/_Body$/, '')
            .replace(/([a-z])([A-Z])/g, '$1$2')
            .split(/(?=[A-Z])/)
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join('');
          typeMapping[schemaName] = cleanName + 'Request';
        } else {
          typeMapping[schemaName] = schemaName;
        }
      });
      
      return typeMapping;
    };

    // Generate organized type exports
    const generateTypeExports = (schemaNames: string[], typeMapping: { [key: string]: string }): string => {
      const categories = {
        'Authentication': ['CognitoAuthRequest', 'Error'],
        'Organizations': ['Organization', 'OrganizationCreate', 'OrganizationUpdate', 'OrganizationDetails'],
        'Users': ['UserReference', 'User', 'UserCreate', 'UserUpdate', 'UserDetails'],
        'Customer Goals': ['CustomerGoal', 'CustomerGoalCreate', 'CustomerGoalUpdate', 'CustomerGoalDetails'],
        'Maps': ['Map', 'MapCreate', 'MapUpdate', 'MapDetails'],
        'Phases': ['Phase', 'PhaseCreate', 'PhaseUpdate', 'PhaseDetails'],
        'Moments': ['Moment', 'MomentCreate', 'MomentUpdate', 'MomentDetails'],
        'Activities': ['Activity', 'ActivityCreate', 'ActivityUpdate', 'ActivityDetails', 'ActivitiesByType'],
        'Actions': ['Action', 'ActionCreate', 'ActionUpdate', 'ActionDetails'],
        'Personas': ['Persona', 'PersonaCreate', 'PersonaUpdate', 'PersonaDetails'],
        'Components': ['Component', 'ComponentCreate', 'ComponentUpdate', 'ComponentDetails', 'ComponentMatch', 'ComponentValidationError', 'ComponentUploadResult'],
        'Other': []
      };

      // Categorize remaining schemas
      const categorizedSchemas = new Set();
      Object.values(categories).flat().forEach(schema => categorizedSchemas.add(schema));
      
      schemaNames.forEach(schemaName => {
        if (!categorizedSchemas.has(schemaName)) {
          categories['Other'].push(schemaName);
        }
      });

      let exports = '';
      
      Object.entries(categories).forEach(([category, schemas]) => {
        const existingSchemas = schemas.filter(schema => schemaNames.includes(schema));
        
        if (existingSchemas.length > 0) {
          exports += `\n// ${category}\n`;
          existingSchemas.forEach(schemaName => {
            const typeName = typeMapping[schemaName];
            exports += `export type ${typeName} = z.infer<typeof ${schemaName}>;\n`;
          });
        }
      });

      return exports;
    };

    // Extract schema names from generated code
    const schemaNames = extractSchemaNames(zodClient);
    const typeMapping = generateTypeMapping(schemaNames);
    const dynamicTypeExports = generateTypeExports(schemaNames, typeMapping);

    // Create the type exports only (ApiV2Client moved to separate file)
    const typeExports = `// Export TypeScript types from Zod schemas${dynamicTypeExports}

// Re-export the API client from base
export { apiV2Client, default as default } from "@/api/v2/base";

`;
    
    const modifiedClient = zodClient.replace(
      'export const schemas = {',
      typeExports + 'export const schemas = {'
    );

    // Write the modified client code
    fs.writeFileSync(`${outputDir}/client.ts`, modifiedClient);

    console.log("✅ API types generated successfully");
  } catch (error) {
    console.error("❌ Error generating types:", error);
  }
}

generateTypes();

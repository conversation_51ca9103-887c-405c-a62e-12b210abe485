# Mulapin

Mulapin is a modern web application built with Next.js and Material UI, designed to provide a comprehensive user interface with a focus on design consistency and developer experience.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Tech Stack](#tech-stack)
- [Project Structure](#project-structure)
- [Design System](#design-system)

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js** (v18 or later) - [Download Node.js](https://nodejs.org/)
- **IDE** - We recommend using either:
  - [Visual Studio Code](https://code.visualstudio.com/) with the following extensions:
    - ESLint
    - Prettier
    - TypeScript
  - [Cursor](https://cursor.sh/) which comes with many helpful features for development
- **Git** - [Download Git](https://git-scm.com/downloads) (This is included in VS Code)

## Getting Started

### Clone the Repository

```bash
git clone https://github.com/Mulapin/mulapin.git
cd mulapin
```

### Install Dependencies

```bash
npm install
```

### Run the Development Server

```bash
npm run dev
```

Open [http://localhost:3009](http://localhost:3009) with your browser to see the application.

### Launch Storybook

Storybook provides a sandbox to build and test UI components in isolation.

```bash
npm run storybook
```

Open [http://localhost:6006](http://localhost:6006) to view the Storybook interface locally.

#### Storybook Environments

- **Development**: [https://mulapin-storybook-git-develop-mulapin.vercel.app/](https://mulapin-storybook-git-develop-mulapin.vercel.app/)
- **Production**: [https://mulapin-storybook.vercel.app/](https://mulapin-storybook.vercel.app/)

## Development Workflow

Follow these steps when contributing to the project:

1. **Pull the latest main branch**
2. **Create a new branch from main**
3. **Make your changes**

   - Write clean, maintainable code
   - Follow the established code style and patterns
   - Add comments where necessary

4. **Test your changes**

   - Test on local Mulapin app (http://localhost:3009)
   - Test on Storybook (http://localhost:6006)
   - Ensure all existing functionality still works

5. **Build the project to check for errors**

   ```bash
   npm run build
   ```

   Fix any build errors before proceeding.

6. **Commit your changes**

   Write clear, concise commit messages that explain what changes were made and why.

7. **Push/Publish your branch**
8. **Create a Pull Request**

   - Go to the GitHub repository
   - Create a new Pull Request from your branch targeting the `develop` branch
   - Provide a detailed description of your changes
   - Reference any related issues

9. **Request a review**

   - Assign reviewers to your Pull Request
   - Address any feedback or requested changes

10. **Merge your changes**
    - Once approved, merge your Pull Request
    - Delete your branch after merging

## Tech Stack

Mulapin is built with the following technologies:

### Core

- **[Next.js](https://nextjs.org/)** - React framework for production
- **[React](https://reactjs.org/)** - JavaScript library for building user interfaces
- **[TypeScript](https://www.typescriptlang.org/)** - Typed JavaScript

### UI

- **[Material UI (MUI)](https://mui.com/)** - React UI component library
- **[Storybook](https://storybook.js.org/)** - UI component development environment

### State Management

- **[React Context API](https://reactjs.org/docs/context.html)** - For global state management

### Styling

- **[Emotion](https://emotion.sh/)** - CSS-in-JS library (used by MUI)
- **[MUI Theme](https://mui.com/customization/theming/)** - Customizable design system

### Code Quality

- **[ESLint](https://eslint.org/)** - JavaScript linting
- **[Prettier](https://prettier.io/)** - Code formatting

### Build Tools

- **[Webpack](https://webpack.js.org/)** - Module bundler (via Next.js)
- **[Babel](https://babeljs.io/)** - JavaScript compiler (via Next.js)

## Project Structure

```
mulapin/
├── .storybook/          # Storybook configuration
├── public/              # Static files
├── src/
│   ├── app/             # Next.js app router pages
│   ├── components/       # Reusable UI components
│   ├── hooks/           # Custom React hooks
│   ├── stories/         # Storybook stories
│   ├── styles/          # Global styles and theme
│   ├── types/           # TypeScript type definitions
│   └── utils/           # Utility functions
├── .eslintrc.js         # ESLint configuration
├── .gitignore           # Git ignore rules
├── next.config.js       # Next.js configuration
├── package.json         # Project dependencies and scripts
├── README.md            # Project documentation
└── tsconfig.json        # TypeScript configuration
```

## Design System

Mulapin uses a comprehensive design system built on top of Material UI. The design tokens are defined in `src/styles/design-tokens.ts` and are used to create the theme in `src/styles/theme.ts`.

You can view the design system in Storybook under the "Basic/Design System" section, which includes:

- **Design Tokens** - Colors, typography, spacing, etc.
- **Documentation** - How to use and update the design system

Access the design system in Storybook:

- Local: [http://localhost:6006/?path=/docs/basic-design-system-documentation--docs](http://localhost:6006/?path=/docs/basic-design-system-documentation--docs)
- Development: [https://mulapin-storybook-git-develop-mulapin.vercel.app/?path=/docs/basic-design-system-documentation--docs](https://mulapin-storybook-git-develop-mulapin.vercel.app/?path=/docs/basic-design-system-documentation--docs)
- Production: [https://mulapin-storybook.vercel.app/?path=/docs/basic-design-system-documentation--docs](https://mulapin-storybook.vercel.app/?path=/docs/basic-design-system-documentation--docs)

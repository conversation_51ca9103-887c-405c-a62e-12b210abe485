import { ThemeProvider } from "@mui/material/styles";
import type { Preview } from "@storybook/react";
import { themes } from "@storybook/theming";
import React from "react";
import { getCustomTheme } from "../src/styles/theme";

export const decorators = [
  (Story) => {
    return (
      <ThemeProvider theme={getCustomTheme("dark")}>
        <div style={{ padding: "20px" }}>
          <Story />
        </div>
      </ThemeProvider>
    );
  },
];

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    docs: {
      theme: themes.dark,
    },
    darkMode: {
      current: "dark",
    },
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#121212" },
        { name: "light", value: "#f8f8f8" },
      ],
    },
  },
};

export default preview;

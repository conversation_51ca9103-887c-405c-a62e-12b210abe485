openapi: 3.0.3
info:
  title: Mulapin API v2.0
  version: "2"
  description: |
    Complete customer journey mapping API platform with 53 endpoints across authentication and business logic.

    **Authentication Flow:**
    1. Call `POST /auth/login` with username/password to get Cognito tokens
    2. Extract the `IdToken` from the response
    3. Use the `IdToken` as Bearer token for all other API calls

    **Authentication:**
    - Uses AWS Cognito for authentication via `/auth/login` endpoint
    - All business endpoints require Bear<PERSON> token in Authorization header
    - Token format: `Authorization: Bearer <IdToken>`
    - **Important**: Use IdToken (not AccessToken) from Cognito

    **Organization Context:**
    - Organization automatically determined from JWT token
    - Users are restricted to their organization only
    - First-time users use POST /organization for onboarding

    **Role Hierarchy:**
    - `admin`: Full organization access, can manage users and all content
    - `writer`: Can create and edit journey content
    - `reader`: Read-only access to journey content

servers:
  - url: https://z0p4210n29.execute-api.ap-southeast-2.amazonaws.com/Prod
    description: Production API (AWS API Gateway)
  - url: http://127.0.0.1:3000
    description: Local Development (SAM Local)

security:
  - BearerAuth: []

paths:
  # Authentication endpoint (Cognito)
  /auth/login:
    post:
      summary: Authenticate with Cognito
      description: |
        **Note: This endpoint uses the AWS Cognito server, not your main API server.**

        Authenticate user with username and password using AWS Cognito.
        Copy the `IdToken` from the response to use as Bearer token for other endpoints.

        **Authentication Flow:**
        1. Call this endpoint with username/password
        2. Extract the `IdToken` from the response  
        3. Use the `IdToken` as Bearer token for all other API calls
      tags:
        - Authentication
      # Override server for this specific endpoint
      servers:
        - url: https://cognito-idp.ap-southeast-2.amazonaws.com
          description: AWS Cognito Identity Provider Service
      security: [] # No auth required for login
      parameters:
        - name: Content-Type
          in: header
          required: true
          schema:
            type: string
            default: "application/x-amz-json-1.1"
        - name: X-Amz-Target
          in: header
          required: true
          schema:
            type: string
            default: "AWSCognitoIdentityProviderService.InitiateAuth"
      requestBody:
        required: true
        content:
          application/x-amz-json-1.1:
            schema:
              $ref: "#/components/schemas/CognitoAuthRequest"
            example:
              ClientId: "4f7v5jru6aajsm4gtluvtqdai7"
              AuthFlow: "USER_PASSWORD_AUTH"
              AuthParameters:
                USERNAME: "<EMAIL>"
                PASSWORD: "Password123!@#"
      responses:
        "200":
          description: Authentication successful
          content:
            application/x-amz-json-1.1:
              schema:
                $ref: "#/components/schemas/CognitoAuthResponse"
              example:
                AuthenticationResult:
                  AccessToken: "eyJraWQiOiJLTzRVMWZs..."
                  ExpiresIn: 3600
                  IdToken: "eyJraWQiOiJLTzRVMWZs..."
                  RefreshToken: "eyJjdHkiOiJKV1Qi..."
                  TokenType: "Bearer"
        "400":
          description: Invalid request parameters
          content:
            application/x-amz-json-1.1:
              schema:
                type: object
                properties:
                  __type:
                    type: string
                    example: "InvalidParameterException"
                  message:
                    type: string
                    example: "Invalid request parameters"
        "401":
          description: Invalid credentials
          content:
            application/x-amz-json-1.1:
              schema:
                type: object
                properties:
                  __type:
                    type: string
                    example: "NotAuthorizedException"
                  message:
                    type: string
                    example: "Incorrect username or password."
        "429":
          description: Too many requests
          content:
            application/x-amz-json-1.1:
              schema:
                type: object
                properties:
                  __type:
                    type: string
                    example: "TooManyRequestsException"
                  message:
                    type: string
                    example: "Too Many Requests"

  /organization:
    get:
      tags: [Organization]
      summary: Get Current Organization
      description: |
        Retrieves the current user's organization details including business structure and statistics.
        Returns 404 if the user has no organization (needs onboarding).
      responses:
        "200":
          description: Organization details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OrganizationDetails"
              example:
                data:
                  organization_id: "acme-retail-group"
                  name: "Acme Retail Group"
                  industry: "Retail & E-commerce"
                  subscription_tier: "professional"
                  settings:
                    timezone: "Australia/Sydney"
                    currency: "AUD"
                    language: "en"
                  business_structure:
                    divisions: ["Online", "Retail Stores", "Wholesale"]
                    channels: ["Website", "Mobile App", "Physical Stores", "B2B Portal"]
                    products: ["Electronics", "Home & Garden", "Fashion", "Sports"]
                    brands: ["Acme Electronics", "Acme Home", "Acme Active"]
                  status: "active"
                  created_at: "2024-01-01T00:00:00Z"
                  updated_at: "2024-01-15T10:30:00Z"
                  statistics:
                    total_users: 12
                    total_maps: 8
                    total_personas: 15
                message: "Organization details retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"
        "404":
          description: User has no organization (needs onboarding)
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error: "not_found"
                code: "ORG_404"
                message: "No organization found for user. Please complete onboarding."
                timestamp: "2024-01-15T14:30:00Z"

    post:
      tags: [Organization]
      summary: Create Organization (User Onboarding)
      description: |
        Creates a new organization for first-time users. This endpoint handles the onboarding flow where:
        1. User has valid Cognito JWT token but no organization in our database
        2. Creates organization + user record atomically
        3. User becomes admin of the new organization
        4. User ID in database matches Cognito sub claim
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OrganizationCreate"
            example:
              name: "Global Tech Solutions"
              industry: "Technology Services"
              description: "Leading provider of enterprise software solutions"
              subscription_tier: "professional"
              settings:
                timezone: "America/New_York"
                currency: "USD"
                language: "en"
              business_structure:
                divisions: ["Product Development", "Professional Services", "Support"]
                channels: ["Direct Sales", "Partner Network", "Online"]
                products: ["ERP Suite", "CRM Platform", "Analytics Tools"]
                brands: ["GTS Enterprise", "GTS Cloud"]
      responses:
        "201":
          description: Organization created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  organization:
                    $ref: "#/components/schemas/OrganizationDetails"
                  user:
                    $ref: "#/components/schemas/User"
                  message:
                    type: string
                    example: "Organization created successfully. You are now the admin."
              example:
                data:
                  organization:
                    organization_id: "global-tech-solutions"
                    name: "Global Tech Solutions"
                    industry: "Technology Services"
                    description: "Leading provider of enterprise software solutions"
                    subscription_tier: "professional"
                    settings:
                      timezone: "America/New_York"
                      currency: "USD"
                      language: "en"
                    business_structure:
                      divisions: ["Product Development", "Professional Services", "Support"]
                      channels: ["Direct Sales", "Partner Network", "Online"]
                      products: ["ERP Suite", "CRM Platform", "Analytics Tools"]
                      brands: ["GTS Enterprise", "GTS Cloud"]
                    status: "active"
                    created_at: "2024-01-15T14:30:00Z"
                    updated_at: "2024-01-15T14:30:00Z"
                    statistics:
                      total_users: 1
                      total_maps: 0
                      total_personas: 0
                  user:
                    user_id: "59ee54e8-0071-7071-c56c-beec590dc95f"
                    organization_id: "global-tech-solutions"
                    profile:
                      email: "<EMAIL>"
                      first_name: "John"
                      last_name: "Doe"
                      display_name: "John D."
                    access_control:
                      role: "admin"
                      department: "Executive"
                      permissions: ["all"]
                    created_at: "2024-01-15T14:30:00Z"
                    updated_at: "2024-01-15T14:30:00Z"
                message: "Organization created successfully. You are now the admin."
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"
        "409":
          description: User already has an organization
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "User already belongs to an organization"
                  existing_organization_id:
                    type: string
                    example: "acme-retail-group"

    put:
      tags: [Organization]
      summary: Update Current Organization
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OrganizationUpdate"
      responses:
        "200":
          description: Organization updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Organization"
              example:
                data:
                  organization_id: "acme-retail-group"
                  name: "Acme Retail Group - Enhanced"
                  industry: "Retail & E-commerce"
                  description: "Leading retail company with enhanced digital transformation initiatives"
                  subscription_tier: "professional"
                  settings:
                    timezone: "Australia/Sydney"
                    currency: "AUD"
                    language: "en"
                    notifications_enabled: true
                  business_structure:
                    divisions: ["Online", "Retail Stores", "Wholesale", "B2B"]
                    channels:
                      ["Website", "Mobile App", "Physical Stores", "B2B Portal", "Social Commerce"]
                    products:
                      ["Electronics", "Home & Garden", "Fashion", "Sports", "Health & Beauty"]
                    brands: ["Acme Electronics", "Acme Home", "Acme Active", "Acme Health"]
                  status: "active"
                  created_at: "2024-01-01T00:00:00Z"
                  updated_at: "2025-07-25T08:42:09Z"
                message: "Organization updated successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

  /users:
    get:
      tags: [Users]
      summary: Get Organization Users
      description: |
        Retrieves all users in the current organization.
        Requires 'admin' role to view all users.
        Results include user profiles and access control information.
      parameters:
        - name: role
          in: query
          required: false
          schema:
            type: string
            enum: [admin, writer, reader]
          description: Filter users by role
        - name: department
          in: query
          required: false
          schema:
            type: string
          description: Filter users by department
      responses:
        "200":
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/User"
              example:
                data:
                  - user_id: "usr_emily_rodriguez_001"
                    organization_id: "acme-retail-group"
                    profile:
                      email: "<EMAIL>"
                      first_name: "Emily"
                      last_name: "Rodriguez"
                      display_name: "Emily R."
                      avatar_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg"
                    access_control:
                      role: "admin"
                      department: "Operations"
                      permissions: ["maps.create", "maps.edit", "users.manage"]
                    created_at: "2024-01-01T00:00:00Z"
                    updated_at: "2024-01-10T14:30:00Z"
                  - user_id: "usr_alex_chen_002"
                    organization_id: "acme-retail-group"
                    profile:
                      email: "<EMAIL>"
                      first_name: "Alex"
                      last_name: "Chen"
                      display_name: "Alex C."
                    access_control:
                      role: "writer"
                      department: "Digital Marketing"
                      permissions: ["maps.create", "maps.edit"]
                    created_at: "2024-01-05T10:00:00Z"
                    updated_at: "2024-01-12T09:15:00Z"
                message: "Users retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    post:
      tags: [Users]
      summary: Create User
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserCreate"
      responses:
        "201":
          description: User created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
              example:
                data:
                  user_id: "usr_sarah_johnson_003"
                  organization_id: "acme-retail-group"
                  profile:
                    email: "<EMAIL>"
                    first_name: "Sarah"
                    last_name: "Johnson"
                    display_name: "Sarah J."
                  access_control:
                    role: "writer"
                    department: "Marketing"
                    permissions: ["maps.create", "maps.edit"]
                  created_at: "2025-07-25T08:42:09Z"
                  updated_at: "2025-07-25T08:42:09Z"
                message: "User created successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

  /users/{userId}:
    get:
      tags: [Users]
      summary: Get User Details
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: User details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDetails"
              example:
                data:
                  user_id: "usr_emily_rodriguez_001"
                  organization_id: "acme-retail-group"
                  profile:
                    email: "<EMAIL>"
                    first_name: "Emily"
                    last_name: "Rodriguez"
                    display_name: "Emily R."
                    avatar_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg"
                  access_control:
                    role: "admin"
                    department: "Operations"
                    permissions: ["maps.create", "maps.edit", "users.manage"]
                  created_at: "2024-01-01T00:00:00Z"
                  updated_at: "2024-01-10T14:30:00Z"
                message: "User details retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    put:
      tags: [Users]
      summary: Update User
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserUpdate"
      responses:
        "200":
          description: User updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
              example:
                data:
                  user_id: "usr_emily_rodriguez_001"
                  organization_id: "acme-retail-group"
                  profile:
                    email: "<EMAIL>"
                    first_name: "Emily"
                    last_name: "Rodriguez"
                    display_name: "Emily R."
                    avatar_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg"
                    phone: "+61 2 9876 5432"
                  access_control:
                    role: "admin"
                    department: "Operations"
                    permissions: ["maps.create", "maps.edit", "users.manage"]
                  created_at: "2024-01-01T00:00:00Z"
                  updated_at: "2025-07-25T08:42:09Z"
                message: "User updated successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    delete:
      tags: [Users]
      summary: Delete User
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: User deleted

  /customer-goals:
    get:
      tags: [Customer Goals]
      summary: Get Customer Goals
      description: |
        Retrieves all customer goals for the current organization.
        Customer goals are high-level objectives that customers want to achieve.
        Each goal can have multiple journey maps associated with it.
      parameters:
        - name: priority
          in: query
          required: false
          schema:
            type: string
            enum: [high, medium, low]
          description: Filter goals by priority level
        - name: status
          in: query
          required: false
          schema:
            type: string
            enum: [active, draft, archived]
          description: Filter goals by status
      responses:
        "200":
          description: Customer goals retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/CustomerGoal"
              example:
                data:
                  - customer_goal_id: "cg_complete_shopping_2024"
                    organization_id: "acme-retail-group"
                    name: "Complete Weekly Grocery Shopping"
                    description: "Successfully purchase all needed groceries for the week with minimal time and effort"
                    category: "Shopping & Commerce"
                    priority: "high"
                    status: "active"
                    created_at: "2024-01-02T10:00:00Z"
                    updated_at: "2024-01-10T14:20:00Z"
                    created_by:
                      user_id: "usr_emily_rodriguez_001"
                      name: "Emily Rodriguez"
                  - customer_goal_id: "cg_quick_meal_prep_2024"
                    organization_id: "acme-retail-group"
                    name: "Quick Meal Preparation"
                    description: "Prepare healthy meals quickly with minimal preparation time"
                    category: "Food & Cooking"
                    priority: "medium"
                    status: "active"
                    created_at: "2024-01-05T11:30:00Z"
                    updated_at: "2024-01-08T09:15:00Z"
                message: "Customer goals retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    post:
      tags: [Customer Goals]
      summary: Create Customer Goal
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerGoalCreate"
      responses:
        "201":
          description: Customer goal created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerGoal"
              example:
                data:
                  customer_goal_id: "goal_improve_mobile_experience_1753420145"
                  name: "Improve Mobile Shopping Experience"
                  description: "Enhance the mobile app user experience to increase customer satisfaction and conversion rates"
                  category: "Digital Experience"
                  priority: "high"
                  status: "active"
                  organization_id: "acme-retail-group"
                  created_at: "2025-07-25T08:42:09Z"
                  updated_at: "2025-07-25T08:42:09Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Customer goal created successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

  /customer-goals/{customer_goal_id}:
    get:
      tags: [Customer Goals]
      summary: Get Customer Goal
      parameters:
        - name: customer_goal_id
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Customer goal details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerGoalDetails"
              example:
                data:
                  customer_goal_id: "goal_complete_online_purchase_1753411888"
                  name: "Complete Online Purchase"
                  description: "Customer wants to successfully purchase a product through our e-commerce platform with minimal friction and maximum confidence"
                  category: "E-commerce"
                  priority: "high"
                  status: "active"
                  organization_id: "test-organization-2025"
                  created_at: "2025-07-25T02:51:28.793831Z"
                  updated_at: "2025-07-25T02:51:28.793831Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Customer goal retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    put:
      tags: [Customer Goals]
      summary: Update Customer Goal
      parameters:
        - name: customer_goal_id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CustomerGoalUpdate"
      responses:
        "200":
          description: Customer goal updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CustomerGoal"
              example:
                data:
                  customer_goal_id: "goal_complete_online_purchase_1753411888"
                  name: "Complete Online Purchase Seamlessly"
                  description: "Customer wants to successfully purchase a product through our e-commerce platform with minimal friction, maximum confidence, and excellent customer support"
                  category: "E-commerce"
                  priority: "high"
                  status: "active"
                  organization_id: "test-organization-2025"
                  created_at: "2025-07-25T02:51:28.793831Z"
                  updated_at: "2025-07-25T08:42:09Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Customer goal updated successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    delete:
      tags: [Customer Goals]
      summary: Delete Customer Goal
      parameters:
        - name: customer_goal_id
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Customer goal deleted

  /maps:
    get:
      tags: [Maps]
      summary: Get Maps
      description: |
        Retrieves all journey maps for the current organization.
        Maps are the core entities that connect customer goals to detailed journey phases.
      parameters:
        - name: customer_goal_id
          in: query
          required: false
          schema:
            type: string
          description: Filter maps by customer goal ID
        - name: state
          in: query
          required: false
          schema:
            type: string
            enum: [draft, review, published, archived]
          description: Filter maps by state
      responses:
        "200":
          description: Maps retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Map"
              example:
                data:
                  - map_id: "map_grocery_journey_v3"
                    organization_id: "acme-retail-group"
                    customer_goal:
                      customer_goal_id: "cg_complete_shopping_2024"
                      name: "Complete Weekly Grocery Shopping"
                      priority: "high"
                    name: "In-Store Shopping Experience"
                    description: "Complete journey mapping for customers shopping in physical grocery stores"
                    division: "Retail Operations"
                    state: "published"
                    status: "active"
                    created_at: "2024-01-10T09:00:00Z"
                    updated_at: "2024-01-14T16:45:00Z"
                  - map_id: "map_online_ordering_v2"
                    organization_id: "acme-retail-group"
                    customer_goal:
                      customer_goal_id: "cg_order_online_2024"
                      name: "Order Groceries Online"
                      priority: "high"
                    name: "Online Shopping Journey"
                    description: "End-to-end digital grocery shopping experience"
                    division: "Digital Commerce"
                    state: "review"
                    status: "active"
                    created_at: "2024-01-12T11:30:00Z"
                    updated_at: "2024-01-15T14:20:00Z"
                message: "Maps retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    post:
      tags: [Maps]
      summary: Create Map
      description: |
        Creates a new journey map linked to a customer goal.
        Maps organize the customer journey into phases, moments, and activities.
        Requires 'writer' or 'admin' role.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MapCreate"
            example:
              name: "Mobile App Shopping Journey"
              description: "End-to-end customer journey for mobile app grocery shopping experience"
              customer_goal_id: "cg_complete_shopping_2024"
              division: "Digital Commerce"
              state: "draft"
      responses:
        "201":
          description: Map created successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Map"
              example:
                data:
                  map_id: "map_mobile_journey_v1"
                  organization_id: "acme-retail-group"
                  customer_goal:
                    customer_goal_id: "cg_complete_shopping_2024"
                    name: "Complete Weekly Grocery Shopping"
                    priority: "high"
                  name: "Mobile App Shopping Journey"
                  description: "End-to-end customer journey for mobile app grocery shopping experience"
                  division: "Digital Commerce"
                  state: "draft"
                  status: "active"
                  created_at: "2024-01-15T14:30:00Z"
                  updated_at: "2024-01-15T14:30:00Z"
                  created_by:
                    user_id: "usr_john_doe_001"
                    name: "John Doe"
                message: "Map created successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"
        "400":
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error: "validation_failed"
                code: "VAL_001"
                message: "Customer goal ID 'cg_invalid_001' not found in this organization"
                timestamp: "2024-01-15T14:30:00Z"
        "409":
          description: Map name already exists
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              example:
                error: "conflict"
                code: "MAP_409"
                message: "A journey map with the name 'Mobile App Shopping Journey' already exists"
                timestamp: "2024-01-15T14:30:00Z"

  /maps/{mapId}:
    get:
      tags: [Maps]
      summary: Get Map Details
      description: |
        Retrieves comprehensive details of a specific journey map including:
        - All phases with their sequences and components
        - All moments across phases (deduplicated if shared)
        - All personas referenced in the map
        - All components used throughout the journey
        - Nested activities grouped by type (customer, front_stage, back_stage, system)
        - Detailed actions within each activity
        - Summary statistics

        This endpoint provides the complete journey structure for visualization and analysis.
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: string
          description: The ID of the map to retrieve
          example: "map_grocery_journey_v3"
      responses:
        "200":
          description: Map details retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MapDetails"
              example:
                data:
                  map_id: "map_grocery_journey_v3"
                  organization_id: "acme-retail-group"
                  customer_goal:
                    customer_goal_id: "goal_complete_online_purchase_1753411888"
                    name: "Complete Online Purchase"
                    description: "Customer wants to successfully purchase a product through our e-commerce platform with minimal friction and maximum confidence"
                    category: "E-commerce"
                    priority: "high"
                    status: "active"
                    organization_id: "test-organization-2025"
                  name: "In-Store Shopping Experience"
                  description: "Complete journey mapping for customers shopping in physical grocery stores"
                  division: "Retail Operations"
                  state: "published"
                  status: "active"
                  phases:
                    - phase_id: "phase_research_products"
                      name: "Product Research & Planning"
                      description: "Customer researches products and plans their shopping trip"
                      sequence: 1
                      moment_ids:
                        - "moment_compare_prices_online"
                      components_ids:
                        - "comp_mobile_app_003"
                        - "comp_price_comparison_002"
                    - phase_id: "phase_purchase_decision"
                      name: "Purchase Decision"
                      description: "Customer decides what to buy and adds to cart"
                      sequence: 2
                      moment_ids:
                        - "moment_add_items_to_cart"
                      components_ids:
                        - "comp_shopping_cart_system"
                        - "comp_inventory_system_001"
                  moments:
                    - moment_id: "moment_compare_prices_online"
                      name: "Compare Prices Online"
                      description: "Customer compares prices across different platforms"
                      phase_id: "phase_research_products"
                      sequence: 1
                      activities:
                        customer:
                          - activity_id: "act_browse_mobile_catalog"
                            name: "Browse Product Catalog"
                            description: "Customer browses products using mobile app"
                            activity_type: "customer"
                            moment_id: "moment_compare_prices_online"
                            sequence: 1
                            estimated_duration: "3-5 minutes"
                            complexity_level: "medium"
                            personas:
                              - "persona_busy_parent_shopper"
                              - "persona_tech_savvy_millennial"
                            action_details:
                              - action_id: "action_open_app"
                                activity_id: "act_browse_mobile_catalog"
                                name: "Open Mobile App"
                                description: "Customer opens the mobile shopping application"
                                sequence: 1
                                category: "Customer Interaction"
                                priority: "medium"
                                estimated_effort: "30 seconds"
                                status: "active"
                              - action_id: "action_search_products"
                                activity_id: "act_browse_mobile_catalog"
                                name: "Search for Products"
                                description: "Customer searches for desired products"
                                sequence: 2
                                category: "Customer Interaction"
                                priority: "high"
                                estimated_effort: "2 minutes"
                                status: "active"
                        front_stage:
                          - activity_id: "act_assist_product_location"
                            name: "Assist with Product Location"
                            description: "Store associate helps customer find products"
                            activity_type: "front_stage"
                            moment_id: "moment_compare_prices_online"
                            sequence: 1
                            estimated_duration: "2-3 minutes"
                            complexity_level: "low"
                            personas:
                              - "persona_helpful_store_associate"
                            action_details:
                              - action_id: "action_greet_customer"
                                activity_id: "act_assist_product_location"
                                name: "Greet Customer"
                                description: "Store associate greets customer and offers assistance"
                                sequence: 1
                                category: "Customer Service"
                                priority: "high"
                                estimated_effort: "30 seconds"
                                status: "active"
                        back_stage: []
                        system:
                          - activity_id: "act_update_inventory_display"
                            name: "Update Inventory Display"
                            description: "System updates product availability in real-time"
                            activity_type: "system"
                            moment_id: "moment_compare_prices_online"
                            sequence: 1
                            estimated_duration: "5 seconds"
                            complexity_level: "low"
                            personas:
                              - "persona_inventory_system"
                            action_details:
                              - action_id: "action_sync_inventory"
                                activity_id: "act_update_inventory_display"
                                name: "Sync Inventory Data"
                                description: "Synchronize inventory levels across all systems"
                                sequence: 1
                                category: "System Process"
                                priority: "high"
                                estimated_effort: "automated"
                                status: "active"
                    - moment_id: "moment_add_items_to_cart"
                      name: "Add Items to Cart"
                      description: "Customer adds selected items to shopping cart"
                      phase_id: "phase_purchase_decision"
                      sequence: 1
                      activities:
                        customer:
                          - activity_id: "act_browse_mobile_catalog"
                            name: "Browse Product Catalog"
                            description: "Customer browses products using mobile app"
                            activity_type: "customer"
                            moment_id: "moment_compare_prices_online"
                            sequence: 1
                            estimated_duration: "3-5 minutes"
                            complexity_level: "medium"
                            personas:
                              - "persona_busy_parent_shopper"
                              - "persona_tech_savvy_millennial"
                            action_details:
                              - action_id: "action_open_app"
                                activity_id: "act_browse_mobile_catalog"
                                name: "Open Mobile App"
                                description: "Customer opens the mobile shopping application"
                                sequence: 1
                                category: "Customer Interaction"
                                priority: "medium"
                                estimated_effort: "30 seconds"
                                status: "active"
                              - action_id: "action_search_products"
                                activity_id: "act_browse_mobile_catalog"
                                name: "Search for Products"
                                description: "Customer searches for desired products"
                                sequence: 2
                                category: "Customer Interaction"
                                priority: "high"
                                estimated_effort: "2 minutes"
                                status: "active"
                        front_stage:
                          - activity_id: "act_assist_product_location"
                            name: "Assist with Product Location"
                            description: "Store associate helps customer find products"
                            activity_type: "front_stage"
                            moment_id: "moment_compare_prices_online"
                            sequence: 1
                            estimated_duration: "2-3 minutes"
                            complexity_level: "low"
                            personas:
                              - "persona_helpful_store_associate"
                            action_details:
                              - action_id: "action_greet_customer"
                                activity_id: "act_assist_product_location"
                                name: "Greet Customer"
                                description: "Store associate greets customer and offers assistance"
                                sequence: 1
                                category: "Customer Service"
                                priority: "high"
                                estimated_effort: "30 seconds"
                                status: "active"
                        back_stage: []
                        system:
                          - activity_id: "act_update_inventory_display"
                            name: "Update Inventory Display"
                            description: "System updates product availability in real-time"
                            activity_type: "system"
                            moment_id: "moment_compare_prices_online"
                            sequence: 1
                            estimated_duration: "5 seconds"
                            complexity_level: "low"
                            personas:
                              - "persona_inventory_system"
                            action_details:
                              - action_id: "action_sync_inventory"
                                activity_id: "act_update_inventory_display"
                                name: "Sync Inventory Data"
                                description: "Synchronize inventory levels across all systems"
                                sequence: 1
                                category: "System Process"
                                priority: "high"
                                estimated_effort: "automated"
                                status: "active"
                  personas:
                    - persona_id: "persona_busy_parent_shopper"
                      name: "Emily Rodriguez - Busy Working Parent"
                      persona_type: "customer"
                      age: 34
                      role: "Marketing Manager & Mother of Two"
                      location: "Austin, Texas"
                      motivations:
                        - "Save time on grocery shopping"
                        - "Provide healthy meals for family"
                      frustrations:
                        - "Long checkout lines"
                        - "Out-of-stock items"
                      goals:
                        - "Complete shopping in under 30 minutes"
                        - "Find all items on shopping list"
                    - persona_id: "persona_tech_savvy_millennial"
                      name: "Alex Chen - Tech-Savvy Millennial"
                      persona_type: "customer"
                      age: 28
                      role: "Software Developer"
                      location: "San Francisco, California"
                      motivations:
                        - "Quick and efficient shopping"
                        - "Mobile-first experience"
                      frustrations:
                        - "Slow checkout process"
                        - "Limited mobile features"
                    - persona_id: "persona_helpful_store_associate"
                      name: "Maria Santos - Store Associate"
                      persona_type: "front_stage"
                      age: 26
                      role: "Customer Service Representative"
                      location: "Austin, Texas"
                      motivations:
                        - "Help customers find products"
                        - "Provide excellent service"
                      frustrations:
                        - "Difficult-to-find products"
                        - "System downtime"
                    - persona_id: "persona_inventory_system"
                      name: "Oracle Inventory Management System"
                      persona_type: "system"
                      description: "Cloud-based inventory management system that tracks product availability"
                      vendor: "Oracle Retail"
                      platform: "Oracle Cloud Infrastructure"
                      availability: "99.9% uptime SLA"
                  components:
                    - component_id: "comp_mobile_app_003"
                      numbering: "APP-003"
                      name: "Mobile Shopping Application"
                      description: "Customer-facing mobile application for product browsing and purchasing"
                      best_practices: "Ensure responsive design and fast loading times"
                      level: "Critical"
                      framework: "customer"
                    - component_id: "comp_inventory_system_001"
                      numbering: "INV-001"
                      name: "Real-time Inventory System"
                      description: "Backend system for tracking product availability"
                      best_practices: "Maintain 99.9% uptime and sub-second response times"
                      level: "Essential"
                      framework: "business"
                    - component_id: "comp_price_comparison_002"
                      numbering: "PRICE-002"
                      name: "Price Comparison Engine"
                      description: "Backend service for comparing prices across different sources"
                      best_practices: "Update price data in real-time and cache frequently accessed comparisons"
                      level: "Essential"
                      framework: "customer"
                    - component_id: "comp_product_database_001"
                      numbering: "PROD-001"
                      name: "Product Information Database"
                      description: "Central repository for all product information and specifications"
                      best_practices: "Ensure data accuracy and maintain consistent product categorization"
                      level: "Critical"
                      framework: "business"
                    - component_id: "comp_shopping_cart_system"
                      numbering: "CART-001"
                      name: "Shopping Cart Management System"
                      description: "Handles cart operations, item management, and session persistence"
                      best_practices: "Implement cart persistence across sessions and devices"
                      level: "Critical"
                      framework: "customer"
                    - component_id: "comp_product_selector"
                      numbering: "SELECT-001"
                      name: "Product Selection Interface"
                      description: "UI component for product browsing, filtering, and selection"
                      best_practices: "Provide intuitive filtering and search capabilities"
                      level: "Essential"
                      framework: "customer"
                message: "Map details retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    put:
      tags: [Maps]
      summary: Update Map
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MapUpdate"
      responses:
        "200":
          description: Map updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Map"
              example:
                data:
                  map_id: "map_grocery_journey_v3"
                  organization_id: "acme-retail-group"
                  customer_goal:
                    customer_goal_id: "cg_complete_shopping_2024"
                    name: "Complete Weekly Grocery Shopping"
                    priority: "high"
                  name: "Enhanced In-Store Shopping Experience"
                  description: "Updated journey mapping for customers shopping in physical grocery stores with improved accessibility features"
                  division: "Retail Operations"
                  state: "published"
                  status: "active"
                  created_at: "2024-01-10T09:00:00Z"
                  updated_at: "2025-07-25T08:42:09Z"
                message: "Map updated successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    delete:
      tags: [Maps]
      summary: Delete Map
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Map deleted

  /maps/{mapId}/phases:
    get:
      tags: [Phases]
      summary: Get Map Phases
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Phases retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Phase"

    post:
      tags: [Phases]
      summary: Create Phase
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PhaseCreate"
      responses:
        "201":
          description: Phase created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Phase"
              example:
                data:
                  phase_id: "phase_discovery_001"
                  map_id: "map_mobile_journey_v1"
                  organization_id: "acme-retail-group"
                  name: "Discovery Phase"
                  description: "Customer discovers the need and explores options"
                  phase_order: 1
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Phase created successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

  /phases/{phaseId}:
    get:
      tags: [Phases]
      summary: Get Phase
      parameters:
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
            example: "phase_research_products"
      responses:
        "200":
          description: Phase details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PhaseDetails"
              example:
                data:
                  phase_id: "phase_discovery_001"
                  map_id: "map_mobile_journey_v1"
                  organization_id: "acme-retail-group"
                  name: "Discovery Phase"
                  description: "Customer discovers the need and explores options"
                  phase_order: 1
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  moment_count: 3
                message: "Phase retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    put:
      tags: [Phases]
      summary: Update Phase
      parameters:
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
            example: "phase_research_products"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PhaseUpdate"
      responses:
        "200":
          description: Phase updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Phase"
              example:
                data:
                  phase_id: "phase_discovery_001"
                  map_id: "map_mobile_journey_v1"
                  organization_id: "acme-retail-group"
                  name: "Enhanced Discovery Phase"
                  description: "Customer discovers the need and explores options with enhanced guidance"
                  phase_order: 1
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T09:15:22.123456Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  moment_count: 3
                message: "Phase updated successfully"
                timestamp: "2025-07-25T09:15:22.123456Z"
                status: "success"

    delete:
      tags: [Phases]
      summary: Delete Phase
      parameters:
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
            example: "phase_research_products"
      responses:
        "204":
          description: Phase deleted

  /moments:
    get:
      tags: [Moments]
      summary: Get Organization Moments
      responses:
        "200":
          description: Moments retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Moment"

  /moments/batch:
    post:
      tags: [Moments]
      summary: Get Multiple Moments by IDs
      description: Retrieve multiple moments by providing an array of moment IDs in the request body
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [moment_ids]
              properties:
                moment_ids:
                  type: array
                  items:
                    type: string
                  description: Array of moment IDs to retrieve
                  example:
                    [
                      "moment_compare_prices_online",
                      "moment_check_product_reviews",
                      "moment_add_items_to_cart",
                    ]
      responses:
        "200":
          description: Moments retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/MomentDetails"

  /maps/{mapId}/phases/{phaseId}/moments:
    get:
      tags: [Moments]
      summary: Get Phase Moments
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: string
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Moments retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Moment"

    post:
      tags: [Moments]
      summary: Create Moment
      parameters:
        - name: mapId
          in: path
          required: true
          schema:
            type: string
        - name: phaseId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MomentCreate"
      responses:
        "201":
          description: Moment created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Moment"
              example:
                data:
                  moment_id: "moment_browse_catalog_001"
                  phase_id: "phase_discovery_001"
                  organization_id: "acme-retail-group"
                  name: "Browse Product Catalog"
                  description: "Customer browses through available products in the catalog"
                  image_url: "https://example.com/images/browse-catalog.jpg"
                  components: ["comp_catalog_001", "comp_search_002"]
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Moment created successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

  /moments/{momentId}:
    get:
      tags: [Moments]
      summary: Get Moment
      parameters:
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      responses:
        "200":
          description: Moment details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MomentDetails"
              example:
                data:
                  moment_id: "moment_browse_catalog_001"
                  phase_id: "phase_discovery_001"
                  organization_id: "acme-retail-group"
                  name: "Browse Product Catalog"
                  description: "Customer browses through available products in the catalog"
                  image_url: "https://example.com/images/browse-catalog.jpg"
                  components: ["comp_catalog_001", "comp_search_002"]
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  activity_count: 4
                message: "Moment retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    put:
      tags: [Moments]
      summary: Update Moment
      parameters:
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/MomentUpdate"
      responses:
        "200":
          description: Moment updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Moment"
              example:
                data:
                  moment_id: "moment_browse_catalog_001"
                  phase_id: "phase_discovery_001"
                  organization_id: "acme-retail-group"
                  name: "Enhanced Product Catalog Browse"
                  description: "Customer browses through available products with enhanced filtering and search capabilities"
                  image_url: "https://example.com/images/enhanced-browse-catalog.jpg"
                  components: ["comp_enhanced_catalog_001", "comp_smart_search_002"]
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T09:15:22.123456Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Moment updated successfully"
                timestamp: "2025-07-25T09:15:22.123456Z"
                status: "success"

    delete:
      tags: [Moments]
      summary: Delete Moment
      parameters:
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      responses:
        "204":
          description: Moment deleted

  /moments/{momentId}/activities:
    get:
      tags: [Activities]
      summary: Get Moment Activities
      description: Retrieve all activities for a moment, grouped by activity type
      parameters:
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      responses:
        "200":
          description: Activities retrieved grouped by activity type
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActivitiesByType"
              example:
                data:
                  customer:
                    - activity_id: "act_browse_products_001"
                      name: "Browse Products"
                      description: "Customer searches through product catalog"
                      activity_type: "customer"
                      status: "active"
                  front_stage:
                    - activity_id: "act_assist_customer_002"
                      name: "Assist Customer"
                      description: "Sales staff helps customer find products"
                      activity_type: "front_stage"
                      status: "active"
                  back_stage:
                    - activity_id: "act_update_inventory_003"
                      name: "Update Inventory"
                      description: "Staff updates inventory levels"
                      activity_type: "back_stage"
                      status: "active"
                  system:
                    - activity_id: "act_track_behavior_004"
                      name: "Track User Behavior"
                      description: "System tracks browsing patterns"
                      activity_type: "system"
                      status: "active"
                message: "Activities retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    post:
      tags: [Activities]
      summary: Create Activity in Moment
      parameters:
        - name: momentId
          in: path
          required: true
          schema:
            type: string
            example: "moment_compare_prices_online"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ActivityCreate"
      responses:
        "201":
          description: Activity created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Activity"
              example:
                data:
                  activity_id: "act_browse_products_001"
                  moment_id: "moment_browse_catalog_001"
                  organization_id: "acme-retail-group"
                  name: "Browse Products"
                  description: "Customer searches through product catalog using filters and search"
                  activity_type: "customer"
                  components: ["comp_search_filter_001", "comp_product_grid_002"]
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Activity created successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

  /activities/{activityId}:
    get:
      tags: [Activities]
      summary: Get Activity
      parameters:
        - name: activityId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Activity details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActivityDetails"
              example:
                data:
                  activity_id: "act_browse_products_001"
                  moment_id: "moment_browse_catalog_001"
                  organization_id: "acme-retail-group"
                  name: "Browse Products"
                  description: "Customer searches through product catalog using filters and search"
                  activity_type: "customer"
                  components: ["comp_search_filter_001", "comp_product_grid_002"]
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  action_count: 5
                message: "Activity retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    put:
      tags: [Activities]
      summary: Update Activity
      parameters:
        - name: activityId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ActivityUpdate"
      responses:
        "200":
          description: Activity updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Activity"
              example:
                data:
                  activity_id: "act_browse_products_001"
                  moment_id: "moment_browse_catalog_001"
                  organization_id: "acme-retail-group"
                  name: "Enhanced Product Browsing"
                  description: "Customer searches through product catalog using advanced filters, AI recommendations, and enhanced search capabilities"
                  activity_type: "customer"
                  components:
                    [
                      "comp_ai_search_filter_001",
                      "comp_smart_product_grid_002",
                      "comp_recommendations_003",
                    ]
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T09:15:22.123456Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Activity updated successfully"
                timestamp: "2025-07-25T09:15:22.123456Z"
                status: "success"

    delete:
      tags: [Activities]
      summary: Delete Activity
      parameters:
        - name: activityId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Activity deleted

  /activities/{activityId}/actions:
    get:
      tags: [Actions]
      summary: Get Activity Actions
      description: Retrieve all actions within a specific activity
      parameters:
        - name: activityId
          in: path
          required: true
          schema:
            type: string
            example: "act_browse_product_catalog"
      responses:
        "200":
          description: Actions retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Action"
              example:
                data:
                  - action_id: "act_click_search_button_001"
                    activity_id: "act_browse_products_001"
                    organization_id: "acme-retail-group"
                    name: "Click Search Button"
                    description: "User clicks the search button to find products"
                    action_order: 1
                    status: "active"
                    created_at: "2025-07-25T08:42:09.262698Z"
                    updated_at: "2025-07-25T08:42:09.262698Z"
                  - action_id: "act_apply_filters_002"
                    activity_id: "act_browse_products_001"
                    organization_id: "acme-retail-group"
                    name: "Apply Product Filters"
                    description: "User applies category and price filters"
                    action_order: 2
                    status: "active"
                    created_at: "2025-07-25T08:42:09.262698Z"
                    updated_at: "2025-07-25T08:42:09.262698Z"
                message: "Actions retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    post:
      tags: [Actions]
      summary: Create Action in Activity
      description: Create a new action within a specific activity
      parameters:
        - name: activityId
          in: path
          required: true
          schema:
            type: string
            example: "act_browse_product_catalog"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ActionCreate"
      responses:
        "201":
          description: Action created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Action"
              example:
                data:
                  action_id: "act_click_search_button_001"
                  activity_id: "act_browse_products_001"
                  organization_id: "acme-retail-group"
                  name: "Click Search Button"
                  description: "User clicks the search button to find products matching their criteria"
                  action_order: 1
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Action created successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

  /actions/{actionId}:
    get:
      tags: [Actions]
      summary: Get Action
      parameters:
        - name: actionId
          in: path
          required: true
          schema:
            type: string
            example: "action_open_app"
      responses:
        "200":
          description: Action details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ActionDetails"
              example:
                data:
                  action_id: "act_click_search_button_001"
                  activity_id: "act_browse_products_001"
                  organization_id: "acme-retail-group"
                  name: "Click Search Button"
                  description: "User clicks the search button to find products matching their criteria"
                  action_order: 1
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Action retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    put:
      tags: [Actions]
      summary: Update Action
      parameters:
        - name: actionId
          in: path
          required: true
          schema:
            type: string
            example: "action_open_app"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ActionUpdate"
      responses:
        "200":
          description: Action updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Action"
              example:
                data:
                  action_id: "act_click_search_button_001"
                  activity_id: "act_browse_products_001"
                  organization_id: "acme-retail-group"
                  name: "Enhanced Search Button Click"
                  description: "User clicks the enhanced search button with AI-powered suggestions to find products matching their criteria"
                  action_order: 1
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T09:15:22.123456Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Action updated successfully"
                timestamp: "2025-07-25T09:15:22.123456Z"
                status: "success"

    delete:
      tags: [Actions]
      summary: Delete Action
      parameters:
        - name: actionId
          in: path
          required: true
          schema:
            type: string
            example: "action_open_app"
      responses:
        "204":
          description: Action deleted

  /personas:
    get:
      tags: [Personas]
      summary: Get Organization Personas
      responses:
        "200":
          description: Personas retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Persona"
              example:
                data:
                  - persona_id: "persona_busy_parent_001"
                    organization_id: "acme-retail-group"
                    name: "Emily Rodriguez"
                    persona_type: "customer"
                    tagline: "Efficient shopping for a busy family"
                    age: 34
                    role: "Marketing Manager & Mother of Two"
                    status: "active"
                    created_at: "2025-07-25T08:42:09.262698Z"
                  - persona_id: "persona_tech_enthusiast_002"
                    organization_id: "acme-retail-group"
                    name: "Alex Chen"
                    persona_type: "customer"
                    tagline: "Early adopter seeking cutting-edge solutions"
                    age: 28
                    role: "Software Developer"
                    status: "active"
                    created_at: "2025-07-25T08:42:09.262698Z"
                message: "Personas retrieved successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

    post:
      tags: [Personas]
      summary: Create Persona
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PersonaCreate"
      responses:
        "201":
          description: Persona created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Persona"
              example:
                data:
                  persona_id: "persona_busy_parent_001"
                  organization_id: "acme-retail-group"
                  name: "Emily Rodriguez"
                  persona_type: "customer"
                  tagline: "Efficient shopping for a busy family"
                  age: 34
                  role: "Marketing Manager & Mother of Two"
                  income: 75000
                  location: "Sydney, Australia"
                  avatar_url: "https://cdn.acmeretail.com/personas/emily_rodriguez.jpg"
                  profile:
                    goals: ["Save time while shopping", "Find quality products for family"]
                    pain_points: ["Too many choices", "Limited time", "Comparing prices"]
                    preferred_channels: ["Mobile app", "Online website"]
                    tech_comfort: "high"
                  status: "active"
                  created_at: "2025-07-25T08:42:09.262698Z"
                  updated_at: "2025-07-25T08:42:09.262698Z"
                  created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                  updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                message: "Persona created successfully"
                timestamp: "2025-07-25T08:42:09.262698Z"
                status: "success"

  /personas/type/{personaType}:
    get:
      tags: [Personas]
      summary: Get Personas by Type
      parameters:
        - name: personaType
          in: path
          required: true
          schema:
            type: string
            enum: [customer, front_stage, back_stage, system]
          description: The type of personas to retrieve
      responses:
        "200":
          description: Personas of specified type retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Persona"

  /personas/{personaId}:
    get:
      tags: [Personas]
      summary: Get Persona
      parameters:
        - name: personaId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Persona details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PersonaDetails"
              examples:
                personaDetails:
                  summary: "Customer service representative persona details"
                  value:
                    data:
                      persona_id: "persona_csr_001"
                      organization_id: "acme-retail-group"
                      persona_type: "customer_service"
                      name: "Sarah - Customer Service Rep"
                      description: "Experienced customer service representative who handles complex inquiries"
                      demographics:
                        age_range: "25-35"
                        location: "Sydney, Australia"
                        experience_level: "experienced"
                      characteristics:
                        - "Patient and empathetic"
                        - "Detail-oriented"
                        - "Solution-focused"
                      goals:
                        - "Resolve customer issues quickly"
                        - "Maintain high satisfaction scores"
                      pain_points:
                        - "Complex system navigation"
                        - "Incomplete customer history"
                      behavioral_patterns:
                        - "Prefers step-by-step guidance"
                        - "Uses multiple tabs for reference"
                      status: "active"
                      created_at: "2025-07-25T08:42:09.262698Z"
                      updated_at: "2025-07-25T08:42:09.262698Z"
                      created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                      updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                    message: "Persona retrieved successfully"
                    timestamp: "2025-07-25T08:42:09.262698Z"
                    status: "success"

    put:
      tags: [Personas]
      summary: Update Persona
      parameters:
        - name: personaId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PersonaUpdate"
      responses:
        "200":
          description: Persona updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Persona"
              examples:
                updatedPersona:
                  summary: "Updated customer persona"
                  value:
                    data:
                      persona_id: "persona_csr_001"
                      organization_id: "acme-retail-group"
                      persona_type: "customer_service"
                      name: "Sarah - Senior Customer Service Rep"
                      description: "Senior customer service representative with expertise in complex technical issues"
                      demographics:
                        age_range: "25-35"
                        location: "Sydney, Australia"
                        experience_level: "expert"
                      characteristics:
                        - "Patient and empathetic"
                        - "Detail-oriented"
                        - "Solution-focused"
                        - "Technical problem solver"
                      goals:
                        - "Resolve customer issues quickly"
                        - "Maintain high satisfaction scores"
                        - "Mentor junior staff"
                      pain_points:
                        - "Complex system navigation"
                        - "Incomplete customer history"
                      behavioral_patterns:
                        - "Prefers step-by-step guidance"
                        - "Uses multiple tabs for reference"
                        - "Documents solutions for team"
                      status: "active"
                      created_at: "2025-07-25T08:42:09.262698Z"
                      updated_at: "2025-07-25T08:52:15.445321Z"
                      created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                      updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                    message: "Persona updated successfully"
                    timestamp: "2025-07-25T08:52:15.445321Z"
                    status: "success"

    delete:
      tags: [Personas]
      summary: Delete Persona
      parameters:
        - name: personaId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Persona deleted

  /components:
    get:
      tags: [Components]
      summary: Get Organization Components
      responses:
        "200":
          description: Components retrieved
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/Component"
              examples:
                componentsList:
                  summary: "Organization components list"
                  value:
                    data:
                      - component_id: "comp_checkout_flow_001"
                        organization_id: "acme-retail-group"
                        name: "Mobile Checkout Flow"
                        description: "Complete mobile application checkout process including payment verification"
                        category: "E-commerce Process"
                        component_type: "process"
                        usage_count: 23
                        last_used: "2025-07-20T14:30:00Z"
                        status: "active"
                        created_at: "2025-07-25T08:42:09.262698Z"
                        updated_at: "2025-07-25T08:42:09.262698Z"
                      - component_id: "comp_payment_gateway_002"
                        organization_id: "acme-retail-group"
                        name: "Payment Gateway Integration"
                        description: "Secure payment processing integration with multiple providers"
                        category: "Technology Integration"
                        component_type: "integration"
                        usage_count: 47
                        last_used: "2025-07-22T09:15:00Z"
                        status: "active"
                        created_at: "2025-07-25T08:42:09.262698Z"
                        updated_at: "2025-07-25T08:42:09.262698Z"
                    message: "Components retrieved successfully"
                    timestamp: "2025-07-25T08:42:09.262698Z"
                    status: "success"

    post:
      tags: [Components]
      summary: Create Component
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ComponentCreate"
      responses:
        "201":
          description: Component created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Component"
              examples:
                newComponent:
                  summary: "Newly created component"
                  value:
                    data:
                      component_id: "comp_customer_support_chat_003"
                      organization_id: "acme-retail-group"
                      name: "Customer Support Live Chat"
                      description: "Real-time customer support chat system with agent routing and escalation"
                      category: "Customer Service"
                      component_type: "service"
                      usage_count: 0
                      last_used: null
                      status: "active"
                      created_at: "2025-07-25T08:42:09.262698Z"
                      updated_at: "2025-07-25T08:42:09.262698Z"
                      created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                      updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                    message: "Component created successfully"
                    timestamp: "2025-07-25T08:42:09.262698Z"
                    status: "success"

  /components/{componentId}:
    get:
      tags: [Components]
      summary: Get Component
      parameters:
        - name: componentId
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: Component details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ComponentDetails"
              examples:
                componentDetails:
                  summary: "Component details"
                  value:
                    data:
                      component_id: "comp_checkout_flow_001"
                      organization_id: "acme-retail-group"
                      name: "Mobile Checkout Flow"
                      description: "Complete mobile application checkout process including payment verification and order confirmation"
                      category: "E-commerce Process"
                      component_type: "process"
                      usage_count: 23
                      last_used: "2025-07-20T14:30:00Z"
                      impact_score: 8.7
                      efficiency_rating: 9.2
                      status: "active"
                      created_at: "2025-07-25T08:42:09.262698Z"
                      updated_at: "2025-07-25T08:42:09.262698Z"
                      created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                      updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                    message: "Component retrieved successfully"
                    timestamp: "2025-07-25T08:42:09.262698Z"
                    status: "success"

    put:
      tags: [Components]
      summary: Update Component
      parameters:
        - name: componentId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ComponentUpdate"
      responses:
        "200":
          description: Component updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Component"
              examples:
                updatedComponent:
                  summary: "Updated component"
                  value:
                    data:
                      component_id: "comp_checkout_flow_001"
                      organization_id: "acme-retail-group"
                      name: "Enhanced Mobile Checkout Flow"
                      description: "Enhanced mobile application checkout process with improved payment verification and faster order confirmation"
                      category: "E-commerce Process"
                      component_type: "process"
                      usage_count: 25
                      last_used: "2025-07-25T10:15:00Z"
                      status: "active"
                      created_at: "2025-07-25T08:42:09.262698Z"
                      updated_at: "2025-07-25T10:25:33.771234Z"
                      created_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                      updated_by: "59ee54e8-0071-7071-c56c-beec590dc95f"
                    message: "Component updated successfully"
                    timestamp: "2025-07-25T10:25:33.771234Z"
                    status: "success"

    delete:
      tags: [Components]
      summary: Delete Component
      parameters:
        - name: componentId
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: Component deleted

  /components/match:
    post:
      tags: [Components]
      summary: Find Matching Components
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [text]
              properties:
                text:
                  type: string
                  example: "Customer completing mobile checkout and payment verification"
                threshold:
                  type: number
                  format: float
                  minimum: 0
                  maximum: 1
                  default: 0.7
      responses:
        "200":
          description: Component matches found
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ComponentMatch"

  /components/upload:
    post:
      tags: [Components]
      summary: Bulk Upload Components from CSV
      description: |
        Upload components in bulk from a CSV file. The CSV file must contain the following columns:
        - numbering (required): Component numbering identifier
        - name (required): Component name
        - description (required): Component description
        - best_practices (optional): Best practices text
        - level (optional): Level designation
        - status (optional): Component status (defaults to 'active')

        Example CSV format:
        ```
        numbering,name,description,best_practices,level,status
        C001,User Login,Customer authentication process,Use secure protocols,Basic,active
        C002,Payment Processing,Handle payment transactions,Validate all inputs,Advanced,active
        ```
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [csv_file_location]
              properties:
                csv_file_location:
                  type: string
                  format: uri
                  description: URL or file path to the CSV file containing component data
                  example: "s3://acme-retail-data/components/payment_components_2024.csv"
                overwrite_existing:
                  type: boolean
                  default: false
                  description: Whether to overwrite existing components with same numbering
                validate_only:
                  type: boolean
                  default: false
                  description: If true, only validate the CSV without creating components
      responses:
        "200":
          description: Components uploaded successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ComponentUploadResult"
              examples:
                uploadSuccess:
                  summary: "Successful component upload"
                  value:
                    data:
                      total_rows_processed: 15
                      components_created: 13
                      components_skipped: 2
                      validation_errors: []
                      created_component_ids:
                        - "comp_mobile_login_001"
                        - "comp_product_search_002"
                        - "comp_checkout_flow_003"
                        - "comp_payment_gateway_004"
                        - "comp_order_confirmation_005"
                        - "comp_customer_support_006"
                        - "comp_inventory_check_007"
                        - "comp_shipping_calc_008"
                        - "comp_tax_calculation_009"
                        - "comp_discount_engine_010"
                        - "comp_loyalty_points_011"
                        - "comp_email_notification_012"
                        - "comp_sms_alerts_013"
                      processing_time_ms: 1847
                    message: "Components uploaded successfully. 13 components created, 2 skipped due to duplicates."
                    timestamp: "2025-07-25T08:42:09.262698Z"
                    status: "success"
        "400":
          description: Invalid CSV format or data
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/Error"
                  - type: object
                    properties:
                      validation_errors:
                        type: array
                        items:
                          $ref: "#/components/schemas/ComponentValidationError"

  /ai/generate-text:
    post:
      tags: [AI Services]
      summary: Generate AI Text Content
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [content_type, context]
              properties:
                content_type:
                  type: string
                  enum:
                    [
                      customer_goal_description,
                      map_description,
                      phase_description,
                      moment_description,
                      activity_description,
                      action_description,
                      persona_description,
                      persona_goals,
                      persona_frustrations,
                    ]
                context:
                  type: object
                  additionalProperties: true
                tone:
                  type: string
                  enum: [professional, casual, technical, friendly]
                  default: professional
                length:
                  type: string
                  enum: [short, medium, long]
                  default: medium
      responses:
        "200":
          description: AI text generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  generated_text:
                    type: string
                    example: "Customers often feel frustrated when they can't find specific products in the store, especially during busy shopping periods. This moment requires clear signage and helpful staff assistance to guide customers efficiently."
                  metadata:
                    type: object
                    properties:
                      model_used:
                        type: string
                        example: "gpt-4-turbo"
                      token_count:
                        type: integer
                        example: 156
              examples:
                textGeneration:
                  summary: "AI generated moment description"
                  value:
                    data:
                      generated_text: "During the product discovery phase, customers often feel overwhelmed by the vast array of options available. They experience excitement about finding something new but also anxiety about making the right choice. This moment is critical as it sets the tone for the entire shopping experience and requires clear navigation, personalized recommendations, and easy comparison tools to help customers feel confident in their decisions."
                      metadata:
                        model_used: "gpt-4-turbo"
                        token_count: 243
                        generation_time_ms: 1247
                        prompt_tokens: 89
                        completion_tokens: 154
                    message: "AI text content generated successfully"
                    timestamp: "2025-07-25T08:42:09.262698Z"
                    status: "success"

  /ai/generate-image:
    post:
      tags: [AI Services]
      summary: Generate AI Images
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [image_type, parameters]
              properties:
                image_type:
                  type: string
                  enum: [persona_avatar, map_illustration, phase_icon]
                parameters:
                  type: object
                  additionalProperties: true
                style:
                  type: string
                  enum: [professional, modern, minimalist, colorful]
                  default: professional
      responses:
        "200":
          description: AI image generated
          content:
            application/json:
              schema:
                type: object
                properties:
                  image_url:
                    type: string
                    format: uri
                    example: "https://cdn.acmeretail.com/generated/persona_busy_parent_avatar_2024.png"
                  thumbnail_url:
                    type: string
                    format: uri
                    example: "https://cdn.acmeretail.com/generated/thumbs/persona_busy_parent_avatar_2024_thumb.png"
              examples:
                imageGeneration:
                  summary: "AI generated persona avatar"
                  value:
                    data:
                      image_url: "https://cdn.acmeretail.com/generated/persona_tech_savvy_millennial_avatar_2025.png"
                      thumbnail_url: "https://cdn.acmeretail.com/generated/thumbs/persona_tech_savvy_millennial_avatar_2025_thumb.png"
                      metadata:
                        image_type: "persona_avatar"
                        style: "modern"
                        resolution: "512x512"
                        format: "PNG"
                        generation_time_ms: 3247
                        model_version: "dall-e-3"
                    message: "AI image generated successfully"
                    timestamp: "2025-07-25T08:42:09.262698Z"
                    status: "success"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        AWS Cognito IdToken (not AccessToken) for authentication.

        **Getting a Token:**
        1. Sign up/sign in via AWS Cognito hosted UI or SDK
        2. Use the IdToken from the authentication response
        3. Include in requests: `Authorization: Bearer <IdToken>`

        **Token Claims:**
        - `sub`: User ID (matches user_id in database)
        - `email`: User email address
        - `cognito:username`: Cognito username
        - Organization context is determined from database lookup

        **First-time Users:**
        - Authenticate with Cognito to get token
        - Call POST /organization to complete onboarding
        - User is automatically created with admin role

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    Forbidden:
      description: Insufficient permissions
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"

  schemas:
    # Authentication schemas (Cognito)
    CognitoAuthRequest:
      type: object
      required:
        - ClientId
        - AuthFlow
        - AuthParameters
      properties:
        ClientId:
          type: string
          example: "4f7v5jru6aajsm4gtluvtqdai7"
          description: The app client ID
        AuthFlow:
          type: string
          example: "USER_PASSWORD_AUTH"
          description: The authentication flow
        AuthParameters:
          type: object
          required:
            - USERNAME
            - PASSWORD
          properties:
            USERNAME:
              type: string
              format: email
              example: "<EMAIL>"
              description: User's email/username
            PASSWORD:
              type: string
              format: password
              example: "Password123!@#"
              description: User's password

    CognitoAuthResponse:
      type: object
      properties:
        AuthenticationResult:
          type: object
          properties:
            AccessToken:
              type: string
              description: Access token
            ExpiresIn:
              type: integer
              description: Token expiration time in seconds
              example: 3600
            IdToken:
              type: string
              description: JWT identity token (use this for API authentication)
              example: "eyJraWQiOiJLTzRVMWZs..."
            RefreshToken:
              type: string
              description: Refresh token
            TokenType:
              type: string
              example: "Bearer"

    # Main API schemas
    Organization:
      type: object
      properties:
        organization_id:
          type: string
          example: "acme-financial-2024"
        name:
          type: string
          example: "Acme Financial Services"
        industry:
          type: string
          example: "Financial Services"
        subscription_tier:
          type: string
          enum: [starter, professional, enterprise]
        settings:
          type: object
          properties:
            timezone:
              type: string
              example: "America/New_York"
            locale:
              type: string
              example: "en-US"
            data_retention_days:
              type: integer
              example: 2555
        status:
          type: string
          enum: [active, suspended, archived]
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    OrganizationDetails:
      allOf:
        - $ref: "#/components/schemas/Organization"
        - type: object
          properties:
            business_structure:
              type: object
              properties:
                divisions:
                  type: array
                  items:
                    type: string
                channels:
                  type: array
                  items:
                    type: string
                products:
                  type: array
                  items:
                    type: string
                brands:
                  type: array
                  items:
                    type: string
            statistics:
              type: object
              properties:
                total_users:
                  type: integer
                total_maps:
                  type: integer
                total_personas:
                  type: integer

    OrganizationCreate:
      type: object
      required: [name, industry]
      properties:
        name:
          type: string
          example: "Global Tech Solutions Inc."
        industry:
          type: string
          example: "Technology Services"
        subscription_tier:
          type: string
          enum: [starter, professional, enterprise]
          default: starter
          example: "professional"
        settings:
          type: object
          properties:
            timezone:
              type: string
              example: "America/Los_Angeles"
            locale:
              type: string
              example: "en-US"

    OrganizationUpdate:
      type: object
      properties:
        name:
          type: string
        industry:
          type: string
        settings:
          type: object
          properties:
            timezone:
              type: string
            locale:
              type: string
        business_structure:
          type: object
          properties:
            divisions:
              type: array
              items:
                type: string
            channels:
              type: array
              items:
                type: string

    UserProfile:
      type: object
      properties:
        user_id:
          type: string
          example: "usr_sarah_johnson_001"
        organization_id:
          type: string
          example: "acme-financial-2024"
        profile:
          type: object
          properties:
            email:
              type: string
              format: email
              example: "<EMAIL>"
            first_name:
              type: string
              example: "Sarah"
            last_name:
              type: string
              example: "Johnson"
            display_name:
              type: string
              example: "Sarah J."
            avatar_url:
              type: string
              format: uri
              example: "https://cdn.acmefinancial.com/avatars/sarah_johnson.jpg"
        access_control:
          type: object
          properties:
            role:
              type: string
              enum: [admin, writer, reader]
            department:
              type: string
            permissions:
              type: array
              items:
                type: string

    User:
      type: object
      properties:
        user_id:
          type: string
          example: "usr_mike_chen_005"
        organization_id:
          type: string
          example: "acme-financial-2024"
        profile:
          type: object
          properties:
            email:
              type: string
              format: email
            first_name:
              type: string
            last_name:
              type: string
            display_name:
              type: string
            avatar_url:
              type: string
              format: uri
        access_control:
          type: object
          properties:
            role:
              type: string
              enum: [admin, writer, reader]
            department:
              type: string
            permissions:
              type: array
              items:
                type: string
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    UserDetails:
      allOf:
        - $ref: "#/components/schemas/User"

    UserCreate:
      type: object
      required: [email, first_name, last_name, role]
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        first_name:
          type: string
          example: "Michael"
        last_name:
          type: string
          example: "Chen"
        display_name:
          type: string
          example: "Mike C."
        role:
          type: string
          enum: [admin, writer, reader]
          example: "writer"
        department:
          type: string
          example: "Product Management"
        permissions:
          type: array
          items:
            type: string
          example: ["create_maps", "edit_personas", "view_reports"]

    UserUpdate:
      type: object
      properties:
        profile:
          type: object
          properties:
            first_name:
              type: string
            last_name:
              type: string
            display_name:
              type: string
            avatar_url:
              type: string
              format: uri
        access_control:
          type: object
          properties:
            role:
              type: string
              enum: [admin, writer, reader]
            department:
              type: string
            permissions:
              type: array
              items:
                type: string

    CustomerGoal:
      type: object
      properties:
        customer_goal_id:
          type: string
          example: "cg_complete_shopping_2024"
        organization_id:
          type: string
          example: "acme-retail-group"
        name:
          type: string
          example: "Complete Weekly Grocery Shopping"
        description:
          type: string
          example: "Successfully purchase all needed groceries for the week with minimal time and effort"
        category:
          type: string
          example: "Shopping & Commerce"
        priority:
          type: string
          enum: [high, medium, low]
          example: "high"
        status:
          type: string
          enum: [active, draft, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    CustomerGoalDetails:
      allOf:
        - $ref: "#/components/schemas/CustomerGoal"
        - type: object
          properties:
            maps:
              type: array
              items:
                type: object
                properties:
                  map_id:
                    type: string
                  name:
                    type: string
                  state:
                    type: string

    CustomerGoalCreate:
      type: object
      required: [name, description]
      properties:
        name:
          type: string
          example: "Complete Online Grocery Ordering"
        description:
          type: string
          example: "Customer successfully places and receives an online grocery order with all desired items"
        category:
          type: string
          example: "E-commerce & Delivery"
        priority:
          type: string
          enum: [high, medium, low]
          default: medium
          example: "high"

    CustomerGoalUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        category:
          type: string
        priority:
          type: string
          enum: [high, medium, low]
        status:
          type: string
          enum: [active, draft, archived]

    Map:
      type: object
      properties:
        map_id:
          type: string
          example: "map_grocery_journey_v3"
        organization_id:
          type: string
          example: "acme-retail-group"
        customer_goal:
          $ref: "#/components/schemas/CustomerGoal"
        name:
          type: string
          example: "In-Store Shopping Experience"
        description:
          type: string
          example: "Complete journey mapping for customers shopping in physical grocery stores"
        division:
          type: string
          example: "Retail Operations"
        state:
          type: string
          enum: [draft, review, published, archived]
          example: "published"
        status:
          type: string
          enum: [active, inactive]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    MapDetails:
      allOf:
        - $ref: "#/components/schemas/Map"
        - type: object
          properties:
            phases:
              type: array
              items:
                allOf:
                  - $ref: "#/components/schemas/Phase"
                  - type: object
                    properties:
                      moment_ids:
                        type: array
                        items:
                          type: string
                        description: "Array of moment IDs that belong to this phase"
                        example: ["moment_compare_prices_online", "moment_check_product_reviews"]
              example:
                - phase_id: "phase_research_products"
                  name: "Product Research & Planning"
                  description: "Customer researches products and plans their shopping trip"
                  sequence: 1
                  moment_ids: ["moment_compare_prices_online", "moment_check_product_reviews"]
                - phase_id: "phase_purchase_decision"
                  name: "Purchase Decision"
                  description: "Customer decides what to buy and adds to cart"
                  sequence: 2
                  moment_ids: ["moment_add_items_to_cart"]
            moments:
              type: array
              items:
                $ref: "#/components/schemas/MomentDetails"
              description: "All moments in the map (deduplicated - shared moments appear only once)"
              example:
                - moment_id: "moment_compare_prices_online"
                  name: "Compare Prices Online"
                  description: "Customer compares prices across different platforms"
                  phase_id: "phase_research_products"
                  sequence: 1
                - moment_id: "moment_check_product_reviews"
                  name: "Check Product Reviews"
                  description: "Customer reads reviews and ratings"
                  phase_id: "phase_research_products"
                  sequence: 2
                - moment_id: "moment_add_items_to_cart"
                  name: "Add Items to Cart"
                  description: "Customer adds selected items to shopping cart"
                  phase_id: "phase_purchase_decision"
                  sequence: 1
            personas:
              type: array
              items:
                $ref: "#/components/schemas/PersonaDetails"
              description: "All personas referenced in the map (deduplicated)"
              example:
                - persona_id: "persona_busy_parent_shopper"
                  name: "Emily Rodriguez - Busy Working Parent"
                  persona_type: "customer"
                  age: 34
                  role: "Marketing Manager & Mother of Two"
                  motivations: ["Save time on grocery shopping", "Provide healthy meals for family"]
                  frustrations: ["Long checkout lines", "Out-of-stock items"]
                - persona_id: "persona_tech_savvy_millennial"
                  name: "Alex Chen - Tech-Savvy Millennial"
                  persona_type: "customer"
                  age: 28
                  role: "Software Developer"
                  motivations: ["Quick and efficient shopping", "Mobile-first experience"]
                  frustrations: ["Slow checkout process", "Limited mobile features"]
            components:
              type: array
              items:
                $ref: "#/components/schemas/ComponentDetails"
              description: "All components referenced in the map (deduplicated)"
              example:
                - component_id: "comp_mobile_app_003"
                  numbering: "APP-003"
                  name: "Mobile Shopping Application"
                  description: "Customer-facing mobile application for product browsing and purchasing"
                  best_practices: "Ensure responsive design and fast loading times"
                  level: "Critical"
                - component_id: "comp_price_comparison_002"
                  numbering: "PRICE-002"
                  name: "Price Comparison Engine"
                  description: "Backend service for comparing prices across different sources"
                  best_practices: "Update price data in real-time and cache frequently accessed comparisons"
                  level: "Essential"
            statistics:
              type: object
              properties:
                total_phases:
                  type: integer
                total_moments:
                  type: integer
                total_actions:
                  type: integer
                total_personas:
                  type: integer
                total_components:
                  type: integer

    MapCreate:
      type: object
      required: [name, description, customer_goal_id, division]
      properties:
        name:
          type: string
          example: "Mobile App Shopping Journey"
        description:
          type: string
          example: "End-to-end customer journey for mobile app grocery shopping experience"
        customer_goal_id:
          type: string
          example: "cg_complete_shopping_2024"
        division:
          type: string
          example: "Digital Commerce"
        state:
          type: string
          enum: [draft, review, published]
          default: draft
          example: "draft"

    MapUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        division:
          type: string
        state:
          type: string
          enum: [draft, review, published, archived]
        status:
          type: string
          enum: [active, inactive]

    Phase:
      type: object
      properties:
        phase_id:
          type: string
          example: "phase_research_products"
        organization_id:
          type: string
          example: "acme-retail-group"
        map_id:
          type: string
          example: "map_grocery_journey_v3"
        name:
          type: string
          example: "Product Research & Planning"
        description:
          type: string
          example: "Customer researches products and plans their shopping trip"
        sequence:
          type: integer
          example: 1
        components:
          type: array
          items:
            type: string
          example: ["comp_meal_planning_001", "comp_price_comparison_002"]
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    PhaseDetails:
      allOf:
        - $ref: "#/components/schemas/Phase"
        - type: object
          properties:
            moment_ids:
              type: array
              items:
                type: string
              description: "Array of moment IDs that belong to this phase"
              example: ["moment_compare_prices_online", "moment_check_product_reviews"]

    PhaseCreate:
      type: object
      required: [name, description]
      properties:
        name:
          type: string
          example: "Pre-Shopping Preparation"
        description:
          type: string
          example: "Customer prepares for shopping by creating lists and checking for deals"
        sequence:
          type: integer
          minimum: 1
          example: 1
        components:
          type: array
          items:
            type: string
          example: ["comp_shopping_list_creation", "comp_coupon_discovery"]

    PhaseUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        sequence:
          type: integer
        components:
          type: array
          items:
            type: string
        status:
          type: string
          enum: [active, inactive, archived]

    Moment:
      type: object
      properties:
        moment_id:
          type: string
          example: "moment_compare_prices_online"
        organization_id:
          type: string
          example: "acme-retail-group"
        name:
          type: string
          example: "Compare Prices Online"
        description:
          type: string
          example: "Customer compares prices across different platforms"
        image_url:
          type: string
          format: uri
          example: "https://cdn.acmeretail.com/moments/compare_prices_online.jpg"
        components:
          type: array
          items:
            type: string
          example: ["comp_price_comparison_001", "comp_search_filter_002"]
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    MomentDetails:
      allOf:
        - $ref: "#/components/schemas/Moment"
        - type: object
          properties:
            activities:
              $ref: "#/components/schemas/ActivitiesByType"
              description: "Activities grouped by activity type with full details including actions (customer, front_stage, back_stage, system)"

    MomentCreate:
      type: object
      required: [name, description]
      properties:
        name:
          type: string
          example: "Check Product Availability"
        description:
          type: string
          example: "Customer checks if desired products are available in-store or online"
        image_url:
          type: string
          format: uri
          example: "https://cdn.acmeretail.com/moments/check_product_availability.jpg"
        components:
          type: array
          items:
            type: string
          example: ["comp_inventory_check", "comp_store_locator"]

    MomentUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        image_url:
          type: string
          format: uri
        components:
          type: array
          items:
            type: string
        status:
          type: string
          enum: [active, inactive, archived]

    Activity:
      type: object
      properties:
        activity_id:
          type: string
          example: "act_browse_product_catalog"
        organization_id:
          type: string
          example: "acme-retail-group"
        moment_id:
          type: string
          example: "moment_compare_prices_online"
        name:
          type: string
          example: "Browse Product Catalog"
        description:
          type: string
          example: "Customer browses through available products using the mobile app"
        activity_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
          example: "customer"
          description: "The type of activity for grouping purposes"
        estimated_duration:
          type: string
          example: "3-5 minutes"
        complexity_level:
          type: string
          enum: [low, medium, high]
          example: "medium"
        personas:
          type: array
          items:
            type: string
          description: Array of persona IDs associated with this activity
          example: ["persona_busy_parent_shopper", "persona_tech_savvy_millennial"]
        sequence:
          type: integer
          minimum: 1
          example: 1
          description: "Order of this activity within its activity type"
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    ActivityDetails:
      allOf:
        - $ref: "#/components/schemas/Activity"
        - type: object
          properties:
            action_details:
              type: array
              items:
                $ref: "#/components/schemas/Action"
              description: "Full action objects with all details (expands the action IDs from the base activity)"
              example:
                - action_id: "action_open_app"
                  activity_id: "act_browse_product_catalog"
                  name: "Open Mobile App"
                  description: "Customer opens the mobile shopping application"
                  sequence: 1
                  category: "Customer Interaction"
                  priority: "medium"
                  estimated_effort: "30 seconds"
                  status: "active"
                - action_id: "action_search_products"
                  activity_id: "act_browse_product_catalog"
                  name: "Search for Products"
                  description: "Customer searches for desired products"
                  sequence: 2
                  category: "Customer Interaction"
                  priority: "high"
                  estimated_effort: "2 minutes"
                  status: "active"

    ActivityCreate:
      type: object
      required: [name, description, activity_type]
      properties:
        name:
          type: string
          example: "Scan Product Barcode"
        description:
          type: string
          example: "Customer uses mobile app to scan product barcode for information"
        activity_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
          example: "customer"
        estimated_duration:
          type: string
          example: "30 seconds"
        complexity_level:
          type: string
          enum: [low, medium, high]
          default: medium
          example: "low"
        personas:
          type: array
          items:
            type: string
          description: Array of persona IDs associated with this activity
          example: ["persona_busy_parent_shopper", "persona_tech_savvy_millennial"]
        sequence:
          type: integer
          minimum: 1
          example: 1
          description: "Order of this activity within its activity type"

    ActivityUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        activity_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
        estimated_duration:
          type: string
        complexity_level:
          type: string
          enum: [low, medium, high]
        personas:
          type: array
          items:
            type: string
          description: Array of persona IDs associated with this activity
        sequence:
          type: integer
          minimum: 1
          description: "Order of this activity within its activity type"
        status:
          type: string
          enum: [active, inactive, archived]

    Action:
      type: object
      properties:
        action_id:
          type: string
          example: "action_add_to_cart_flow"
        organization_id:
          type: string
          example: "acme-retail-group"
        activity_id:
          type: string
          example: "act_browse_product_catalog"
          description: "ID of the activity this action belongs to"
        name:
          type: string
          example: "Add Item to Shopping Cart"
        description:
          type: string
          example: "Customer adds selected product to their digital shopping cart"
        sequence:
          type: integer
          minimum: 1
          example: 1
          description: "Order of this action within the activity"
        category:
          type: string
          example: "E-commerce Interaction"
        link:
          type: string
          format: uri
          example: "https://acmeretail.atlassian.net/browse/CART-123"
        supported_by:
          type: string
          example: "Development Team Alpha"
        reference_screen:
          type: string
          example: "Shopping Cart Interface v2.1"
        delivery_epic:
          type: string
          example: "E-commerce Enhancement Q1 2024"
        additional_document_references:
          type: string
          example: "https://acmeretail.confluence.com/design-doc-123"
        priority:
          type: string
          enum: [high, medium, low]
          example: "high"
        estimated_effort:
          type: string
          example: "2 story points"
        usage_count:
          type: integer
          example: 47
        last_used:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        impact_score:
          type: number
          format: float
          example: 8.5
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    ActionDetails:
      allOf:
        - $ref: "#/components/schemas/Action"

    ActionCreate:
      type: object
      required: [name, description, sequence, category]
      properties:
        name:
          type: string
          example: "Send Push Notification for Deal"
        description:
          type: string
          example: "System sends personalized push notification to customer about relevant deals"
        sequence:
          type: integer
          minimum: 1
          example: 1
          description: "Order of this action within the activity"
        category:
          type: string
          example: "Marketing & Notifications"
        link:
          type: string
          format: uri
          example: "https://acmeretail.atlassian.net/browse/NOTIF-456"
        supported_by:
          type: string
          example: "Mobile Development Team"
        reference_screen:
          type: string
          example: "Push Notification Settings Screen"
        delivery_epic:
          type: string
          example: "Customer Engagement Platform Q2 2024"
        additional_document_references:
          type: string
          example: "https://acmeretail.confluence.com/notification-strategy"
        priority:
          type: string
          enum: [high, medium, low]
          default: medium
          example: "medium"
        estimated_effort:
          type: string
          example: "1 story point"

    ActionUpdate:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        sequence:
          type: integer
          minimum: 1
          description: "Order of this action within the activity"
        category:
          type: string
        link:
          type: string
          format: uri
        supported_by:
          type: string
        reference_screen:
          type: string
        delivery_epic:
          type: string
        additional_document_references:
          type: string
        priority:
          type: string
          enum: [high, medium, low]
        estimated_effort:
          type: string
        status:
          type: string
          enum: [active, inactive, archived]

    Persona:
      type: object
      properties:
        persona_id:
          type: string
          example: "persona_busy_parent_shopper"
        organization_id:
          type: string
          example: "acme-retail-group"
        name:
          type: string
          example: "Emily Rodriguez - Busy Working Parent"
        persona_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
          example: "customer"
        # Human persona fields
        image_url:
          type: string
          format: uri
          example: "https://cdn.acmeretail.com/personas/emily_rodriguez.jpg"
        tagline:
          type: string
          example: "Efficient shopping for a busy family"
        age:
          type: integer
          example: 34
        role:
          type: string
          example: "Marketing Manager & Mother of Two"
        income:
          type: string
          example: "$75,000 - $95,000"
        location:
          type: string
          example: "Austin, Texas"
        status_description:
          type: string
          example: "Active professional balancing career and family responsibilities"
        motivations:
          type: array
          items:
            type: string
          example:
            [
              "Save time on grocery shopping",
              "Provide healthy meals for family",
              "Stay within budget",
            ]
        frustrations:
          type: array
          items:
            type: string
          example: ["Long checkout lines", "Out-of-stock items", "Difficult store navigation"]
        goals:
          type: array
          items:
            type: string
          example:
            [
              "Complete shopping in under 30 minutes",
              "Find all items on shopping list",
              "Stay within weekly budget",
            ]
        preferences:
          type: array
          items:
            type: string
          example:
            [
              "Mobile app notifications for deals",
              "Self-checkout options",
              "Organized store layout",
            ]
        # System persona fields (when persona_type = "system")
        description:
          type: string
          example: "Cloud-based inventory management system that tracks product availability in real-time"
        category:
          type: string
          example: "Inventory Management"
        vendor:
          type: string
          example: "Oracle Retail"
        platform:
          type: string
          example: "Oracle Cloud Infrastructure"
        availability:
          type: string
          example: "99.9% uptime SLA"
        # Common fields
        notes:
          type: string
          example: "Primary system for real-time inventory tracking across all store locations"
        status:
          type: string
          enum: [active, inactive, maintenance, deprecated, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    PersonaDetails:
      allOf:
        - $ref: "#/components/schemas/Persona"
        - type: object
          properties:
            moments_used:
              type: array
              items:
                type: object
                properties:
                  moment_id:
                    type: string
                  moment_name:
                    type: string
                  map_name:
                    type: string
                  activity_count:
                    type: integer
                    description: "Number of activities this persona is involved in for this moment"

    PersonaCreate:
      type: object
      required: [name, persona_type]
      properties:
        name:
          type: string
        persona_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
        # Human persona fields (required for non-system personas)
        image_url:
          type: string
          format: uri
        tagline:
          type: string
        age:
          type: integer
          minimum: 1
          maximum: 120
        role:
          type: string
        income:
          type: string
        location:
          type: string
        status_description:
          type: string
        motivations:
          type: array
          items:
            type: string
        frustrations:
          type: array
          items:
            type: string
        goals:
          type: array
          items:
            type: string
        preferences:
          type: array
          items:
            type: string
        # System persona fields (required for system personas)
        description:
          type: string
        category:
          type: string
        vendor:
          type: string
        platform:
          type: string
        availability:
          type: string
        # Common fields
        notes:
          type: string

    PersonaUpdate:
      type: object
      properties:
        name:
          type: string
        persona_type:
          type: string
          enum: [customer, front_stage, back_stage, system]
        # Human persona fields
        image_url:
          type: string
          format: uri
        tagline:
          type: string
        age:
          type: integer
        role:
          type: string
        income:
          type: string
        location:
          type: string
        status_description:
          type: string
        motivations:
          type: array
          items:
            type: string
        frustrations:
          type: array
          items:
            type: string
        goals:
          type: array
          items:
            type: string
        preferences:
          type: array
          items:
            type: string
        # System persona fields
        description:
          type: string
        category:
          type: string
        vendor:
          type: string
        platform:
          type: string
        availability:
          type: string
        # Common fields
        notes:
          type: string
        status:
          type: string
          enum: [active, inactive, maintenance, deprecated, archived]

    Component:
      type: object
      properties:
        component_id:
          type: string
          example: "comp_payment_processing_001"
        organization_id:
          type: string
          example: "acme-retail-group"
        numbering:
          type: string
          example: "PAY-001"
        name:
          type: string
          example: "Secure Payment Processing"
        description:
          type: string
          example: "End-to-end secure payment processing for credit cards and digital wallets"
        best_practices:
          type: string
          example: "Always validate payment information before processing. Implement PCI DSS compliance standards."
        level:
          type: string
          example: "Critical"
        framework:
          type: string
          enum: [customer, business]
          example: "customer"
        phases_used:
          type: array
          items:
            type: string
          example: ["phase_checkout_process", "phase_order_completion"]
        moments_used:
          type: array
          items:
            type: string
          example: ["moment_enter_payment_info", "moment_confirm_purchase"]
        status:
          type: string
          enum: [active, inactive, archived]
          example: "active"
        created_by:
          $ref: "#/components/schemas/UserReference"
        updated_by:
          $ref: "#/components/schemas/UserReference"

    ComponentDetails:
      allOf:
        - $ref: "#/components/schemas/Component"
        - type: object
          properties:
            usage_details:
              type: array
              items:
                type: object
                properties:
                  entity_type:
                    type: string
                    enum: [phase, moment]
                  entity_id:
                    type: string
                  entity_name:
                    type: string
                  map_name:
                    type: string

    ComponentCreate:
      type: object
      required: [numbering, name, description]
      properties:
        numbering:
          type: string
          example: "INV-002"
        name:
          type: string
          example: "Real-time Inventory Sync"
        description:
          type: string
          example: "Synchronizes inventory levels across all channels in real-time"
        best_practices:
          type: string
          example: "Update inventory every 30 seconds. Alert when stock falls below threshold."
        level:
          type: string
          example: "Essential"
        framework:
          type: string
          enum: [customer, business]
          example: "business"

    ComponentUpdate:
      type: object
      properties:
        numbering:
          type: string
        name:
          type: string
        description:
          type: string
        best_practices:
          type: string
        level:
          type: string
        framework:
          type: string
          enum: [customer, business]
        status:
          type: string
          enum: [active, inactive, archived]

    ComponentMatch:
      type: object
      properties:
        component_id:
          type: string
          example: "comp_payment_processing_001"
        component_name:
          type: string
          example: "Secure Payment Processing"
        numbering:
          type: string
          example: "PAY-001"
        similarity_score:
          type: number
          format: float
          minimum: 0
          maximum: 1
          example: 0.92
        description:
          type: string
          example: "End-to-end secure payment processing for credit cards and digital wallets"

    ComponentUploadResult:
      type: object
      properties:
        total_rows:
          type: integer
          description: Total number of rows in the CSV file
        processed_rows:
          type: integer
          description: Number of rows successfully processed
        created_components:
          type: integer
          description: Number of components successfully created
        updated_components:
          type: integer
          description: Number of components updated (when overwrite_existing=true)
        failed_rows:
          type: integer
          description: Number of rows that failed processing
        validation_errors:
          type: array
          items:
            $ref: "#/components/schemas/ComponentValidationError"
        created_component_ids:
          type: array
          items:
            type: string
          description: IDs of successfully created components
        processing_time_ms:
          type: integer
          description: Time taken to process the upload in milliseconds

    ComponentValidationError:
      type: object
      properties:
        row_number:
          type: integer
          description: Row number in the CSV file (1-based)
        field:
          type: string
          description: Field name that caused the error
        value:
          type: string
          description: Invalid value that was provided
        error_message:
          type: string
          description: Description of the validation error
        error_code:
          type: string
          enum:
            [
              required_field_missing,
              invalid_format,
              duplicate_numbering,
              invalid_enum_value,
              field_too_long,
            ]

    UserReference:
      type: object
      properties:
        user_id:
          type: string
          example: "usr_sarah_johnson_001"
        name:
          type: string
          example: "Sarah Johnson"
        profile_image_url:
          type: string
          format: uri
          example: "https://cdn.acmefinancial.com/avatars/sarah_johnson.jpg"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T14:30:00Z"

    Error:
      type: object
      properties:
        error:
          type: string
          example: "validation_failed"
        code:
          type: string
          example: "VAL_001"
        message:
          type: string
          example: "Required field 'name' is missing from the request"
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T14:30:00Z"

    ActivitiesByType:
      type: object
      required: [customer, front_stage, back_stage, system]
      description: Activities grouped by type (customer, front_stage, back_stage, system)
      properties:
        customer:
          type: array
          items:
            $ref: "#/components/schemas/ActivityDetails"
          description: Customer activities
        front_stage:
          type: array
          items:
            $ref: "#/components/schemas/ActivityDetails"
          description: Front-stage employee activities
        back_stage:
          type: array
          items:
            $ref: "#/components/schemas/ActivityDetails"
          description: Back-stage employee activities
        system:
          type: array
          items:
            $ref: "#/components/schemas/ActivityDetails"
          description: System/automated activities
      example:
        customer:
          - activity_id: "act_browse_mobile_catalog"
            name: "Browse Product Catalog"
            description: "Customer browses products using mobile app"
            activity_type: "customer"
        front_stage:
          - activity_id: "act_assist_product_location"
            name: "Assist with Product Location"
            description: "Store associate helps customer find products"
            activity_type: "front_stage"
        back_stage:
          - activity_id: "act_update_inventory"
            name: "Update Inventory Levels"
            description: "Staff updates inventory levels in the system"
            activity_type: "back_stage"
        system:
          - activity_id: "act_process_payment"
            name: "Process Payment Transaction"
            description: "System automatically processes customer payment"
            activity_type: "system"

import TouchpointCard from "@/components/mapCanvas-deprecated/TouchpointCard";
import { Box, List } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof TouchpointCard> = {
  title: "Composites/Map Canvas/Touchpoint Card",
  component: TouchpointCard,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#2a2a2a" },
        { name: "light", value: "#ffffff" },
      ],
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onEdit: { action: "edit touchpoint" },
    onDelete: { action: "delete touchpoint" },
    onExpand: { action: "expand touchpoint" },
    onImageUpload: { action: "image uploaded" },
    onSectionAdd: { action: "section added" },
    onSectionItemAdd: { action: "section item added" },
    touchpoint: {
      control: "object",
      description: "Touchpoint data object",
    },
  },
  decorators: [
    (Story) => (
      <Box sx={{ width: "400px", backgroundColor: "#2a2a2a", p: 2 }}>
        <List sx={{ px: 0 }}>
          <Story />
        </List>
      </Box>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof TouchpointCard>;

// Pending status
export const Pending: Story = {
  args: {
    touchpoint: {
      id: "1",
      title: "ABC CRM",
      description: "Prospect document",
      status: "pending",
      isActive: true,
    },
  },
  parameters: {
    docs: {
      description: {
        story: "Touchpoint card with pending status (orange clock icon).",
      },
    },
  },
};

// Scheduled status
export const Scheduled: Story = {
  args: {
    touchpoint: {
      id: "2",
      title: "ATM",
      description: "Advertisement",
      status: "scheduled",
      isActive: true,
    },
  },
  parameters: {
    docs: {
      description: {
        story: "Touchpoint card with scheduled status (blue calendar icon).",
      },
    },
  },
};

// Completed status
export const Completed: Story = {
  args: {
    touchpoint: {
      id: "3",
      title: "ABC application",
      description: "Customer application tracker",
      status: "completed",
      isActive: true,
    },
  },
  parameters: {
    docs: {
      description: {
        story: "Touchpoint card with completed status (green checkmark icon).",
      },
    },
  },
};

// Multiple cards
export const MultipleCards: Story = {
  render: () => (
    <Box sx={{ width: "400px", backgroundColor: "#2a2a2a", p: 2 }}>
      <List sx={{ px: 0 }}>
        <TouchpointCard
          touchpoint={{
            id: "1",
            title: "ABC CRM",
            description: "Prospect document",
            status: "pending",
            isActive: true,
          }}
          onEdit={(id) => console.log("Edit:", id)}
          onDelete={(id) => console.log("Delete:", id)}
          onExpand={(id) => console.log("Expand:", id)}
        />
        <TouchpointCard
          touchpoint={{
            id: "2",
            title: "ABC application",
            description: "Customer application tracker",
            status: "completed",
            isActive: true,
          }}
          onEdit={(id) => console.log("Edit:", id)}
          onDelete={(id) => console.log("Delete:", id)}
          onExpand={(id) => console.log("Expand:", id)}
        />
        <TouchpointCard
          touchpoint={{
            id: "3",
            title: "Post",
            description: "Changeover pack",
            status: "scheduled",
            isActive: true,
          }}
          onEdit={(id) => console.log("Edit:", id)}
          onDelete={(id) => console.log("Delete:", id)}
          onExpand={(id) => console.log("Expand:", id)}
        />
      </List>
    </Box>
  ),
  parameters: {
    docs: {
      description: {
        story: "Multiple touchpoint cards showing different statuses and interactions.",
      },
    },
  },
};

// With expansion content
export const WithExpansionContent: Story = {
  args: {
    touchpoint: {
      id: "1",
      title: "ABC CRM",
      description: "Prospect document",
      status: "pending",
      isActive: true,
      referenceImage:
        "https://wac-cdn.atlassian.com/dam/jcr:************************a470428cb6cd/Agile-report.png",
      sections: [
        {
          id: "delivery-epics",
          title: "Delivery epics",
          items: [],
        },
        {
          id: "support-documents",
          title: "Support documents",
          items: [],
        },
      ],
    },
    onEdit: (id) => console.log("Edit touchpoint:", id),
    onDelete: (id) => console.log("Delete touchpoint:", id),
    onExpand: (id) => console.log("Expand touchpoint:", id),
    onImageUpload: (id, file) => console.log("Image uploaded for:", id, file.name),
    onSectionAdd: (touchpointId, sectionTitle) =>
      console.log("Add section:", sectionTitle, "to:", touchpointId),
    onSectionItemAdd: (touchpointId, sectionId, item) =>
      console.log("Add item:", item, "to section:", sectionId),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Touchpoint card with expansion content including reference image and dynamic sections. Click the expand arrow to see the full content.",
      },
    },
  },
};

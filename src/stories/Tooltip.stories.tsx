import InfoIcon from "@mui/icons-material/Info";
import { Box, Button, Fade, Grow, IconButton, Tooltip, Typography, Zoom } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof Tooltip> = {
  title: "Components/Tooltip",
  component: Tooltip,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "Tooltip title",
    },
    placement: {
      control: "select",
      options: [
        "bottom-end",
        "bottom-start",
        "bottom",
        "left-end",
        "left-start",
        "left",
        "right-end",
        "right-start",
        "right",
        "top-end",
        "top-start",
        "top",
      ],
      description: "Tooltip placement",
    },
    arrow: {
      control: "boolean",
      description: "If true, adds an arrow to the tooltip",
    },
    followCursor: {
      control: "boolean",
      description: "If true, the tooltip follows the cursor",
    },
    enterDelay: {
      control: "number",
      description: "The number of milliseconds to wait before showing the tooltip",
    },
    leaveDelay: {
      control: "number",
      description: "The number of milliseconds to wait before hiding the tooltip",
    },
  },
  decorators: [
    (Story) => (
      <Box sx={{ p: 5, display: "flex", justifyContent: "center", alignItems: "center" }}>
        <Story />
      </Box>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Tooltip>;

// Basic tooltip
export const Basic: Story = {
  args: {
    title: "Basic Tooltip",
    children: <Button>Hover me</Button>,
  },
};

// Tooltip with arrow
export const WithArrow: Story = {
  args: {
    title: "Tooltip with Arrow",
    arrow: true,
    children: <Button>Hover me</Button>,
  },
};

// Tooltip placement
export const Placement: Story = {
  render: () => (
    <Box sx={{ display: "grid", gridTemplateColumns: "repeat(3, 1fr)", gap: 2 }}>
      <Tooltip title="Top" placement="top" arrow>
        <Button variant="outlined" fullWidth>
          Top
        </Button>
      </Tooltip>
      <Tooltip title="Top Start" placement="top-start" arrow>
        <Button variant="outlined" fullWidth>
          Top Start
        </Button>
      </Tooltip>
      <Tooltip title="Top End" placement="top-end" arrow>
        <Button variant="outlined" fullWidth>
          Top End
        </Button>
      </Tooltip>
      <Tooltip title="Left" placement="left" arrow>
        <Button variant="outlined" fullWidth>
          Left
        </Button>
      </Tooltip>
      <Tooltip title="Center" placement="bottom" arrow>
        <Button variant="outlined" fullWidth>
          Center
        </Button>
      </Tooltip>
      <Tooltip title="Right" placement="right" arrow>
        <Button variant="outlined" fullWidth>
          Right
        </Button>
      </Tooltip>
      <Tooltip title="Bottom" placement="bottom" arrow>
        <Button variant="outlined" fullWidth>
          Bottom
        </Button>
      </Tooltip>
      <Tooltip title="Bottom Start" placement="bottom-start" arrow>
        <Button variant="outlined" fullWidth>
          Bottom Start
        </Button>
      </Tooltip>
      <Tooltip title="Bottom End" placement="bottom-end" arrow>
        <Button variant="outlined" fullWidth>
          Bottom End
        </Button>
      </Tooltip>
    </Box>
  ),
};

// Tooltip with icon button
export const WithIconButton: Story = {
  args: {
    title: "Info",
    children: (
      <IconButton>
        <InfoIcon />
      </IconButton>
    ),
  },
};

// Tooltip with HTML content
export const WithHTMLContent: Story = {
  args: {
    title: (
      <>
        <Typography color="inherit" variant="subtitle2">
          Tooltip with HTML Content
        </Typography>
        <Typography color="inherit" variant="body2">
          You can add multiple lines and formatting.
        </Typography>
      </>
    ),
    arrow: true,
    children: <Button>Hover for rich tooltip</Button>,
  },
};

// Tooltip with custom delay
export const WithDelay: Story = {
  args: {
    title: "Delayed Tooltip",
    enterDelay: 1000,
    leaveDelay: 500,
    children: <Button>Hover with delay</Button>,
  },
};

// Tooltip that follows cursor
export const FollowCursor: Story = {
  args: {
    title: "Following Cursor",
    followCursor: true,
    children: <Button>Move cursor over me</Button>,
  },
};

// Tooltip with Zoom transition
export const ZoomTransition: Story = {
  args: {
    title: "Zoom Transition",
    TransitionComponent: Zoom,
    children: <Button>Hover for zoom effect</Button>,
  },
};

// Tooltip with Fade transition
export const FadeTransition: Story = {
  args: {
    title: "Fade Transition",
    TransitionComponent: Fade,
    children: <Button>Hover for fade effect</Button>,
  },
};

// Tooltip with Grow transition
export const GrowTransition: Story = {
  args: {
    title: "Grow Transition",
    TransitionComponent: Grow,
    children: <Button>Hover for grow effect</Button>,
  },
};

// Tooltip with custom styling
export const CustomStyling: Story = {
  args: {
    title: "Custom Styled Tooltip",
    arrow: true,
    componentsProps: {
      tooltip: {
        sx: {
          bgcolor: "primary.main",
          "& .MuiTooltip-arrow": {
            color: "primary.main",
          },
          boxShadow: 2,
          borderRadius: 2,
          p: 2,
        },
      },
    },
    children: <Button>Hover for custom style</Button>,
  },
};

// Tooltip with disabled button
export const WithDisabledButton: Story = {
  render: () => (
    <Tooltip title="You can't click a disabled button">
      <span>
        <Button disabled>Disabled Button</Button>
      </span>
    </Tooltip>
  ),
};

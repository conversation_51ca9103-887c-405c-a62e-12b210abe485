import CloseIcon from "@mui/icons-material/Close";
import InfoIcon from "@mui/icons-material/Info";
import { Alert, AlertTitle, Box, Button, IconButton } from "@mui/material";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof Alert> = {
  title: "Components/Alert",
  component: Alert,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    severity: {
      control: "select",
      options: ["error", "warning", "info", "success"],
      description: "The severity of the alert",
    },
    variant: {
      control: "select",
      options: ["standard", "filled", "outlined"],
      description: "The variant to use",
    },
    onClose: { action: "closed" },
  },
  decorators: [
    (Story) => (
      <Box sx={{ width: "500px" }}>
        <Story />
      </Box>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Alert>;

// Default alert
export const Default: Story = {
  args: {
    severity: "info",
    children: "This is an information alert",
  },
};

// Success alert
export const Success: Story = {
  args: {
    severity: "success",
    children: "This is a success alert",
  },
};

// Warning alert
export const Warning: Story = {
  args: {
    severity: "warning",
    children: "This is a warning alert",
  },
};

// Error alert
export const Error: Story = {
  args: {
    severity: "error",
    children: "This is an error alert",
  },
};

// Alert with title
export const WithTitle: Story = {
  args: {
    severity: "info",
    children: (
      <>
        <AlertTitle>Alert Title</AlertTitle>
        This is an alert with a title
      </>
    ),
  },
};

// Outlined variant
export const Outlined: Story = {
  args: {
    variant: "outlined",
    severity: "info",
    children: "This is an outlined alert",
  },
};

// Filled variant
export const Filled: Story = {
  args: {
    variant: "filled",
    severity: "info",
    children: "This is a filled alert",
  },
};

// Alert with close button
export const WithCloseButton: Story = {
  args: {
    severity: "info",
    onClose: () => {},
    children: "This is an alert with a close button",
  },
};

// Alert with custom close button
export const WithCustomCloseButton: Story = {
  args: {
    severity: "info",
    action: (
      <IconButton aria-label="close" color="inherit" size="small" onClick={() => {}}>
        <CloseIcon fontSize="inherit" />
      </IconButton>
    ),
    children: "This is an alert with a custom close button",
  },
};

// Alert with action
export const WithAction: Story = {
  args: {
    severity: "warning",
    action: (
      <Button color="inherit" size="small">
        UNDO
      </Button>
    ),
    children: "This is an alert with an action button",
  },
};

// Alert with custom icon
export const WithCustomIcon: Story = {
  args: {
    severity: "info",
    icon: <InfoIcon fontSize="inherit" />,
    children: "This is an alert with a custom icon",
  },
};

// Alert without icon
export const WithoutIcon: Story = {
  args: {
    severity: "info",
    icon: false,
    children: "This is an alert without an icon",
  },
};

// Alert with long text
export const WithLongText: Story = {
  args: {
    severity: "info",
    children: (
      <>
        <AlertTitle>Long Message</AlertTitle>
        This is an alert with a very long message that demonstrates how the alert handles text
        wrapping. It contains multiple lines of text to show how the content is displayed when it
        exceeds the width of the container. The alert should maintain its proper styling and layout
        even with extended content.
      </>
    ),
  },
};

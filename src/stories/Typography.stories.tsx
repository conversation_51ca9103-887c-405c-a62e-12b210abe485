import { Box, Divider, Typography } from "@mui/material";
import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta<typeof Typography> = {
  title: "Components/Typography",
  component: Typography,
  parameters: {
    layout: "centered",
  },
  args: {
    color: "textPrimary", // Set default color to textPrimary for all stories
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: [
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "subtitle1",
        "subtitle2",
        "body1",
        "body2",
        "button",
        "caption",
        "overline",
      ],
      description: "The variant to use",
    },
    align: {
      control: "select",
      options: ["inherit", "left", "center", "right", "justify"],
      description: "The alignment of the text",
    },
    color: {
      control: "select",
      options: [
        "initial",
        "inherit",
        "primary",
        "secondary",
        "textPrimary",
        "textSecondary",
        "error",
      ],
      description: "The color of the component",
    },
    gutterBottom: {
      control: "boolean",
      description: "If true, the text will have a bottom margin",
    },
    noWrap: {
      control: "boolean",
      description: "If true, the text will not wrap, but instead will truncate with an ellipsis",
    },
    paragraph: {
      control: "boolean",
      description: "If true, the text will have a bottom margin",
    },
  },
  decorators: [
    (Story) => (
      <Box sx={{ width: "500px", p: 2 }}>
        <Story />
      </Box>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Typography>;

// Heading 1
export const H1: Story = {
  args: {
    variant: "h1",
    children: "Heading 1",
  },
};

// Heading 2
export const H2: Story = {
  args: {
    variant: "h2",
    children: "Heading 2",
  },
};

// Heading 3
export const H3: Story = {
  args: {
    variant: "h3",
    children: "Heading 3",
  },
};

// Heading 4
export const H4: Story = {
  args: {
    variant: "h4",
    children: "Heading 4",
  },
};

// Heading 5
export const H5: Story = {
  args: {
    variant: "h5",
    children: "Heading 5",
  },
};

// Heading 6
export const H6: Story = {
  args: {
    variant: "h6",
    children: "Heading 6",
  },
};

// Subtitle 1
export const Subtitle1: Story = {
  args: {
    variant: "subtitle1",
    children: "Subtitle 1",
  },
};

// Subtitle 2
export const Subtitle2: Story = {
  args: {
    variant: "subtitle2",
    children: "Subtitle 2",
  },
};

// Body 1
export const Body1: Story = {
  args: {
    variant: "body1",
    children:
      "Body 1. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor.",
  },
};

// Body 2
export const Body2: Story = {
  args: {
    variant: "body2",
    children:
      "Body 2. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris. Vivamus hendrerit arcu sed erat molestie vehicula. Sed auctor neque eu tellus rhoncus ut eleifend nibh porttitor.",
  },
};

// Button text
export const ButtonText: Story = {
  args: {
    variant: "button",
    children: "BUTTON TEXT",
  },
};

// Caption text
export const Caption: Story = {
  args: {
    variant: "caption",
    children: "Caption text",
  },
};

// Overline text
export const Overline: Story = {
  args: {
    variant: "overline",
    children: "OVERLINE TEXT",
  },
};

// With gutter bottom
export const GutterBottom: Story = {
  args: {
    variant: "body1",
    gutterBottom: true,
    children:
      "This text has a gutter bottom. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
  },
};

// No wrap
export const NoWrap: Story = {
  args: {
    variant: "body1",
    noWrap: true,
    children:
      "This text will not wrap and will be truncated with an ellipsis if it's too long. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris.",
  },
};

// Paragraph
export const Paragraph: Story = {
  args: {
    variant: "body1",
    paragraph: true,
    children:
      "This text is a paragraph with bottom margin. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
  },
};

// Centered text
export const Centered: Story = {
  args: {
    variant: "body1",
    align: "center",
    children: "This text is centered. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
  },
};

// Typography scale
export const TypographyScale: Story = {
  render: () => (
    <Box>
      <Typography variant="h1" color="textPrimary" gutterBottom>
        h1. Heading
      </Typography>
      <Typography variant="h2" color="textPrimary" gutterBottom>
        h2. Heading
      </Typography>
      <Typography variant="h3" color="textPrimary" gutterBottom>
        h3. Heading
      </Typography>
      <Typography variant="h4" color="textPrimary" gutterBottom>
        h4. Heading
      </Typography>
      <Typography variant="h5" color="textPrimary" gutterBottom>
        h5. Heading
      </Typography>
      <Typography variant="h6" color="textPrimary" gutterBottom>
        h6. Heading
      </Typography>
      <Divider sx={{ my: 2 }} />
      <Typography variant="subtitle1" color="textPrimary" gutterBottom>
        subtitle1. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
      </Typography>
      <Typography variant="subtitle2" color="textPrimary" gutterBottom>
        subtitle2. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
      </Typography>
      <Typography variant="body1" color="textPrimary" gutterBottom>
        body1. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris.
      </Typography>
      <Typography variant="body2" color="textPrimary" gutterBottom>
        body2. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam in dui mauris.
      </Typography>
      <Typography variant="button" color="textPrimary" display="block" gutterBottom>
        BUTTON TEXT
      </Typography>
      <Typography variant="caption" color="textPrimary" display="block" gutterBottom>
        caption text
      </Typography>
      <Typography variant="overline" color="textPrimary" display="block" gutterBottom>
        OVERLINE TEXT
      </Typography>
    </Box>
  ),
};

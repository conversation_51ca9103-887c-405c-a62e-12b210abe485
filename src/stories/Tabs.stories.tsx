import { Box, Tab, Tabs, Typography } from "@mui/material";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { useState } from "react";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography color="textPrimary">{children}</Typography>
        </Box>
      )}
    </div>
  );
}

// Wrapper component to handle tab state
const TabsWrapper = ({
  variant,
  orientation,
  centered,
  scrollButtons,
  indicatorColor,
}: {
  variant?: "standard" | "scrollable" | "fullWidth";
  orientation?: "horizontal" | "vertical";
  centered?: boolean;
  scrollButtons?: "auto" | true | false;
  indicatorColor?: "primary" | "secondary";
}) => {
  const [value, setValue] = useState(0);
  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box
      sx={{
        width: orientation === "vertical" ? "100%" : "500px",
        display: "flex",
        flexDirection: orientation === "vertical" ? "row" : "column",
      }}
    >
      <Box
        sx={{
          borderBottom: orientation === "horizontal" ? 1 : 0,
          borderRight: orientation === "vertical" ? 1 : 0,
          borderColor: "divider",
          width: orientation === "vertical" ? "200px" : "100%",
        }}
      >
        <Tabs
          value={value}
          onChange={handleChange}
          variant={variant}
          orientation={orientation}
          centered={centered}
          scrollButtons={scrollButtons}
          indicatorColor={indicatorColor}
          textColor="primary"
          aria-label="tabs example"
        >
          <Tab label="Item One" id="tab-0" aria-controls="tabpanel-0" />
          <Tab label="Item Two" id="tab-1" aria-controls="tabpanel-1" />
          <Tab label="Item Three" id="tab-2" aria-controls="tabpanel-2" />
          {variant === "scrollable" && (
            <>
              <Tab label="Item Four" id="tab-3" aria-controls="tabpanel-3" />
              <Tab label="Item Five" id="tab-4" aria-controls="tabpanel-4" />
              <Tab label="Item Six" id="tab-5" aria-controls="tabpanel-5" />
            </>
          )}
        </Tabs>
      </Box>
      <Box sx={{ flex: 1 }}>
        <TabPanel value={value} index={0}>
          Content for Item One
        </TabPanel>
        <TabPanel value={value} index={1}>
          Content for Item Two
        </TabPanel>
        <TabPanel value={value} index={2}>
          Content for Item Three
        </TabPanel>
        {variant === "scrollable" && (
          <>
            <TabPanel value={value} index={3}>
              Content for Item Four
            </TabPanel>
            <TabPanel value={value} index={4}>
              Content for Item Five
            </TabPanel>
            <TabPanel value={value} index={5}>
              Content for Item Six
            </TabPanel>
          </>
        )}
      </Box>
    </Box>
  );
};

const meta: Meta<typeof TabsWrapper> = {
  title: "Components/Tabs",
  component: TabsWrapper,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["standard", "scrollable", "fullWidth"],
      description: "The variant to use",
    },
    orientation: {
      control: "select",
      options: ["horizontal", "vertical"],
      description: "The orientation of the tabs",
    },
    centered: {
      control: "boolean",
      description: "If true, the tabs will be centered",
    },
    scrollButtons: {
      control: "select",
      options: ["auto", true, false],
      description: "Determines the behavior of scroll buttons when tabs are set to scroll",
    },
    indicatorColor: {
      control: "select",
      options: ["primary", "secondary"],
      description: "Determines the color of the indicator",
    },
  },
};

export default meta;
type Story = StoryObj<typeof TabsWrapper>;

// Default tabs
export const Default: Story = {
  args: {
    variant: "standard",
    orientation: "horizontal",
    centered: false,
    indicatorColor: "primary",
  },
};

// Centered tabs
export const Centered: Story = {
  args: {
    variant: "standard",
    orientation: "horizontal",
    centered: true,
    indicatorColor: "primary",
  },
};

// Full width tabs
export const FullWidth: Story = {
  args: {
    variant: "fullWidth",
    orientation: "horizontal",
    centered: false,
    indicatorColor: "primary",
  },
};

// Vertical tabs
export const Vertical: Story = {
  args: {
    variant: "standard",
    orientation: "vertical",
    centered: false,
    indicatorColor: "primary",
  },
};

import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import FavoriteIcon from "@mui/icons-material/Favorite";
import HomeIcon from "@mui/icons-material/Home";
import NotificationsIcon from "@mui/icons-material/Notifications";
import SettingsIcon from "@mui/icons-material/Settings";
import { Badge, Box, IconButton } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof IconButton> = {
  title: "Components/IconButton",
  component: IconButton,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    color: {
      control: "select",
      options: [
        "default",
        "primary",
        "secondary",
        "error",
        "info",
        "success",
        "warning",
        "inherit",
      ],
      description: "The color of the component",
    },
    size: {
      control: "select",
      options: ["small", "medium", "large"],
      description: "The size of the component",
    },
    disabled: {
      control: "boolean",
      description: "If true, the component is disabled",
    },
    edge: {
      control: "select",
      options: ["start", "end", false],
      description: "If given, uses a negative margin to counteract the padding on one side",
    },
    onClick: { action: "clicked" },
  },
  decorators: [
    (Story) => (
      <Box sx={{ display: "flex", gap: 2, flexWrap: "wrap" }}>
        <Story />
      </Box>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof IconButton>;

// Default icon button
export const Default: Story = {
  args: {
    children: <AddIcon />,
    "aria-label": "add",
  },
};

// Primary color
export const Primary: Story = {
  args: {
    color: "primary",
    children: <EditIcon />,
    "aria-label": "edit",
  },
};

// Secondary color
export const Secondary: Story = {
  args: {
    color: "secondary",
    children: <FavoriteIcon />,
    "aria-label": "favorite",
  },
};

// Error color
export const Error: Story = {
  args: {
    color: "error",
    children: <DeleteIcon />,
    "aria-label": "delete",
  },
};

// Small size
export const Small: Story = {
  args: {
    size: "small",
    children: <SettingsIcon fontSize="small" />,
    "aria-label": "settings",
  },
};

// Large size
export const Large: Story = {
  args: {
    size: "large",
    children: <HomeIcon fontSize="large" />,
    "aria-label": "home",
  },
};

// Disabled
export const Disabled: Story = {
  args: {
    disabled: true,
    children: <DeleteIcon />,
    "aria-label": "delete",
  },
};

// With badge
export const WithBadge: Story = {
  args: {
    children: (
      <Badge badgeContent={4} color="error">
        <NotificationsIcon />
      </Badge>
    ),
    "aria-label": "notifications",
  },
};

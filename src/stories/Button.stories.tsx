import AddIcon from "@mui/icons-material/Add";
import SendIcon from "@mui/icons-material/Send";
import { Button } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof Button> = {
  title: "Components/Button",
  component: Button,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["text", "contained", "outlined"],
      description: "The variant to use",
    },
    color: {
      control: "select",
      options: ["primary", "secondary", "success", "error", "info", "warning"],
      description: "The color of the component",
    },
    size: {
      control: "select",
      options: ["small", "medium", "large"],
      description: "The size of the component",
    },
    disabled: {
      control: "boolean",
      description: "If true, the component is disabled",
    },
    onClick: { action: "clicked" },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

// Default button
export const Default: Story = {
  args: {
    variant: "contained",
    children: "Button",
  },
};

// Text button
export const Text: Story = {
  args: {
    variant: "text",
    children: "Text Button",
  },
};

// Outlined button
export const Outlined: Story = {
  args: {
    variant: "outlined",
    children: "Outlined Button",
  },
};

// Button with icon
export const WithStartIcon: Story = {
  args: {
    variant: "contained",
    startIcon: <AddIcon />,
    children: "Add Item",
  },
};

// Button with end icon
export const WithEndIcon: Story = {
  args: {
    variant: "contained",
    endIcon: <SendIcon />,
    children: "Send",
  },
};

// Small button
export const Small: Story = {
  args: {
    variant: "contained",
    size: "small",
    children: "Small Button",
  },
};

// Large button
export const Large: Story = {
  args: {
    variant: "contained",
    size: "large",
    children: "Large Button",
  },
};

// Disabled button
export const Disabled: Story = {
  args: {
    variant: "contained",
    disabled: true,
    children: "Disabled Button",
  },
};

// Primary color
export const Primary: Story = {
  args: {
    variant: "contained",
    color: "primary",
    children: "Primary Button",
  },
};

// Secondary color
export const Secondary: Story = {
  args: {
    variant: "contained",
    color: "secondary",
    children: "Secondary Button",
  },
};

// Error color
export const Error: Story = {
  args: {
    variant: "contained",
    color: "error",
    children: "Error Button",
  },
};

// Full width button
export const FullWidth: Story = {
  args: {
    variant: "contained",
    fullWidth: true,
    children: "Full Width Button",
  },
  decorators: [
    (Story) => (
      <div style={{ width: "400px" }}>
        <Story />
      </div>
    ),
  ],
};

import { CustomerJourneyMap } from "@/components/mapCanvas/CustomerJourneyMap";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof CustomerJourneyMap> = {
  title: "Composites/Map Canvas/Map",
  component: CustomerJourneyMap,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
# Customer Journey Map

A comprehensive customer journey mapping component with the following features:

## Key Features
- **Fixed Headers**: Both row and column headers remain visible while scrolling
- **Expandable Rows**: Customer, Front Stage, and Back Stage rows can be expanded to show sub-rows
- **Zoom Controls**: Zoom in/out with buttons or fit to screen
- **Pan/Select Modes**: Switch between panning the canvas and selecting cells
- **Story Mode**: Toggle to show/hide story row
- **Interactive Menus**: Right-click context menus for rows and columns
- **Building Blocks**: Various building block types (Painpoints, Opportunities, etc.)

## Controls
- **Left Toolbar**: Contains zoom, pan/select mode, and settings controls
- **Settings Menu**: Toggle story mode and show/hide different row types
- **Mouse Interactions**: 
  - Pan mode: Click and drag to pan the canvas
  - Select mode: Click cells to select them
  - Scroll: Use mouse wheel to scroll content area
  - Right-click: Open context menus (not fully implemented in this demo)

## Data Structure
The component uses a hierarchical structure with:
- **Stages**: High-level journey phases (Awareness, Consideration, Purchase, Experience)
- **Phases**: Sub-phases within each stage (Discovery, Research, Compare, etc.)
- **Row Groups**: Different swim lanes (Story, Customer, Front Stage, Back Stage, Building Blocks)
- **Sub-rows**: Expandable details within main row groups

## Theme
Uses a dark theme optimized for journey mapping with distinct colors for different element types:
- **Primary Blue**: For headers and story elements
- **Success Green**: For action elements
- **Secondary Pink**: For building blocks
- **Grey Tones**: For system elements and backgrounds
        `,
      },
    },
  },
  argTypes: {
    // No props to control since the component is self-contained
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof CustomerJourneyMap>;

export const Default: Story = {
  name: "Default Journey Map",
  render: () => {
    const sampleMapData = {
      moments: [],
      activities: [],
      personas: [],
      buildingBlocks: [],
      buildingBlockTypes: [],
    };

    return <CustomerJourneyMap mapData={sampleMapData} />;
  },
  parameters: {
    docs: {
      description: {
        story: `
The default customer journey map with sample data showing a complete customer journey from awareness to experience.

**Try these interactions:**
1. Use the zoom controls in the left toolbar
2. Toggle between pan and select modes
3. Click the settings button to access story mode and row visibility options
4. Expand/collapse the Customer, Front Stage, and Back Stage rows using the arrow buttons
5. Scroll around the canvas to see different phases
6. Try selecting cells in select mode
        `,
      },
    },
  },
};

export const WithExpandedRows: Story = {
  name: "With Expanded Rows",
  render: () => {
    const sampleMapData = {
      moments: [],
      activities: [],
      personas: [],
      buildingBlocks: [],
      buildingBlockTypes: [],
    };

    return <CustomerJourneyMap mapData={sampleMapData} />;
  },
  parameters: {
    docs: {
      description: {
        story: `
The same journey map but you can manually expand the Customer, Front Stage, and Back Stage rows to see their sub-components.

**Expanded Row Details:**
- **Customer**: Actions and Feelings sub-rows
- **Front Stage**: Digital and Physical touchpoints
- **Back Stage**: Systems and Processes

Click the expand/collapse arrows in the row headers to toggle visibility.
        `,
      },
    },
  },
};

export const StoryModeEnabled: Story = {
  name: "Story Mode Focus",
  render: () => {
    const sampleMapData = {
      moments: [],
      activities: [],
      personas: [],
      buildingBlocks: [],
      buildingBlockTypes: [],
    };

    return <CustomerJourneyMap mapData={sampleMapData} />;
  },
  parameters: {
    docs: {
      description: {
        story: `
Use the settings menu (gear icon) in the left toolbar to enable "Story Mode" which highlights the story row for narrative focus.

**Story Mode Features:**
- Emphasizes the story row for narrative mapping
- Can be toggled on/off via the settings menu
- Useful for focusing on the customer narrative flow

Access the settings menu and toggle "Story Mode" to see the difference.
        `,
      },
    },
  },
};

export const CustomRowVisibility: Story = {
  name: "Custom Row Visibility",
  render: () => {
    const sampleMapData = {
      moments: [],
      activities: [],
      personas: [],
      buildingBlocks: [],
      buildingBlockTypes: [],
    };

    return <CustomerJourneyMap mapData={sampleMapData} />;
  },
  parameters: {
    docs: {
      description: {
        story: `
Demonstrate the row visibility controls available in the settings menu.

**Available Row Controls:**
- **Action Swim Lanes**: Customer, Front Stage, Back Stage
- **Building Blocks**: Painpoints, Opportunity, Voice of Customer, Operation Context, Analysis

**How to use:**
1. Click the settings (gear) icon in the left toolbar
2. Navigate to "Action swim lane" or "Building blocks" submenus
3. Toggle individual rows on/off using the switches
4. See how the grid adapts to show/hide different content types

This is useful for focusing on specific aspects of the journey or reducing visual complexity.
        `,
      },
    },
  },
};

export const ZoomAndPanDemo: Story = {
  name: "Zoom and Pan Demo",
  render: () => {
    const sampleMapData = {
      moments: [],
      activities: [],
      personas: [],
      buildingBlocks: [],
      buildingBlockTypes: [],
    };

    return <CustomerJourneyMap mapData={sampleMapData} />;
  },
  parameters: {
    docs: {
      description: {
        story: `
Demonstrates the zoom and pan functionality of the journey map.

**Zoom Controls:**
- **Zoom In** (+): Increase scale up to 200%
- **Zoom Out** (-): Decrease scale down to 50%
- **Fit to Screen**: Reset zoom to 100% and center the view

**Pan Controls:**
- **Pan Mode** (hand icon): Click and drag to move around the canvas
- **Select Mode**: Click to select individual cells

**Mouse Interactions:**
- Mouse wheel: Scroll the content area
- Click and drag (in pan mode): Move the canvas around
- Click cells (in select mode): Select individual cells

Try switching between pan and select modes and using the zoom controls to navigate the large journey map.
        `,
      },
    },
  },
};

export const WithApiData: Story = {
  name: "With API Data",
  render: () => {
    const sampleMapData = {
      moments: [],
      activities: [],
      personas: [],
      buildingBlocks: [],
      buildingBlockTypes: [],
    };

    return <CustomerJourneyMap mapData={sampleMapData} />;
  },
};

export const WithMapData: Story = {
  name: "With Map Data (Real API Flow)",
  render: () => {
    const sampleMapData = {
      id: "123",
      name: "Sample Customer Journey",
      status: "Draft",
      moments: [],
      activities: [],
      personas: [],
      building_blocks: [],
      building_block_types: [],
    };

    return <CustomerJourneyMap map={sampleMapData} />;
  },
  parameters: {
    docs: {
      description: {
        story: `
This story demonstrates the CustomerJourneyMap component using the exact same data flow as the real application.

**Real API Data Flow:**
- Mimics how the component receives data from \`mapService.getMapDetails()\`
- Shows proper initialization through the \`map\` prop
- Demonstrates the full data transformation pipeline
- Uses the exact same structure as the actual API response

**Sample Map Data Includes:**
- **Map Metadata**: ID, name, status
- **Phases**: Awareness, Consideration, Purchase, Experience
- **Actions**: Customer and sales actions mapped to specific phases
- **Roles**: Customer and Sales personas with proper structure

This demonstrates how the component would work when integrated with the actual application and API data.
        `,
      },
    },
  },
};

import { Meta } from "@storybook/blocks";

<Meta title="Styles/Documentation" />

# Design System Documentation

This document explains how to use and update the design system in this project.

## Overview

The design system is built using Material UI (MUI) and consists of two main files:

1. `design-tokens.ts` - Contains all the design tokens (colors, typography, spacing, etc.)
2. `theme.ts` - Uses the design tokens to create a theme for the application

## For Designers

### How to Update the Design System

As a designer, you should focus on updating the `design-tokens.ts` file. This file contains all the visual aspects of the design system, organized into clear sections.

#### Color Palette

The color palette is defined in the `colors` object. You can update the following color categories:

- **Primary Colors**: Main brand colors
- **Secondary Colors**: Complementary colors
- **Error Colors**: Used for error states
- **Warning Colors**: Used for warning states
- **Info Colors**: Used for informational states
- **Success Colors**: Used for success states
- **Grey Palette**: Neutral colors
- **Text Colors**: Colors for text in light mode
- **Dark Text Colors**: Colors for text in dark mode
- **Background Colors**: Colors for backgrounds in light mode
- **Dark Background Colors**: Colors for backgrounds in dark mode
- **Action Colors**: Colors for interactive elements in light mode
- **Dark Action Colors**: Colors for interactive elements in dark mode
- **Custom Map Colors**: Colors specific to map components
- **Custom Node Map Colors**: Colors specific to node map components

Example of updating the primary color:

```typescript
// Before
primary: {
  main: '#1976d2',
  light: '#42a5f5',
  dark: '#1565c0',
  contrastText: '#ffffff',
},

// After
primary: {
  main: '#3f51b5', // New primary color
  light: '#757de8',
  dark: '#002984',
  contrastText: '#ffffff',
},
```

#### Typography

The typography settings are defined in the `typography` object. You can update:

- Font family
- Base font size
- Font weights
- Typography variants (h1, h2, h3, etc.)

Example of updating the font family:

```typescript
// Before
fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',

// After
fontFamily: '"Poppins", "Helvetica", "Arial", sans-serif', // New font family
```

#### Spacing

The base spacing unit is defined as a number (in pixels). All spacing in the application is calculated as multiples of this base unit.

Example of updating the spacing:

```typescript
// Before
export const spacing = 8; // 8px base unit

// After
export const spacing = 10; // 10px base unit
```

#### Shape

The shape settings define the border radius used throughout the application.

Example of updating the border radius:

```typescript
// Before
export const shape = {
  borderRadius: 4, // 4px border radius
};

// After
export const shape = {
  borderRadius: 8, // 8px border radius
};
```

#### Shadows

The shadows array defines the elevation shadows used throughout the application. Each index corresponds to a different elevation level.

#### Component Overrides

The component overrides allow you to customize specific MUI components. You can update:

- Button styles
- Card styles
- TextField styles
- Chip styles
- Dialog styles
- Alert styles

Example of updating the button border radius:

```typescript
// Before
MuiButton: {
  styleOverrides: {
    root: {
      borderRadius: 8,
      textTransform: 'none',
      fontWeight: 500,
    },
    // ...
  },
},

// After
MuiButton: {
  styleOverrides: {
    root: {
      borderRadius: 12, // New border radius
      textTransform: 'none',
      fontWeight: 500,
    },
    // ...
  },
},
```

### Testing Your Changes

After updating the design tokens, you can test your changes by:

1. Running the application (`npm run dev`)
2. Running Storybook (`npm run storybook`)

## For Developers

### How to Use the Theme

The theme is automatically applied to the entire application through the `ThemeProvider` in `src/app/layout.tsx`.

To use theme values in your components:

```tsx
import { useTheme } from "@mui/material/styles";

const MyComponent = () => {
  const theme = useTheme();

  return (
    <div
      style={{
        color: theme.palette.primary.main,
        padding: theme.spacing(2),
        borderRadius: theme.shape.borderRadius,
      }}
    >
      Themed Component
    </div>
  );
};
```

### Using the `sx` Prop

MUI components accept an `sx` prop that allows you to use theme values directly:

```tsx
<Button
  sx={{
    backgroundColor: "primary.main",
    "&:hover": {
      backgroundColor: "primary.dark",
    },
    px: 2, // padding-left and padding-right: theme.spacing(2)
    py: 1, // padding-top and padding-bottom: theme.spacing(1)
    borderRadius: "shape.borderRadius",
  }}
>
  Themed Button
</Button>
```

### Adding New Design Tokens

If you need to add new design tokens:

1. Add the token to `design-tokens.ts`
2. If necessary, extend the theme type in `theme.ts`
3. Use the token in the theme creation in `theme.ts`

## Resources

- [MUI Theming Documentation](https://mui.com/material-ui/customization/theming/)
- [MUI Default Theme](https://mui.com/material-ui/customization/default-theme/)
- [MUI Palette](https://mui.com/material-ui/customization/palette/)
- [MUI Typography](https://mui.com/material-ui/customization/typography/)
- [MUI Spacing](https://mui.com/material-ui/customization/spacing/)
- [MUI Breakpoints](https://mui.com/material-ui/customization/breakpoints/)
- [MUI Components](https://mui.com/material-ui/customization/theme-components/)

import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  Avatar,
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  CardHeader,
  CardMedia,
  IconButton,
  Typography,
} from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof Card> = {
  title: "Components/Card",
  component: Card,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["elevation", "outlined"],
      description: "The variant to use",
    },
    raised: {
      control: "boolean",
      description: "If true, the card will use raised styling",
    },
    onClick: { action: "clicked" },
  },
  decorators: [
    (Story) => (
      <Box sx={{ width: "350px" }}>
        <Story />
      </Box>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Card>;

// Basic card
export const Basic: Story = {
  args: {
    children: (
      <CardContent>
        <Typography variant="h5" component="div" gutterBottom>
          Basic Card
        </Typography>
        <Typography variant="body2" color="text.secondary">
          This is a basic card with some content. Cards contain content and actions about a single
          subject.
        </Typography>
      </CardContent>
    ),
  },
};

// Card with actions
export const WithActions: Story = {
  args: {
    children: (
      <>
        <CardContent>
          <Typography variant="h5" component="div" gutterBottom>
            Card with Actions
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This card has action buttons at the bottom. You can use CardActions to add buttons or
            other interactive elements.
          </Typography>
        </CardContent>
        <CardActions>
          <Button size="small">Learn More</Button>
          <Button size="small" color="primary">
            Share
          </Button>
        </CardActions>
      </>
    ),
  },
};

// Card with media
export const WithMedia: Story = {
  args: {
    children: (
      <>
        <CardMedia
          component="img"
          height="140"
          image="https://images.unsplash.com/photo-1551836022-d5d88e9218df?q=80&w=2070&auto=format&fit=crop"
          alt="Green landscape"
        />
        <CardContent>
          <Typography variant="h5" component="div" gutterBottom>
            Card with Media
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This card includes an image at the top. CardMedia can be used to display images, videos,
            or other media.
          </Typography>
        </CardContent>
        <CardActions>
          <Button size="small">View</Button>
        </CardActions>
      </>
    ),
  },
};

// Card with header
export const WithHeader: Story = {
  args: {
    children: (
      <>
        <CardHeader
          avatar={<Avatar aria-label="user">U</Avatar>}
          action={
            <IconButton aria-label="settings">
              <MoreVertIcon />
            </IconButton>
          }
          title="Card with Header"
          subheader="September 14, 2023"
        />
        <CardContent>
          <Typography variant="body2" color="text.secondary">
            This card has a header with an avatar, title, and action button. CardHeader is useful
            for displaying metadata about the card content.
          </Typography>
        </CardContent>
      </>
    ),
  },
};

// Outlined card
export const Outlined: Story = {
  args: {
    variant: "outlined",
    children: (
      <CardContent>
        <Typography variant="h5" component="div" gutterBottom>
          Outlined Card
        </Typography>
        <Typography variant="body2" color="text.secondary">
          This card uses the outlined variant, which gives it a border instead of a shadow.
        </Typography>
      </CardContent>
    ),
  },
};

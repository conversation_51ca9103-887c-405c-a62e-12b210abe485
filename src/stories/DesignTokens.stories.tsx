import { Box, Divider, Grid, Paper, Typography, useTheme } from "@mui/material";
import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import React from "react";
import {
  colors,
  shadows,
  shape,
  spacing,
  transitions,
  typography,
  zIndex,
} from "../styles/design-tokens";

// Component to display design tokens
const DesignTokensDisplay = () => {
  const theme = useTheme();

  // Color Swatch Component
  const ColorSwatch = ({ color, name }: { color: string; name: string }) => {
    // Determine if text should be light or dark based on background color
    const isLight = (color: string) => {
      // Simple check for light/dark colors
      if (color.startsWith("rgba")) {
        return true; // Assume light for transparent colors
      }

      // Convert hex to RGB
      let r, g, b;
      if (color.startsWith("#")) {
        const hex = color.substring(1);
        r = parseInt(hex.substring(0, 2), 16);
        g = parseInt(hex.substring(2, 4), 16);
        b = parseInt(hex.substring(4, 6), 16);
      } else {
        return true; // Default to light for unknown formats
      }

      // Calculate luminance
      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
      return luminance > 0.5;
    };

    return (
      <Paper
        sx={{
          p: 2,
          bgcolor: color,
          color: isLight(color) ? "rgba(0, 0, 0, 0.87)" : "white",
          height: "100px",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="subtitle2">{name}</Typography>
        <Typography variant="body2">{color}</Typography>
      </Paper>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="textPrimary">
        Design Tokens
      </Typography>
      <Typography variant="body1" paragraph color="textPrimary">
        This page displays all the design tokens used in the application. These tokens are defined
        in the <code>design-tokens.ts</code> file and are used to create the theme.
      </Typography>

      {/* Colors Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 4 }} color="textPrimary">
        Colors
      </Typography>
      <Divider sx={{ mb: 3 }} />

      {/* Primary Colors */}
      <Typography variant="h6" gutterBottom color="textPrimary">
        Primary Colors
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(colors.primary).map(([key, value]) => (
          <Grid item xs={6} sm={3} key={key}>
            <ColorSwatch color={value as string} name={`primary.${key}`} />
          </Grid>
        ))}
      </Grid>

      {/* Secondary Colors */}
      <Typography variant="h6" gutterBottom color="textPrimary">
        Secondary Colors
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(colors.secondary).map(([key, value]) => (
          <Grid item xs={6} sm={3} key={key}>
            <ColorSwatch color={value as string} name={`secondary.${key}`} />
          </Grid>
        ))}
      </Grid>

      {/* Error, Warning, Info, Success Colors */}
      <Typography variant="h6" gutterBottom color="textPrimary">
        Feedback Colors
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {["error", "warning", "info", "success"].map((colorType) => (
          <React.Fragment key={colorType}>
            {Object.entries(colors[colorType as keyof typeof colors]).map(([key, value]) => (
              <Grid item xs={6} sm={3} key={`${colorType}-${key}`}>
                <ColorSwatch color={value as string} name={`${colorType}.${key}`} />
              </Grid>
            ))}
          </React.Fragment>
        ))}
      </Grid>

      {/* Grey Colors */}
      <Typography variant="h6" gutterBottom color="textPrimary">
        Grey Palette
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(colors.grey).map(([key, value]) => (
          <Grid item xs={6} sm={3} md={2} key={key}>
            <ColorSwatch color={value as string} name={`grey.${key}`} />
          </Grid>
        ))}
      </Grid>

      {/* Text Colors */}
      <Typography variant="h6" gutterBottom color="textPrimary">
        Text Colors ({theme.palette.mode} mode)
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(theme.palette.text).map(([key, value]) => (
          <Grid item xs={6} sm={4} key={key}>
            <ColorSwatch color={value} name={`text.${key}`} />
          </Grid>
        ))}
      </Grid>

      {/* Background Colors */}
      <Typography variant="h6" gutterBottom color="textPrimary">
        Background Colors ({theme.palette.mode} mode)
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(theme.palette.background).map(([key, value]) => (
          <Grid item xs={6} sm={4} key={key}>
            <ColorSwatch color={value} name={`background.${key}`} />
          </Grid>
        ))}
      </Grid>

      {/* Custom Map Colors */}
      <Typography variant="h6" gutterBottom color="textPrimary">
        Custom Map Colors
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(theme.palette.map).map(([key, value]) => (
          <Grid item xs={6} sm={3} key={key}>
            <ColorSwatch color={value} name={`map.${key}`} />
          </Grid>
        ))}
      </Grid>

      {/* Custom Node Map Colors */}
      <Typography variant="h6" gutterBottom color="textPrimary">
        Custom Node Map Colors
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(theme.palette.nodeMap).map(([key, value]) => (
          <Grid item xs={6} sm={3} key={key}>
            <ColorSwatch color={value} name={`nodeMap.${key}`} />
          </Grid>
        ))}
      </Grid>

      {/* Typography Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 6 }} color="textPrimary">
        Typography
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Typography variant="h6" gutterBottom color="textPrimary">
        Font Family
      </Typography>
      <Paper sx={{ p: 2, mb: 4 }}>
        <Typography variant="body1" color="textPrimary">
          {typography.fontFamily}
        </Typography>
      </Paper>

      <Typography variant="h6" gutterBottom color="textPrimary">
        Font Weights
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="body1" fontWeight={typography.fontWeightLight} color="textPrimary">
              Light: {typography.fontWeightLight}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography
              variant="body1"
              fontWeight={typography.fontWeightRegular}
              color="textPrimary"
            >
              Regular: {typography.fontWeightRegular}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography
              variant="body1"
              fontWeight={typography.fontWeightMedium}
              color="textPrimary"
            >
              Medium: {typography.fontWeightMedium}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="body1" fontWeight={typography.fontWeightBold} color="textPrimary">
              Bold: {typography.fontWeightBold}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      <Typography variant="h6" gutterBottom color="textPrimary">
        Typography Variants
      </Typography>

      <Grid container spacing={2} sx={{ mb: 4 }}>
        {[
          { variant: "h1", label: "Heading 1" },
          { variant: "h2", label: "Heading 2" },
          { variant: "h3", label: "Heading 3" },
          { variant: "h4", label: "Heading 4" },
          { variant: "h5", label: "Heading 5" },
          { variant: "h6", label: "Heading 6" },
          { variant: "subtitle1", label: "Subtitle 1" },
          { variant: "subtitle2", label: "Subtitle 2" },
          { variant: "body1", label: "Body 1" },
          { variant: "body2", label: "Body 2" },
          { variant: "button", label: "Button" },
          { variant: "caption", label: "Caption" },
          { variant: "overline", label: "Overline" },
        ].map((item) => (
          <Grid item xs={12} key={item.variant}>
            <Paper sx={{ p: 2 }}>
              <Typography variant={item.variant as any} color="textPrimary">
                {item.label}
              </Typography>
              <Typography variant="caption" display="block" color="textSecondary">
                {item.variant}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* Spacing Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 6 }} color="textPrimary">
        Spacing
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Typography variant="h6" gutterBottom color="textPrimary">
        Base Spacing Unit: {spacing}px
      </Typography>
      <Typography variant="body1" paragraph color="textPrimary">
        All spacing in the application is calculated as multiples of this base unit.
      </Typography>

      <Grid container spacing={2} sx={{ mb: 4 }}>
        {[1, 2, 3, 4, 6, 8, 12].map((spacingUnit) => (
          <Grid item xs={6} sm={4} md={3} key={spacingUnit}>
            <Paper sx={{ p: 2, textAlign: "center" }}>
              <Box
                sx={{
                  width: "100%",
                  height: theme.spacing(spacingUnit),
                  bgcolor: "primary.main",
                  mb: 1,
                }}
              />
              <Typography variant="body2" color="textPrimary">
                {spacingUnit} ({theme.spacing(spacingUnit)})
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* Shape Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 6 }} color="textPrimary">
        Shape
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Typography variant="h6" gutterBottom color="textPrimary">
        Border Radius: {shape.borderRadius}px
      </Typography>
      <Typography variant="body1" paragraph color="textPrimary">
        The default border radius used throughout the application.
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {[
          0,
          shape.borderRadius / 2,
          shape.borderRadius,
          shape.borderRadius * 2,
          shape.borderRadius * 4,
          "50%",
        ].map((radius, index) => (
          <Grid item xs={6} sm={4} md={2} key={index}>
            <Paper
              sx={{
                p: 2,
                textAlign: "center",
                height: 100,
                width: 100,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: radius,
                mx: "auto",
              }}
            >
              <Typography variant="body2" color="textPrimary">
                {typeof radius === "string" ? radius : `${radius}px`}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* Shadows Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 6 }} color="textPrimary">
        Shadows
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {shadows.map((shadow, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Paper
              sx={{
                p: 3,
                height: 100,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                boxShadow: shadow,
              }}
            >
              <Typography color="textPrimary">Elevation {index}</Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* Transitions Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 6 }} color="textPrimary">
        Transitions
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Typography variant="h6" gutterBottom color="textPrimary">
        Easing Functions
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(transitions.easing).map(([key, value]) => (
          <Grid item xs={12} sm={6} key={key}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle2" color="textPrimary">
                {key}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {value}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>

      <Typography variant="h6" gutterBottom color="textPrimary">
        Duration (ms)
      </Typography>
      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(transitions.duration).map(([key, value]) => (
          <Grid item xs={6} sm={4} md={3} key={key}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle2" color="textPrimary">
                {key}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {value}ms
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* Z-Index Section */}
      <Typography variant="h5" gutterBottom sx={{ mt: 6 }} color="textPrimary">
        Z-Index
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <Grid container spacing={2} sx={{ mb: 4 }}>
        {Object.entries(zIndex).map(([key, value]) => (
          <Grid item xs={6} sm={4} md={3} key={key}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="subtitle2" color="textPrimary">
                {key}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {value}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

const meta: Meta<typeof DesignTokensDisplay> = {
  title: "Styles/Design Tokens",
  component: DesignTokensDisplay,
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;
type Story = StoryObj<typeof DesignTokensDisplay>;

export const Default: Story = {};

import BuildingBlockCard from "@/components/mapCanvas/buildingBlock/BuildingBlockCard";
import { BuildingBlock } from "@/types/map";
import type { Meta, StoryObj } from "@storybook/react";

// Mock data for Painpoint
const mockPainpoint: BuildingBlock = {
  id: 1,
  name: "Inefficient Lead Tracking",
  desc: "Lack of a centralized system to track and prioritize leads can lead to missed opportunities.",
  order: 26,
  vote: 42,
  phase: 1,
  type: 3,
  sub_type: 5,
  role: null,
  brand: null,
  segment: null,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
};

// Mock data for Opportunity
const mockOpportunity: BuildingBlock = {
  id: 2,
  name: "Personalized Home Loan Solutions",
  desc: "Leveraging customer data to provide tailored home loan solutions.",
  order: 15,
  vote: 38,
  phase: 2,
  type: 3,
  sub_type: 6,
  role: null,
  brand: null,
  segment: null,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
};

const meta: Meta<typeof BuildingBlockCard> = {
  title: "Composites/Map Canvas/Building Block Card",
  component: BuildingBlockCard,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#2d2d2d" },
        { name: "light", value: "#ffffff" },
      ],
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onClick: { action: "clicked" },
    onEdit: { action: "edited" },
    onDelete: { action: "deleted" },
    type: {
      control: { type: "select" },
      options: ["Painpoints", "Opportunity", "Operation context"],
    },
    variant: {
      control: { type: "select" },
      options: ["bordered", "default"],
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "400px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof BuildingBlockCard>;

// Painpoint variant
export const Painpoint: Story = {
  args: {
    buildingBlock: mockPainpoint,
    type: "Painpoints",
    variant: "bordered",
  },
};

// Opportunity variant
export const Opportunity: Story = {
  args: {
    buildingBlock: mockOpportunity,
    type: "Opportunity",
    variant: "bordered",
  },
};

// With interaction handlers
export const WithInteractions: Story = {
  args: {
    buildingBlock: mockPainpoint,
    type: "Painpoints",
    variant: "bordered",
    onClick: () => console.log("Card clicked"),
    onEdit: (newText: string) => console.log("Edited:", newText),
    onDelete: () => console.log("Deleted"),
  },
};

// With longer description text
export const LongDescription: Story = {
  args: {
    buildingBlock: {
      ...mockOpportunity,
      desc: "Leveraging customer data and financial expertise to provide tailored home loan solutions. This includes analyzing customer financial history, current income, debt-to-income ratios, and future financial goals to create personalized loan packages that meet their specific needs and circumstances.",
    },
    type: "Opportunity",
    variant: "bordered",
    onClick: () => console.log("Card clicked"),
    onEdit: (newText: string) => console.log("Edited:", newText),
    onDelete: () => console.log("Deleted"),
  },
};

// Operation context variant (no borders)
export const OperationContext: Story = {
  args: {
    buildingBlock: {
      ...mockPainpoint,
      name: "Customer Onboarding Process",
      desc: "The process of onboarding new customers includes identity verification, account setup, and initial product recommendations.",
    },
    type: "Operation context",
    variant: "default",
    onClick: () => console.log("Card clicked"),
    onEdit: (newText: string) => console.log("Edited:", newText),
    onDelete: () => console.log("Deleted"),
  },
};

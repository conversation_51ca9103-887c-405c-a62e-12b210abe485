import SearchIcon from "@mui/icons-material/Search";
import { Box, InputAdornment, TextField } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof TextField> = {
  title: "Components/TextField",
  component: TextField,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["outlined", "filled", "standard"],
      description: "The variant to use",
    },
    size: {
      control: "select",
      options: ["small", "medium"],
      description: "The size of the component",
    },
    color: {
      control: "select",
      options: ["primary", "secondary", "error", "info", "success", "warning"],
      description: "The color of the component",
    },
    disabled: {
      control: "boolean",
      description: "If true, the component is disabled",
    },
    required: {
      control: "boolean",
      description: "If true, the label is displayed as required",
    },
    fullWidth: {
      control: "boolean",
      description: "If true, the input will take up the full width of its container",
    },
    onChange: { action: "changed" },
  },
  decorators: [
    (Story) => (
      <Box sx={{ width: "300px" }}>
        <Story />
      </Box>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof TextField>;

// Default text field
export const Default: Story = {
  args: {
    label: "Label",
    placeholder: "Placeholder",
  },
};

// Outlined variant
export const Outlined: Story = {
  args: {
    label: "Outlined",
    variant: "outlined",
    placeholder: "Placeholder",
  },
};

// Filled variant
export const Filled: Story = {
  args: {
    label: "Filled",
    variant: "filled",
    placeholder: "Placeholder",
  },
};

// Standard variant
export const Standard: Story = {
  args: {
    label: "Standard",
    variant: "standard",
    placeholder: "Placeholder",
  },
};

// Required field
export const Required: Story = {
  args: {
    label: "Required Field",
    required: true,
    placeholder: "Placeholder",
  },
};

// Disabled field
export const Disabled: Story = {
  args: {
    label: "Disabled Field",
    disabled: true,
    placeholder: "Placeholder",
  },
};

// With helper text
export const WithHelperText: Story = {
  args: {
    label: "With Helper Text",
    helperText: "Some helper text",
    placeholder: "Placeholder",
  },
};

// With error
export const WithError: Story = {
  args: {
    label: "Error Field",
    error: true,
    helperText: "Error message",
    placeholder: "Placeholder",
  },
};

// With start adornment
export const WithStartAdornment: Story = {
  args: {
    label: "Search",
    placeholder: "Search...",
    InputProps: {
      startAdornment: (
        <InputAdornment position="start">
          <SearchIcon />
        </InputAdornment>
      ),
    },
  },
};

// With end adornment
export const WithEndAdornment: Story = {
  args: {
    label: "Amount",
    placeholder: "0.00",
    InputProps: {
      endAdornment: <InputAdornment position="end">$</InputAdornment>,
    },
  },
};

// Multiline text field
export const Multiline: Story = {
  args: {
    label: "Multiline",
    multiline: true,
    rows: 4,
    placeholder: "Enter multiple lines of text",
  },
};

// Small size
export const Small: Story = {
  args: {
    label: "Small Size",
    size: "small",
    placeholder: "Placeholder",
  },
};

// Full width
export const FullWidth: Story = {
  args: {
    label: "Full Width",
    fullWidth: true,
    placeholder: "This field takes up the full width",
  },
};

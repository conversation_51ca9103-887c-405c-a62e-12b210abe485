import DeleteIcon from "@mui/icons-material/Delete";
import FaceIcon from "@mui/icons-material/Face";
import { Avatar, Chip, Stack } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof Chip> = {
  title: "Components/Chip",
  component: Chip,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["filled", "outlined"],
      description: "The variant to use",
    },
    color: {
      control: "select",
      options: ["default", "primary", "secondary", "success", "error", "info", "warning"],
      description: "The color of the component",
    },
    size: {
      control: "select",
      options: ["small", "medium"],
      description: "The size of the component",
    },
    disabled: {
      control: "boolean",
      description: "If true, the component is disabled",
    },
    onDelete: { action: "deleted" },
    onClick: { action: "clicked" },
  },
  decorators: [
    (Story) => (
      <div style={{ padding: "1rem" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Chip>;

// Default chip
export const Default: Story = {
  args: {
    label: "Chip",
  },
};

// Outlined chip
export const Outlined: Story = {
  args: {
    label: "Outlined Chip",
    variant: "outlined",
  },
};

// Clickable chip
export const Clickable: Story = {
  args: {
    label: "Clickable Chip",
    onClick: () => console.log("Chip clicked"),
  },
};

// Deletable chip
export const Deletable: Story = {
  args: {
    label: "Deletable Chip",
    onDelete: () => console.log("Chip deleted"),
  },
};

// Chip with icon
export const WithIcon: Story = {
  args: {
    label: "With Icon",
    icon: <FaceIcon />,
  },
};

// Chip with avatar
export const WithAvatar: Story = {
  args: {
    label: "With Avatar",
    avatar: <Avatar>M</Avatar>,
  },
};

// Chip with delete icon
export const WithCustomDeleteIcon: Story = {
  args: {
    label: "Custom Delete Icon",
    onDelete: () => console.log("Chip deleted"),
    deleteIcon: <DeleteIcon />,
  },
};

// Small chip
export const Small: Story = {
  args: {
    label: "Small Chip",
    size: "small",
  },
};

// Disabled chip
export const Disabled: Story = {
  args: {
    label: "Disabled Chip",
    disabled: true,
  },
};

// Primary color
export const Primary: Story = {
  args: {
    label: "Primary",
    color: "primary",
  },
};

// Secondary color
export const Secondary: Story = {
  args: {
    label: "Secondary",
    color: "secondary",
  },
};

// Success color
export const Success: Story = {
  args: {
    label: "Success",
    color: "success",
  },
};

// Error color
export const Error: Story = {
  args: {
    label: "Error",
    color: "error",
  },
};

// Chip with custom styling
export const CustomStyling: Story = {
  args: {
    label: "Custom Style",
    sx: {
      backgroundColor: "#6a1b9a",
      color: "white",
      fontWeight: "bold",
      "& .MuiChip-deleteIcon": {
        color: "white",
      },
    },
    onDelete: () => console.log("Chip deleted"),
  },
};

// Multiple chips in a group
export const ChipGroup: Story = {
  render: () => (
    <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
      <Chip label="React" color="primary" />
      <Chip label="Next.js" color="secondary" />
      <Chip label="TypeScript" color="info" />
      <Chip label="Material UI" color="success" />
      <Chip label="Storybook" color="warning" />
    </Stack>
  ),
};

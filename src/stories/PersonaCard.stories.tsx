import PersonaCard from "@/app/persona/components/PersonaCard";
import { action } from "@storybook/addon-actions";
import type { Meta, StoryObj } from "@storybook/react";

const meta: Meta<typeof PersonaCard> = {
  title: "Composites/Persona/Persona Card",
  component: PersonaCard,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onClick: { action: "clicked" },
    onEdit: { action: "edit clicked" },
    onDelete: { action: "delete clicked" },
    onRegenerateImage: { action: "regenerate image clicked" },
    onRegeneratePersona: { action: "regenerate persona clicked" },
  },
};

export default meta;
type Story = StoryObj<typeof PersonaCard>;

// Default state with all properties
export const Default: Story = {
  args: {
    name: "<PERSON>",
    ageRange: "25-30",
    gender: "Female",
    profession: "Software Engineer",
    image: "https://picsum.photos/300/300",
    isGeneratingImage: false,
    onClick: action("clicked"),
    onEdit: action("edit clicked"),
    onDelete: action("delete clicked"),
    onRegenerateImage: action("regenerate image clicked"),
    onRegeneratePersona: action("regenerate persona clicked"),
  },
};

// Loading state while generating image
export const GeneratingImage: Story = {
  args: {
    ...Default.args,
    isGeneratingImage: true,
  },
};

// Without image
export const NoImage: Story = {
  args: {
    ...Default.args,
    image: undefined,
  },
};

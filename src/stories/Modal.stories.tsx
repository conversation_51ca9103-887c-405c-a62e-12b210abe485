import Modal from "@/components/Modal";
import { Box, Button, TextField, Typography } from "@mui/material";
import type { Meta, StoryObj } from "@storybook/react";
import { useState } from "react";

// Wrapper component to handle modal state
const ModalWrapper = ({
  title,
  children,
  maxWidth,
}: {
  title: string;
  children: React.ReactNode;
  maxWidth?: number;
}) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button variant="contained" onClick={() => setOpen(true)}>
        Open Modal
      </Button>
      <Modal open={open} onClose={() => setOpen(false)} title={title} maxWidth={maxWidth}>
        {children}
      </Modal>
    </>
  );
};

const meta: Meta<typeof ModalWrapper> = {
  title: "Components/Modal",
  component: ModalWrapper,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    title: {
      control: "text",
      description: "The title of the modal",
    },
    maxWidth: {
      control: "number",
      description: "The maximum width of the modal",
    },
  },
};

export default meta;
type Story = StoryObj<typeof ModalWrapper>;

// Default modal
export const Default: Story = {
  args: {
    title: "Modal Title",
    children: (
      <Box>
        <Typography variant="body1" color="textPrimary" paragraph>
          This is a basic modal with some content.
        </Typography>
        <Typography variant="body1" color="textPrimary" paragraph>
          Click outside or press ESC to close.
        </Typography>
      </Box>
    ),
  },
};

// Form modal
export const FormModal: Story = {
  args: {
    title: "Contact Form",
    children: (
      <Box component="form" sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <TextField label="Name" fullWidth />
        <TextField label="Email" type="email" fullWidth />
        <TextField label="Message" multiline rows={4} fullWidth />
        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1, mt: 2 }}>
          <Button variant="text">Cancel</Button>
          <Button variant="contained">Submit</Button>
        </Box>
      </Box>
    ),
  },
};

// Confirmation modal
export const ConfirmationModal: Story = {
  args: {
    title: "Confirm Action",
    maxWidth: 500,
    children: (
      <Box>
        <Typography variant="body1" color="textPrimary" paragraph>
          Are you sure you want to proceed with this action?
        </Typography>
        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1, mt: 2 }}>
          <Button variant="text">Cancel</Button>
          <Button variant="contained" color="error">
            Confirm
          </Button>
        </Box>
      </Box>
    ),
  },
};

// Large content modal
export const LargeContentModal: Story = {
  args: {
    title: "Terms and Conditions",
    children: (
      <Box>
        <Typography variant="body1" color="textPrimary" paragraph>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget
          ultricies aliquam, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl. Nullam
          euismod, nisl eget ultricies aliquam, nunc nisl aliquet nunc, quis aliquam nisl nunc quis
          nisl.
        </Typography>
        <Typography variant="body1" color="textPrimary" paragraph>
          Nullam euismod, nisl eget ultricies aliquam, nunc nisl aliquet nunc, quis aliquam nisl
          nunc quis nisl. Nullam euismod, nisl eget ultricies aliquam, nunc nisl aliquet nunc, quis
          aliquam nisl nunc quis nisl.
        </Typography>
        <Typography variant="body1" color="textPrimary" paragraph>
          Nullam euismod, nisl eget ultricies aliquam, nunc nisl aliquet nunc, quis aliquam nisl
          nunc quis nisl. Nullam euismod, nisl eget ultricies aliquam, nunc nisl aliquet nunc, quis
          aliquam nisl nunc quis nisl.
        </Typography>
        <Typography variant="body1" color="textPrimary" paragraph>
          Nullam euismod, nisl eget ultricies aliquam, nunc nisl aliquet nunc, quis aliquam nisl
          nunc quis nisl. Nullam euismod, nisl eget ultricies aliquam, nunc nisl aliquet nunc, quis
          aliquam nisl nunc quis nisl.
        </Typography>
        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1, mt: 2 }}>
          <Button variant="text">Decline</Button>
          <Button variant="contained" color="primary">
            Accept
          </Button>
        </Box>
      </Box>
    ),
  },
};

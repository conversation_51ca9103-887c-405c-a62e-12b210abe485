import ActivityCard from "@/components/mapCanvas/activity/ActivityCard";
import { Activity } from "@/types/api/client";
import type { Meta, StoryObj } from "@storybook/react";

// Mock user reference
const mockUserRef = {
  user_id: "usr_john_doe",
  name: "<PERSON>",
  profile_image_url: "",
  timestamp: "2024-01-15T10:30:00Z"
};

// Mock data  
const mockActivity = {
  activity_id: "act_customer_inquiry",
  organization_id: "acme-financial-2024", 
  moment_id: "moment_initial_contact",
  name: "Customer Account Inquiry",
  description: "The customer inquires about opening a new savings account.",
  activity_type: "customer",
  estimated_duration: "5-10 minutes",
  complexity_level: "low",
  personas: ["persona_new_customer"],
  sequence: 1,
  status: "active",
  created_by: mockUserRef,
  updated_by: mockUserRef
} as Activity;

// Mock attachments
const mockAttachments: { name: string; image: string; status: string }[] = [
  {
    name: "Customer Record System",
    image:
      "https://wac-cdn.atlassian.com/dam/jcr:************************a470428cb6cd/Agile-report.png",
    status: "scheduled",
  },
  {
    name: "Customer Application Tracking",
    image:
      "https://wac-cdn.atlassian.com/dam/jcr:************************a470428cb6cd/Agile-report.png",
    status: "pending",
  },
  {
    name: "Additional Document",
    image:
      "https://wac-cdn.atlassian.com/dam/jcr:************************a470428cb6cd/Agile-report.png",
    status: "scheduled",
  },
];

const meta: Meta<typeof ActivityCard> = {
  title: "Composites/Map Canvas/Activity Card",
  component: ActivityCard,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#1a1a1a" },
        { name: "light", value: "#ffffff" },
      ],
    } as any,
  },
  tags: ["autodocs"],
  argTypes: {
    onClick: { action: "clicked" },
    onEdit: { action: "edited" },
    onDelete: { action: "deleted" },
    onEditPersona: { action: "persona edited" },
    attachments: {
      control: "object",
      description: "Array of attachment objects with name, image, and status properties",
    } as any,
  },
  decorators: [
    (Story) => (
      <div style={{ width: "400px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof ActivityCard>;

// Default state
export const Default: Story = {
  args: {
    activity: mockActivity as any,
  },
};

// With longer description text
export const LongDescription: Story = {
  args: {
    activity: {
      ...mockActivity,
      description: "The customer inquires about opening a new savings account with competitive interest rates. They also want to know about minimum deposit requirements, monthly fees, and whether there are any promotional offers available for new accounts.",
    } as any,
    onClick: () => console.log("Card clicked"),
    onEdit: (newText) => console.log("Edited:", newText),
    onDelete: () => console.log("Deleted"),
  },
};

// Without role image
export const NoRoleImage: Story = {
  args: {
    activity: {
      ...mockActivity,
      role: {
        id: 2,
        name: "Bank Representative",
        image: "",
        description: "",
        order: 0,
        map_role_id: 0,
        type: "",
      } as any,
    } as any,
  },
};

export const WithAttachments: Story = {
  args: {
    activity: {
      ...mockActivity,
      description: "I gather offline leads from my network, existing customers and the wider team",
    } as any,
    attachments: mockAttachments,
    onClick: () => console.log("Card clicked"),
    onEdit: (newText) => console.log("Edited:", newText),
    onDelete: () => console.log("Deleted"),
  },
};

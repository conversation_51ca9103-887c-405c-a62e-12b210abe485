import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  CircularProgress,
  IconButton,
  Menu,
  MenuItem,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

interface PersonaCardProps {
  image?: string;
  name: string;
  ageRange: string;
  gender: string;
  profession: string;
  isGeneratingImage?: boolean;
  onClick?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onRegenerateImage?: () => void;
  onRegeneratePersona?: () => void;
}

const PersonaCard: React.FC<PersonaCardProps> = ({
  image,
  name,
  ageRange,
  gender,
  profession,
  isGeneratingImage = false,
  onClick,
  onEdit,
  onDelete,
  onRegenerateImage,
  onRegeneratePersona,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation(); // Prevent card click
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleAction = (callback?: () => void) => (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    handleMenuClose();
    callback?.();
  };

  return (
    <Card
      sx={{
        width: "100%",
        cursor: "pointer",
        "&:hover": {
          boxShadow: 6,
        },
      }}
      onClick={onClick}
    >
      <CardMedia
        component="div"
        sx={{
          height: 300,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          bgcolor: "action.hover",
          color: "text.secondary",
          position: "relative",
        }}
      >
        {isGeneratingImage ? (
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              zIndex: 1,
            }}
          >
            <CircularProgress />
          </Box>
        ) : null}
        {image ? (
          <img
            src={image}
            alt={name}
            style={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
            }}
            onError={(e) => {
              e.currentTarget.style.display = "none";
              e.currentTarget.parentElement!.textContent = "Image not found";
            }}
          />
        ) : (
          "Image not found"
        )}
      </CardMedia>
      <CardContent sx={{ position: "relative" }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Typography variant="h6" component="div" sx={{ fontWeight: "bold" }}>
            {name}
          </Typography>
          <IconButton size="small" aria-label="settings" onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            onClick={(e) => e.stopPropagation()}
          >
            <MenuItem onClick={handleAction(onEdit)}>Edit</MenuItem>
            <MenuItem onClick={handleAction(onRegenerateImage)}>Regenerate image</MenuItem>
            <MenuItem onClick={handleAction(onRegeneratePersona)}>Regenerate persona</MenuItem>
            <MenuItem onClick={handleAction(onDelete)}>Delete</MenuItem>
          </Menu>
        </Box>
        <Typography variant="body2" color="text.secondary">
          {`${ageRange} ages, ${gender}, ${profession}`}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default PersonaCard;

import FormBuilder from "@/components/FormBuilder";
import { useAppContext } from "@/context/AppProvider";
import personaService from "@/services/PersonaService";
import { PersonaData } from "@/types/persona";
import { Box, Divider, Modal, Typography } from "@mui/material";
import React, { useState } from "react";
import { getCustomerFormGroups, getStaffFormGroups } from "../forms/PersonaForm";

interface PersonaEditModalProps {
  open: boolean;
  onClose: () => void;
  profile: PersonaData;
  isCustomer?: boolean;
  onSuccess?: (updatedPersona: PersonaData) => void;
}

const PersonaEditModal: React.FC<PersonaEditModalProps> = ({
  open,
  onClose,
  profile,
  isCustomer = true,
  onSuccess,
}) => {
  const { commonData } = useAppContext();
  const [formValues, setFormValues] = useState(profile);

  const handleFormChange = (values: any) => {
    setFormValues(values);
  };

  const handleSubmit = async (_formData: PersonaData, changedFields?: Record<string, any>) => {
    try {
      if (changedFields && Object.keys(changedFields).length > 0) {
        // Ensure goals and frustrations are strings
        const normalizedChanges = {
          ...changedFields,
          goals: changedFields.goals?.toString() || undefined,
          frustrations: changedFields.frustrations?.toString() || undefined,
        };

        const updatedPersona = await personaService.updateRole(profile.id, normalizedChanges);
        onSuccess?.(updatedPersona);
      }
      onClose();
    } catch (error) {
      console.error("Failed to update persona:", error);
    }
  };

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="edit-persona-modal">
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          color: "white",
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 8,
          padding: 3,
          width: "100%",
          maxWidth: 900,
          maxHeight: "90vh",
          overflow: "auto",
          outline: "none",
        }}
      >
        <Typography variant="h5" mb={2}>
          Edit persona
        </Typography>
        <Divider sx={{ mb: 3 }} />
        <FormBuilder
          groups={
            isCustomer
              ? getCustomerFormGroups(commonData, formValues)
              : getStaffFormGroups(commonData, formValues)
          }
          initialValues={profile}
          onSubmit={handleSubmit}
          onCancel={onClose}
          submitButtonText="Save changes"
          trackChanges={true}
          onChange={handleFormChange}
        />
      </Box>
    </Modal>
  );
};

export default PersonaEditModal;

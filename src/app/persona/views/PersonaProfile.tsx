import { PersonaData } from "@/types/persona";
import AutoAwesomeIcon from "@mui/icons-material/AutoAwesome";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  Avatar,
  Box,
  CircularProgress,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import PersonaEditModal from "./PersonaEditModal";

interface PersonaProfileProps {
  persona: PersonaData;
  onDelete?: () => void;
  onUpdate?: (updatedPersona: PersonaData) => void;
  onGenerateImage?: () => void;
  isGeneratingImage?: boolean;
}

const PersonaProfile: React.FC<PersonaProfileProps> = ({
  persona,
  onDelete,
  onUpdate,
  onGenerateImage,
  isGeneratingImage = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleEditClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setIsEditModalOpen(true);
    handleMenuClose();
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
  };

  const handleRegeneratePersona = () => {
    handleMenuClose();
    // Add regenerate persona logic
  };

  const handleEditSuccess = async (updatedPersona: PersonaData) => {
    onUpdate?.(updatedPersona);
    setIsEditModalOpen(false);
  };

  const handleGenerateImage = async () => {
    onGenerateImage?.();
  };

  const handleRegenerateImage = () => {
    handleMenuClose();
    handleGenerateImage();
  };

  const isCustomer = persona.profession === "Customer";

  return (
    <Box
      sx={{
        bgcolor: "#1e1e1e",
        color: "white",
        borderRadius: "40px",
        padding: 5,
        width: "100%",
        margin: "30px auto",
      }}
    >
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
        {/* Left section - Avatar and Name */}
        <Stack direction="row" spacing={4} width="100%" alignItems="center">
          <Box sx={{ position: "relative" }}>
            <Avatar
              src={persona.image}
              alt={persona.name}
              sx={{
                width: 200,
                height: 200,
              }}
            />
            {isGeneratingImage ? (
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <CircularProgress />
              </Box>
            ) : (
              !persona.image && (
                <IconButton
                  onClick={handleGenerateImage}
                  sx={{
                    position: "absolute",
                    top: 8,
                    right: 8,
                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.7)",
                    },
                  }}
                >
                  <AutoAwesomeIcon sx={{ color: "white" }} />
                </IconButton>
              )
            )}
          </Box>
          <Stack spacing={1} width="100%" justifyContent="space-between" height="120px">
            <Typography variant="h4" gutterBottom>
              {persona.name}
            </Typography>

            {/* Info rows */}
            <Stack spacing={2} direction="row" justifyContent="space-between" mt={5}>
              <Stack spacing={1} width="33%">
                <InfoRow label="Job:" value={persona.job} />
                <InfoRow label="Gender:" value={persona.gender} />
              </Stack>
              <Stack spacing={1} width="33%">
                <InfoRow label="Age:" value={persona.ageRange} />
                {persona.status && <InfoRow label="Status:" value={persona.status} />}
              </Stack>
              <Stack spacing={1} width="33%">
                {persona.income && <InfoRow label="Income:" value={persona.income} />}
                {persona.location && <InfoRow label="Location:" value={persona.location} />}
              </Stack>
            </Stack>
          </Stack>
        </Stack>

        {/* Menu button */}
        <IconButton onClick={handleMenuClick} sx={{ color: "white" }}>
          <MoreVertIcon />
        </IconButton>
      </Stack>

      {/* About section */}
      <Box mt={4}>
        <Typography variant="h6" fontWeight="bold" mb={2}>
          About
        </Typography>
        {persona.about &&
          persona.about.split("\n").map((line, index) => (
            <Typography key={index} variant="body1">
              {line}
            </Typography>
          ))}
      </Box>

      {/* Goals and Frustrations */}
      <Stack direction="row" spacing={8} mt={4}>
        {persona.goals && persona.goals.length > 0 && (
          <Box flex={1}>
            <Typography variant="h6" fontWeight="bold" mb={2}>
              Goals
            </Typography>
            <Stack spacing={1}>
              {persona.goals.split("\n").map((goal, index) => (
                <Typography key={index} variant="body1">
                  {goal}
                </Typography>
              ))}
            </Stack>
          </Box>
        )}

        {persona.frustrations && (
          <Box flex={1}>
            <Typography variant="h6" fontWeight="bold" mb={2}>
              Fraustration
            </Typography>
            <Stack spacing={1}>
              {persona.frustrations.split("\n").map((frustration, index) => (
                <Typography key={index} variant="body1">
                  {frustration}
                </Typography>
              ))}
            </Stack>
          </Box>
        )}
      </Stack>

      {/* Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
      >
        <MenuItem onClick={handleEditClick}>Edit</MenuItem>
        <MenuItem onClick={handleRegenerateImage}>Regenerate image</MenuItem>
        <MenuItem onClick={handleRegeneratePersona}>Regenerate persona</MenuItem>
        <MenuItem onClick={onDelete}>Delete</MenuItem>
      </Menu>

      {/* Edit Modal */}
      <PersonaEditModal
        open={isEditModalOpen}
        onClose={handleCloseEditModal}
        profile={persona}
        isCustomer={isCustomer}
        onSuccess={handleEditSuccess}
      />
    </Box>
  );
};

// Helper component for info rows
const InfoRow = ({ label, value }: { label: string; value: string }) => (
  <Stack direction="row" spacing={1}>
    <Typography variant="body1" fontWeight="bold">
      {label}
    </Typography>
    <Typography variant="body1">{value}</Typography>
  </Stack>
);

export default PersonaProfile;

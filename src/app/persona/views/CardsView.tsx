import { PersonaData } from "@/types/persona";
import { Grid } from "@mui/material";
import React from "react";
import PersonaCard from "../components/PersonaCard";

interface CardsViewProps {
  data: PersonaData[];
  handleClick: (persona: PersonaData) => void;
  handleDelete: (id: number, type: string) => void;
  handleGenerateImage: (persona: PersonaData) => void;
  generatingImageIds: Set<number>;
}

const CardsView: React.FC<CardsViewProps> = ({
  data,
  handleClick,
  handleDelete,
  handleGenerateImage,
  generatingImageIds,
}) => {
  return (
    <Grid container spacing={3}>
      {data?.map((persona) => (
        <Grid item xs={12} sm={6} md={4} key={persona.id}>
          <PersonaCard
            {...persona}
            isGeneratingImage={generatingImageIds.has(persona.id)}
            onClick={() => handleClick(persona)}
            onDelete={() => handleDelete(persona.id, persona.profession)}
            onRegenerateImage={() => handleGenerateImage(persona)}
          />
        </Grid>
      ))}
    </Grid>
  );
};

export default CardsView;

import FormBuilder from "@/components/FormBuilder";
import { useAppContext } from "@/context/AppProvider";
import personaService from "@/services/PersonaService";
import { PersonaData } from "@/types/persona";
import { Box, Divider, Modal, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { getCustomerFormGroups, getStaffFormGroups } from "../forms/PersonaForm";

interface PersonaCreateModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (createdPersona: any) => void;
}

const PersonaCreateModal: React.FC<PersonaCreateModalProps> = ({ open, onClose, onConfirm }) => {
  const { commonData } = useAppContext();
  const [selectedRoleType, setSelectedRoleType] = useState<number | null>(null);
  const [customerRoleId, setCustomerRoleId] = useState<number | null>(null);
  const [frontStageRoleId, setFrontStageRoleId] = useState<number | null>(null);
  const [backStageRoleId, setBackStageRoleId] = useState<number | null>(null);
  const [formKey, setFormKey] = useState(0);
  const [formValues, setFormValues] = useState<any>({});

  useEffect(() => {
    if (commonData.role_types.length > 0) {
      const customerRole = commonData.role_types.find((role) => role.name === "Customer");
      const frontStageRole = commonData.role_types.find((role) => role.name === "Front stage");
      const backStageRole = commonData.role_types.find((role) => role.name === "Back stage");

      if (customerRole) {
        setCustomerRoleId(customerRole.id);
        setSelectedRoleType((prev) => prev ?? customerRole.id);
      }

      if (frontStageRole) {
        setFrontStageRoleId(frontStageRole.id);
      }

      if (backStageRole) {
        setBackStageRoleId(backStageRole.id);
      }
    }
  }, [commonData.role_types]);

  const handleRoleTypeChange = (values: any) => {
    setFormValues(values);
    if (values.profession !== selectedRoleType) {
      setSelectedRoleType(values.profession);
      setFormKey((prev) => prev + 1);
    }
  };

  const getFormGroups = () => {
    const isCustomer = selectedRoleType === customerRoleId;
    const isStaffRole =
      selectedRoleType === frontStageRoleId || selectedRoleType === backStageRoleId;

    const baseFields = [
      {
        name: "profession",
        type: "select" as const,
        label: "Role type",
        options: commonData.role_types.map((role) => ({
          label: role.name,
          value: role.id,
        })),
        validation: { required: true },
      },
    ];

    if (isCustomer) {
      const customerGroups = getCustomerFormGroups(commonData, formValues);
      return [
        { name: "", fields: [...baseFields, ...customerGroups[0].fields] },
        customerGroups[1],
      ];
    } else if (isStaffRole) {
      const staffGroups = getStaffFormGroups(commonData, formValues);
      return [{ name: "", fields: [...baseFields, ...staffGroups[0].fields] }, staffGroups[1]];
    }

    return [
      { name: "", fields: baseFields },
      { name: "", fields: [] },
    ];
  };

  const getInitialValues = () => {
    return {
      profession: selectedRoleType || customerRoleId || 0,
      name: "",
      ageRange: "",
      gender: "",
      job: "",
      income: "",
      status: "",
      location: "",
      about: "",
      goals: "",
      frustrations: "",
    };
  };

  const handleSubmit = async (formData: PersonaData) => {
    try {
      const createdPersona = await personaService.createRole(formData);
      onConfirm?.(createdPersona);
      onClose();
    } catch (error) {
      console.error("Failed to create persona:", error);
    }
  };

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="create-persona-modal">
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          color: "white",
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 8,
          padding: 3,
          width: "100%",
          maxWidth: 900,
          maxHeight: "90vh",
          overflow: "auto",
          outline: "none",
        }}
      >
        <Typography variant="h5" mb={2}>
          Create persona
        </Typography>
        <Divider sx={{ mb: 3 }} />
        <FormBuilder
          key={formKey}
          groups={getFormGroups()}
          initialValues={getInitialValues()}
          onSubmit={handleSubmit}
          onCancel={onClose}
          submitButtonText="Generate a persona"
          onChange={handleRoleTypeChange}
        />
      </Box>
    </Modal>
  );
};

export default PersonaCreateModal;

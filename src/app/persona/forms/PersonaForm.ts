/**
 * @deprecated This form configuration is deprecated. Use the new API v2 persona hooks instead.
 * Migration: Replace with the new `usePersonas`, `useCreatePersona`, etc. hooks from '@/api/v2/hooks'
 * 
 * The new API supports multiple persona types and better type safety.
 * @see /src/api/v2/hooks/usePersonas.ts for the new implementation
 */

import aiService from "@/services/AIService";
import { CommonData } from "@/types/common";

// Helper function to get string values from IDs
const getStringValue = (
  value: any,
  options: any[],
  idField: string = "id",
  nameField: string = "name"
) => {
  if (!value) return "";

  // If value is already a string and not a numeric string, return it
  if (typeof value === "string" && isNaN(Number(value))) {
    return value;
  }

  // Find the option with matching ID and return its name
  const option = options.find((opt) => opt[idField] === value || opt[idField] === Number(value));
  return option ? option[nameField] : value.toString();
};

export const getCustomerFormGroups = (commonData: CommonData, formValues?: any) => {
  const customerLeftFields = [
    {
      name: "name",
      type: "text" as const,
      label: "Name",
      validation: { required: true },
    },
    {
      name: "gender",
      type: "select" as const,
      label: "Gender",
      options: commonData.genders.map((gender) => ({
        label: gender.label,
        value: gender.value,
      })),
      validation: { required: true },
    },
    {
      name: "ageRange",
      type: "select" as const,
      label: "Age",
      options: commonData.age_ranges.map((age) => ({
        label: age.name,
        value: age.id,
      })),
      renderValue: (value: any) => {
        const option = commonData.age_ranges.find((age) => age.name === value || age.id === value);
        return option?.name || "";
      },
      validation: { required: true },
    },
    {
      name: "status",
      type: "select" as const,
      label: "Status",
      options: commonData.role_statuses.map((status) => ({
        label: status.name,
        value: status.id,
      })),
      renderValue: (value: any) => {
        const option = commonData.role_statuses.find(
          (status) => status.name === value || status.id === value
        );
        return option?.name || "";
      },
      validation: { required: true },
    },
    {
      name: "location",
      type: "text" as const,
      label: "Location",
      validation: { required: false },
    },
  ];

  const customerRightFields = [
    {
      name: "income",
      type: "select" as const,
      label: "Income",
      options: commonData.income_types.map((income) => ({
        label: income.name,
        value: income.id,
      })),
      renderValue: (value: any) => {
        const option = commonData.income_types.find(
          (income) => income.name === value || income.id === value
        );
        return option?.name || "";
      },
      validation: { required: true },
    },
    {
      name: "job",
      type: "text" as const,
      label: "What is their job?",
      validation: { required: true },
    },
    {
      name: "about",
      type: "aiTextarea" as const,
      label: "Describe the persona",
      validation: { required: true },
      rowSpan: 4,
      aiHandler: async () => {
        const roleData = {
          name: formValues?.name || "",
          gender: formValues?.gender || "",
          age: getStringValue(formValues?.ageRange, commonData.age_ranges),
          status: getStringValue(formValues?.status, commonData.role_statuses),
          income: getStringValue(formValues?.income, commonData.income_types),
          job: formValues?.job || "",
          location: formValues?.location || "",
          type: "Customer",
        };

        return await aiService.generateText("persona description", { role: roleData });
      },
    },
    {
      name: "goals",
      type: "aiTextarea" as const,
      label: "Goals",
      validation: { required: false },
      rowSpan: 3,
      aiHandler: async () => {
        const roleData = {
          name: formValues?.name || "",
          gender: formValues?.gender || "",
          age: getStringValue(formValues?.ageRange, commonData.age_ranges),
          status: getStringValue(formValues?.status, commonData.role_statuses),
          income: getStringValue(formValues?.income, commonData.income_types),
          job: formValues?.job || "",
          location: formValues?.location || "",
          type: "Customer",
        };

        return await aiService.generateText("persona goal", { role: roleData });
      },
    },
    {
      name: "frustrations",
      type: "aiTextarea" as const,
      label: "Frustration",
      validation: { required: false },
      rowSpan: 3,
      aiHandler: async () => {
        const roleData = {
          name: formValues?.name || "",
          gender: formValues?.gender || "",
          age: getStringValue(formValues?.ageRange, commonData.age_ranges),
          status: getStringValue(formValues?.status, commonData.role_statuses),
          income: getStringValue(formValues?.income, commonData.income_types),
          job: formValues?.job || "",
          location: formValues?.location || "",
          type: "Customer",
        };

        return await aiService.generateText("persona frustration", { role: roleData });
      },
    },
  ];

  return [
    {
      name: "",
      fields: customerLeftFields,
    },
    {
      name: "",
      fields: customerRightFields,
    },
  ];
};

export const getStaffFormGroups = (commonData: CommonData, formValues?: any) => {
  const staffRoleLeftFields = [
    {
      name: "job",
      type: "text" as const,
      label: "Role Title",
      validation: { required: true },
    },
    {
      name: "name",
      type: "text" as const,
      label: "Name",
      validation: { required: true },
    },
  ];

  const staffRoleRightFields = [
    {
      name: "gender",
      type: "select" as const,
      label: "Gender",
      options: commonData.genders.map((gender) => ({
        label: gender.label,
        value: gender.value,
      })),
      validation: { required: true },
    },
    {
      name: "ageRange",
      type: "select" as const,
      label: "Age",
      options: commonData.age_ranges.map((age) => ({
        label: age.name,
        value: age.id,
      })),
      renderValue: (value: any) => {
        const option = commonData.age_ranges.find((age) => age.name === value || age.id === value);
        return option?.name || "";
      },
      validation: { required: true },
    },
    {
      name: "about",
      type: "aiTextarea" as const,
      label: "Describe the persona",
      validation: { required: true },
      rowSpan: 4,
      aiHandler: async () => {
        const roleData = {
          name: formValues?.name || "",
          gender: formValues?.gender || "",
          age: getStringValue(formValues?.ageRange, commonData.age_ranges),
          job: formValues?.job || "",
          role_type: formValues?.profession === "front_stage" ? "Front stage" : "Back stage",
        };

        return await aiService.generateText("persona description", { role: roleData });
      },
    },
  ];

  return [
    {
      name: "",
      fields: staffRoleLeftFields,
    },
    {
      name: "",
      fields: staffRoleRightFields,
    },
  ];
};

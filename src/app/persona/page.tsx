"use client";

import personaService from "@/services/PersonaService";
import { PersonaData, PersonaGroup } from "@/types/persona";
import AddIcon from "@mui/icons-material/Add";
import KeyboardArrowLeftIcon from "@mui/icons-material/KeyboardArrowLeft";
import {
  <PERSON><PERSON>,
  Box,
  Button,
  CircularProgress,
  Container,
  Stack,
  Tab,
  Tabs,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CardsView from "./views/CardsView";
import PersonaCreateModal from "./views/PersonaCreateModal";
import PersonaProfile from "./views/PersonaProfile";

// Helper function to normalize persona data
const normalizePersona = (persona: PersonaData): PersonaData => {
  return {
    ...persona,
    goals: Array.isArray(persona.goals) ? persona.goals.join("\n") : persona.goals,
    frustrations: Array.isArray(persona.frustrations)
      ? persona.frustrations.join("\n")
      : persona.frustrations,
  };
};

const normalizePersonas = (personas: PersonaGroup): PersonaGroup => {
  const normalized: PersonaGroup = {};
  Object.keys(personas).forEach((key) => {
    normalized[key] = personas[key].map(normalizePersona);
  });
  return normalized;
};

const PersonaPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [personas, setPersonas] = useState<PersonaGroup>();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPersona, setSelectedPersona] = useState<PersonaData | null>(null);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [generatingImageIds, setGeneratingImageIds] = useState<Set<number>>(new Set());

  useEffect(() => {
    const fetchPersonas = async () => {
      try {
        const groupedRoles = await personaService.getGroupedRoles();
        setPersonas(normalizePersonas(groupedRoles));
        setError(null);
      } catch (err) {
        setError("Failed to load personas");
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPersonas();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleCardClick = (persona: PersonaData) => {
    setSelectedPersona(normalizePersona(persona));
    setIsProfileOpen(true);
  };

  const handleCreateClick = () => {
    setIsCreateModalOpen(true);
  };

  const handleCreateClose = () => {
    setIsCreateModalOpen(false);
  };

  const handleCreateConfirm = async (createdPersona: PersonaData) => {
    setPersonas((prev) => {
      const normalized = normalizePersona(createdPersona);
      return {
        ...prev,
        [normalized.profession]: [...prev[normalized.profession], normalized],
      };
    });
    setIsCreateModalOpen(false);
    setSelectedPersona(normalizePersona(createdPersona));
    setIsProfileOpen(true);
  };

  const handleDelete = async (id: number, type: string) => {
    try {
      await personaService.deleteRole(id);
      setPersonas((prev) => ({
        ...prev,
        [type]: prev[type].filter((persona) => persona.id !== id),
      }));
      if (selectedPersona?.id === id) {
        setSelectedPersona(null);
        setIsProfileOpen(false);
      }
    } catch (error) {
      console.error("Failed to delete persona:", error);
    }
  };

  const handlePersonaUpdate = (updatedPersona: PersonaData) => {
    const normalized = normalizePersona(updatedPersona);
    setSelectedPersona(normalized);
    setPersonas((prev) => ({
      ...prev,
      [normalized.profession]: prev[normalized.profession].map((persona) =>
        persona.id === normalized.id ? normalized : persona
      ),
    }));
  };

  const handleGenerateImage = async (persona: PersonaData) => {
    try {
      setGeneratingImageIds((prev) => new Set(prev).add(persona.id));
      const res = await personaService.generateImage({
        title: persona.job,
        age: parseInt(persona.ageRange),
        gender: persona.gender,
        role: {
          id: persona.id,
          age: parseInt(persona.ageRange),
        },
      });

      if (res.result) {
        const updatedPersona = await personaService.updateRole(persona.id, {
          image: res.result,
        });
        handlePersonaUpdate(updatedPersona);
      }
    } catch (error) {
      console.error("Failed to generate image:", error);
    } finally {
      setGeneratingImageIds((prev) => {
        const next = new Set(prev);
        next.delete(persona.id);
        return next;
      });
    }
  };

  if (isLoading) {
    return (
      <Container sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error) {
    return (
      <Container sx={{ mt: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Container>
    );
  }

  return (
    <Container>
      {isProfileOpen ? (
        <>
          <Button startIcon={<KeyboardArrowLeftIcon />} onClick={() => setIsProfileOpen(false)}>
            Back
          </Button>
          <PersonaProfile
            persona={selectedPersona}
            onDelete={() => handleDelete(selectedPersona.id, selectedPersona.profession)}
            onUpdate={handlePersonaUpdate}
            onGenerateImage={() => selectedPersona && handleGenerateImage(selectedPersona)}
            isGeneratingImage={selectedPersona ? generatingImageIds.has(selectedPersona.id) : false}
          />
        </>
      ) : (
        <>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h4" gutterBottom>
              Persona
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleCreateClick}
            >
              Create
            </Button>
          </Stack>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="persona views">
              <Tab label="Customer" />
              <Tab label="Front Stage" />
              <Tab label="Back Stage" />
            </Tabs>
          </Box>
          <Box sx={{ mt: 2 }}>
            {activeTab === 0 && (
              <CardsView
                data={personas["Customer"]}
                handleClick={handleCardClick}
                handleDelete={handleDelete}
                handleGenerateImage={handleGenerateImage}
                generatingImageIds={generatingImageIds}
              />
            )}
            {activeTab === 1 && (
              <CardsView
                data={personas["Front stage"]}
                handleClick={handleCardClick}
                handleDelete={handleDelete}
                handleGenerateImage={handleGenerateImage}
                generatingImageIds={generatingImageIds}
              />
            )}
            {activeTab === 2 && (
              <CardsView
                data={personas["Back stage"]}
                handleClick={handleCardClick}
                handleDelete={handleDelete}
                handleGenerateImage={handleGenerateImage}
                generatingImageIds={generatingImageIds}
              />
            )}
          </Box>
        </>
      )}
      <PersonaCreateModal
        open={isCreateModalOpen}
        onClose={handleCreateClose}
        onConfirm={handleCreateConfirm}
      />
    </Container>
  );
};

export default PersonaPage;

"use client";

import Header from "@/components/Header";
import Sidebar from "@/components/Sidebar";
import { AppProvider } from "@/context/AppProvider";
import { AuthProvider } from "@/context/AuthProvider";
import AuthService from "@/services/AuthService";
import { getCustomTheme } from "@/styles/theme";
import { Box, CssBaseline } from "@mui/material";
import { ThemeProvider } from "@mui/material/styles";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import { usePathname } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import "./globals.css";

// Initialize dayjs plugins globally
dayjs.extend(utc);
dayjs.extend(timezone);

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const [mode, setMode] = useState<"light" | "dark">("dark");
  const pathname = usePathname();
  const [username, setUsername] = useState<string | null>(null);
  
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
        retry: 1,
      },
    },
  }));

  const theme = useMemo(() => getCustomTheme(mode), [mode]);

  const toggleTheme = () => {
    setMode((prevMode) => (prevMode === "light" ? "dark" : "light"));
  };

  useEffect(() => {
    const storedUsername = localStorage.getItem("username");
    if (storedUsername) {
      setUsername(storedUsername);
    }
  }, []);

  const logout = () => {
    AuthService.logout();
    window.location.href = "/login";
  };

  const isLoginPage = pathname === "/login";

  // Paths where you want to omit the Box styles
  const applyBoxStyles = !pathname.startsWith("/journeys/map/");

  return (
    <html lang="en">
      <head />
      <body>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <style jsx global>{`
            ${!applyBoxStyles
              ? `
              html, body {
                overflow: hidden !important;
                -ms-overflow-style: none !important;
                scrollbar-width: none !important;
              }
              html::-webkit-scrollbar,
              body::-webkit-scrollbar {
                display: none !important;
              }
            `
              : ""}
          `}</style>

          <QueryClientProvider client={queryClient}>
            <AuthProvider>
              <AppProvider>
                <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="en">
                <Box
                  sx={{
                    display: "flex",
                    minHeight: "100vh",
                    ...(!applyBoxStyles && {
                      overflow: "hidden",
                      "& *": {
                        overflow: "hidden !important",
                        "&::-webkit-scrollbar": { display: "none" },
                        msOverflowStyle: "none",
                        scrollbarWidth: "none",
                      },
                    }),
                  }}
                >
                  {!isLoginPage && <Sidebar toggleTheme={toggleTheme} mode={mode} />}

                  <Box
                    sx={{
                      flexGrow: 1,
                      display: "flex",
                      flexDirection: "column",
                      ...(!applyBoxStyles && {
                        overflow: "hidden",
                      }),
                    }}
                  >
                    {!isLoginPage && applyBoxStyles && (
                      <Header username={username} logout={logout} />
                    )}

                    {/* Conditional Box styles */}
                    <Box
                      sx={{
                        ...(applyBoxStyles && {
                          py: 6,
                          px: 3,
                        }),
                        flexGrow: 1,
                      }}
                    >
                      {children}
                    </Box>
                  </Box>
                </Box>
                </LocalizationProvider>
              </AppProvider>
            </AuthProvider>
          </QueryClientProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

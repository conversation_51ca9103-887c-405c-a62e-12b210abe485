"use client";

import Modal from "@/components/Modal";
import { useAppContext } from "@/context/AppProvider";
import buildingBlockService from "@/services/BuildingBlockService";
import { BuildingBlock } from "@/types/buildingBlock";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import React, { useCallback, useEffect, useMemo, useState } from "react";

interface SelectInsightModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (selectedInsights: BuildingBlock[]) => void;
  title?: string;
  type?: "painpoint" | "opportunity" | "both";
  existingBuildingBlocks?: any[]; // Building blocks already added to the map
}

interface InsightData extends BuildingBlock {
  vote?: number;
  category: "Painpoints" | "Opportunity";
}

const SelectInsightModal: React.FC<SelectInsightModalProps> = ({
  open,
  onClose,
  onConfirm,
  title = "Select a painpoint",
  type = "both",
  existingBuildingBlocks = [],
}) => {
  const { commonData } = useAppContext();
  const [insights, setInsights] = useState<InsightData[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedSegment, setSelectedSegment] = useState("All");
  const [relevantSearch, setRelevantSearch] = useState(true);
  const [selectedInsights, setSelectedInsights] = useState<Set<number>>(new Set());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Fetch insights data
  const fetchInsights = useCallback(async () => {
    if (!open) return;

    try {
      setLoading(true);
      const allInsights: InsightData[] = [];

      // Fetch painpoints (typeId: 3, subTypeId: 5)
      if (type === "painpoint" || type === "both") {
        const painpoints = await buildingBlockService.getBuildingBlocks(3, 5);
        const painpointData = painpoints.map((insight) => ({
          ...insight,
          category: "Painpoints" as const,
          // Use actual vote data if available, otherwise mock it
          vote: (insight as any).vote || Math.floor(Math.random() * 50) + 1,
        }));
        allInsights.push(...painpointData);
      }

      // Fetch opportunities (typeId: 3, subTypeId: 6)
      if (type === "opportunity" || type === "both") {
        const opportunities = await buildingBlockService.getBuildingBlocks(3, 6);
        const opportunityData = opportunities.map((insight) => ({
          ...insight,
          category: "Opportunity" as const,
          // Use actual vote data if available, otherwise mock it
          vote: (insight as any).vote || Math.floor(Math.random() * 50) + 1,
        }));
        allInsights.push(...opportunityData);
      }

      setInsights(allInsights);
    } catch (error) {
      console.error("Failed to fetch insights:", error);
    } finally {
      setLoading(false);
    }
  }, [open, type]);

  useEffect(() => {
    fetchInsights();
  }, [fetchInsights]);

  // Filter insights based on search, category, segment, and exclude already added items
  const filteredInsights = useMemo(() => {
    return insights.filter((insight) => {
      // Search filter
      const matchesSearch =
        !searchQuery ||
        insight.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        insight.desc?.toLowerCase().includes(searchQuery.toLowerCase());

      // Category filter
      const matchesCategory = selectedCategory === "All" || insight.category === selectedCategory;

      // Segment filter
      const matchesSegment =
        selectedSegment === "All" ||
        (insight.segment &&
          commonData.segments?.find((s) => s.id === insight.segment)?.name === selectedSegment);

      // Exclude items that are already added to the map
      const isNotAlreadyAdded = !existingBuildingBlocks.some((existingBlock) => {
        // Check if the building block ID matches
        return (
          existingBlock.id === insight.id ||
          (existingBlock.block && existingBlock.block === insight.id)
        );
      });

      return matchesSearch && matchesCategory && matchesSegment && isNotAlreadyAdded;
    });
  }, [
    insights,
    searchQuery,
    selectedCategory,
    selectedSegment,
    commonData.segments,
    existingBuildingBlocks,
  ]);

  // Paginated data
  const paginatedInsights = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredInsights.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredInsights, page, rowsPerPage]);

  // Handle checkbox selection
  const handleSelectInsight = (insightId: number) => {
    const newSelected = new Set(selectedInsights);
    if (newSelected.has(insightId)) {
      newSelected.delete(insightId);
    } else {
      newSelected.add(insightId);
    }
    setSelectedInsights(newSelected);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedInsights.size === paginatedInsights.length) {
      setSelectedInsights(new Set());
    } else {
      const allIds = new Set(paginatedInsights.map((insight) => insight.id));
      setSelectedInsights(allIds);
    }
  };

  // Handle confirm
  const handleConfirm = () => {
    const selectedInsightData = insights.filter((insight) => selectedInsights.has(insight.id));
    onConfirm?.(selectedInsightData);
    onClose();
  };

  // Handle cancel
  const handleCancel = () => {
    setSelectedInsights(new Set());
    onClose();
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setSearchQuery("");
      setSelectedCategory("All");
      setSelectedSegment("All");
      setSelectedInsights(new Set());
      setPage(0);
    }
  }, [open]);

  // Category options based on type
  const categoryOptions = useMemo(() => {
    const options = ["All"];
    if (type === "painpoint" || type === "both") options.push("Painpoints");
    if (type === "opportunity" || type === "both") options.push("Opportunity");
    return options;
  }, [type]);

  // Segment options
  const segmentOptions = useMemo(() => {
    const options = ["All"];
    if (commonData.segments) {
      options.push(...commonData.segments.map((segment) => segment.name));
    }
    return options;
  }, [commonData.segments]);

  return (
    <Modal open={open} onClose={onClose} title={title} maxWidth={1200}>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
        {/* Filters */}
        <Stack direction="row" spacing={2} alignItems="center">
          {/* Search */}
          <TextField
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
            sx={{ minWidth: 300 }}
          />

          {/* Category Filter */}
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={selectedCategory}
              label="Category"
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {categoryOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Segment Filter */}
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Segment</InputLabel>
            <Select
              value={selectedSegment}
              label="Segment"
              onChange={(e) => setSelectedSegment(e.target.value)}
            >
              {segmentOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Relevant Search Checkbox */}
          <FormControlLabel
            control={
              <Checkbox
                checked={relevantSearch}
                onChange={(e) => setRelevantSearch(e.target.checked)}
                sx={{
                  color: "grey.500",
                  "&.Mui-checked": {
                    color: "primary.main",
                  },
                }}
              />
            }
            label="Relevant search"
          />
        </Stack>

        {/* Data Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={
                      selectedInsights.size > 0 && selectedInsights.size < paginatedInsights.length
                    }
                    checked={
                      paginatedInsights.length > 0 &&
                      selectedInsights.size === paginatedInsights.length
                    }
                    onChange={handleSelectAll}
                    sx={{
                      color: "grey.500",
                      "&.Mui-checked": {
                        color: "primary.main",
                      },
                      "&.MuiCheckbox-indeterminate": {
                        color: "primary.main",
                      },
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Name
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Description
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Typography variant="subtitle2" fontWeight="bold">
                    Vote
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography>Loading...</Typography>
                  </TableCell>
                </TableRow>
              ) : paginatedInsights.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography color="text.secondary">No insights found</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedInsights.map((insight) => (
                  <TableRow
                    key={insight.id}
                    hover
                    onClick={() => handleSelectInsight(insight.id)}
                    sx={{ cursor: "pointer" }}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedInsights.has(insight.id)}
                        sx={{
                          color: "grey.500",
                          "&.Mui-checked": {
                            color: "primary.main",
                          },
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{insight.name || "Untitled"}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {insight.desc || "No description"}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">{insight.vote || 0}</Typography>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25]}
                  colSpan={4}
                  count={filteredInsights.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={(_, newPage) => setPage(newPage)}
                  onRowsPerPageChange={(event) => {
                    setRowsPerPage(parseInt(event.target.value, 10));
                    setPage(0);
                  }}
                  labelDisplayedRows={({ from, to, count }) =>
                    `${from}-${to} of ${count !== -1 ? count : `more than ${to}`}`
                  }
                />
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>

        {/* Action Buttons */}
        <Stack direction="row" spacing={2} justifyContent="flex-end">
          <Button
            variant="outlined"
            onClick={handleCancel}
            sx={{
              borderColor: "grey.500",
              color: "grey.500",
              "&:hover": {
                borderColor: "grey.400",
                backgroundColor: "grey.50",
              },
            }}
          >
            CANCEL
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirm}
            disabled={selectedInsights.size === 0}
            sx={{
              backgroundColor: "#c8ff00",
              color: "#000000",
              fontWeight: "bold",
              "&:hover": {
                backgroundColor: "#b3e600",
              },
              "&:disabled": {
                backgroundColor: "grey.300",
                color: "grey.500",
              },
            }}
          >
            CONFIRM
          </Button>
        </Stack>
      </Box>
    </Modal>
  );
};

export default SelectInsightModal;

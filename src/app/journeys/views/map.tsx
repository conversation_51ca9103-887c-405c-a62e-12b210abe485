import ConfirmDialog from "@/components/ConfirmDialog";
import { useAppContext } from "@/context/AppProvider";
import mapService from "@/services/MapService";
import { CustomerGoal, Map } from "@/types/map";
import AddIcon from "@mui/icons-material/Add";
import { Box, Button, Container } from "@mui/material";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import DataTable from "../components/DataTable";
import NewMapDialog from "./NewMapDialog";
const columns: {
  name: string;
  type: "text" | "date" | "user" | "tags" | "state";
}[] = [
  { name: "Title", type: "text" },
  { name: "Customer goal", type: "text" },
  { name: "States", type: "state" },
  { name: "Created by", type: "user" },
];

// Add the mapping function
export const mapToColumns = (maps: Map[], mapStates: any[]) => {
  return maps.map((map) => ({
    id: map.id,
    Title: map.name,
    "Customer goal": (map.customer_goal as CustomerGoal)?.name || "",
    "Created by": map.user === 1 ? "<PERSON>e" : "Jane Doe",
    States: mapStates.find((state) => state.value === map.state)?.label || map.state,
  }));
};

const MapView = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [mapToDelete, setMapToDelete] = useState<{ id: string; name: string } | null>(null);
  const [data, setData] = useState<Map[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { commonData } = useAppContext();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const maps = await mapService.getMaps();
        setData(maps);
        setError(null);
      } catch (err) {
        setError("Failed to fetch maps");
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const clickRow = (rowData: any) => {
    const mapId = rowData.id;
    router.push(`/journeys/map/${mapId}`);
  };

  const handleClickOpen = () => {
    setOpenDialog(true);
  };

  const handleClose = () => {
    setOpenDialog(false);
  };

  const handleDeleteClick = (rowData: { id: string; Title: string }) => {
    setMapToDelete({ id: rowData.id, name: rowData.Title });
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!mapToDelete) return;

    try {
      await mapService.deleteMap(mapToDelete.id);
      const maps = await mapService.getMaps();
      setData(maps);
      setDeleteConfirmOpen(false);
      setMapToDelete(null);
    } catch (err) {
      console.error("Failed to delete map:", err);
      setError("Failed to delete map");
    }
  };

  if (isLoading) {
    return <Container>Loading...</Container>;
  }

  if (error) {
    return <Container>{error}</Container>;
  }

  return (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleClickOpen}
        >
          Create New
        </Button>
      </Box>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={mapToColumns(data, commonData.map_states)}
        clickRow={clickRow}
        handleDelete={handleDeleteClick}
      />

      <ConfirmDialog
        open={deleteConfirmOpen}
        title="Confirm Delete"
        message={`Are you sure you want to delete "${mapToDelete?.name}"? This action cannot be undone.`}
        onConfirm={handleDeleteConfirm}
        onCancel={() => setDeleteConfirmOpen(false)}
      />

      {/* New Map Dialog */}
      <NewMapDialog open={openDialog} onClose={handleClose} />
    </Box>
  );
};

export default MapView;

import { useDeleteMap, useMaps } from "@/api/v2/hooks/useMaps";
import ConfirmDialog from "@/components/ConfirmDialog";
import { useAppContext } from "@/context/AppProvider";
import type { Map } from "@/types/api/client";
import AddIcon from "@mui/icons-material/Add";
import { Box, Button, Container } from "@mui/material";
import { useRouter } from "next/navigation";
import { useState } from "react";
import DataTable from "../components/DataTable";
import NewMapDialog from "./NewMapDialog";
const columns: {
  name: string;
  type: "text" | "date" | "user" | "tags" | "state";
}[] = [
  { name: "Title", type: "text" },
  { name: "Customer goal", type: "text" },
  { name: "States", type: "state" },
  { name: "Created by", type: "user" },
];

// Add the mapping function
export const mapToColumns = (maps: Map[], mapStates: any[]) => {
  return maps.map((map) => ({
    id: map.map_id,
    Title: map.name,
    "Customer goal": map.customer_goal?.name || "",
    "Created by": map.created_by?.name || "Unknown",
    States: mapStates.find((state) => state.value === map.state)?.label || map.state,
  }));
};

const MapView = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [mapToDelete, setMapToDelete] = useState<{ id: string; name: string } | null>(null);
  const router = useRouter();
  const { commonData } = useAppContext();

  // Use the new hooks
  const { data: maps = [], isLoading, error } = useMaps();
  const deleteMapMutation = useDeleteMap();

  const clickRow = (rowData: any) => {
    const mapId = rowData.id;
    router.push(`/journeys/map/${mapId}`);
  };

  const handleClickOpen = () => {
    setOpenDialog(true);
  };

  const handleClose = () => {
    setOpenDialog(false);
  };

  const handleDeleteClick = (rowData: { id: string; Title: string }) => {
    setMapToDelete({ id: rowData.id, name: rowData.Title });
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!mapToDelete) return;

    try {
      await deleteMapMutation.mutateAsync(mapToDelete.id);
      setDeleteConfirmOpen(false);
      setMapToDelete(null);
    } catch (err) {
      console.error("Failed to delete map:", err);
    }
  };

  if (isLoading) {
    return <Container>Loading...</Container>;
  }

  if (error) {
    return <Container>Error loading maps: {error.message}</Container>;
  }

  return (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleClickOpen}
        >
          Create New
        </Button>
      </Box>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={mapToColumns(maps, commonData.map_states)}
        clickRow={clickRow}
        handleDelete={handleDeleteClick}
      />

      <ConfirmDialog
        open={deleteConfirmOpen}
        title="Confirm Delete"
        message={`Are you sure you want to delete "${mapToDelete?.name}"? This action cannot be undone.`}
        onConfirm={handleDeleteConfirm}
        onCancel={() => setDeleteConfirmOpen(false)}
      />

      {/* New Map Dialog */}
      <NewMapDialog open={openDialog} onClose={handleClose} />
    </Box>
  );
};

export default MapView;

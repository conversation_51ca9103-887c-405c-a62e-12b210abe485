import FormBuilder from "@/components/FormBuilder";
import { useAppContext } from "@/context/AppProvider";
import customerGoalService from "@/services/CustomerGoalService";
import mapService from "@/services/MapService";
import { Box, Divider, Modal, TextField, Typography } from "@mui/material";
import { useRouter } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";

interface NewMapDialogProps {
  open: boolean;
  onClose: () => void;
}

interface FormData {
  name: string;
  desc: string;
  customer_goal: number;
  business_objective: string;
  line_of_business: number;
  state: string;
  customer_goal_select?: string;
  new_customer_goal?: string;
}

const NewMapDialog: React.FC<NewMapDialogProps> = ({ open, onClose }) => {
  const router = useRouter();
  const { commonData } = useAppContext();
  const [showNewGoalInput, setShowNewGoalInput] = useState(false);
  const [formValues, setFormValues] = useState<FormData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const newGoalInputRef = useRef<HTMLInputElement>(null);

  // Use the actual map states from commonData with value/label pairs
  const mapStateOptions = commonData.map_states.map((state) => ({
    value: state.value,
    label: state.label,
  }));

  // Create customer goal options with "Add new" option
  const customerGoalOptions = [
    ...commonData.customer_goals.map((goal) => ({
      label: goal.name,
      value: goal.id.toString(),
    })),
    { label: "Add new", value: "add_new" },
  ];

  const initialValues: FormData = {
    name: "",
    desc: "End-to-end journey for home loan customers",
    customer_goal: commonData.customer_goals[0]?.id || 0,
    customer_goal_select: customerGoalOptions[0].value,
    new_customer_goal: "",
    business_objective: "",
    line_of_business: 0,
    state: null,
  };

  // Update customer_goal when selection or new input changes
  useEffect(() => {
    if (!formValues) return;

    if (formValues.customer_goal_select === "add_new") {
      setShowNewGoalInput(true);
      // Focus the input field when it appears
      setTimeout(() => {
        if (newGoalInputRef.current) {
          newGoalInputRef.current.focus();
        }
      }, 100);
    } else {
      setShowNewGoalInput(false);
      // Find the selected goal and set its ID
      const selectedGoal = commonData.customer_goals.find(
        (goal) => goal.id.toString() === formValues.customer_goal_select
      );
      if (selectedGoal) {
        setFormValues((prev) => ({
          ...prev!,
          customer_goal: selectedGoal.id,
        }));
      }
    }
  }, [formValues?.customer_goal_select, commonData.customer_goals]);

  // Handle form value changes
  const handleFormChange = (values: any) => {
    // Preserve the new_customer_goal value when other fields change
    if (formValues?.new_customer_goal && !values.new_customer_goal) {
      values.new_customer_goal = formValues.new_customer_goal;
    }
    setFormValues(values);
  };

  // Handle new customer goal input change
  const handleNewGoalChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setFormValues((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        new_customer_goal: newValue,
      };
    });
  };

  const formGroups = [
    {
      name: "",
      fields: [
        {
          name: "state",
          type: "buttonGroup" as const,
          label: "State of map",
          states: mapStateOptions.map((state) => state.label),
          stateValues: mapStateOptions.map((state) => state.value),
        },
        {
          name: "name",
          type: "text" as const,
          label: "Journey title",
          validation: { required: true },
        },
        {
          name: "customer_goal_select",
          type: "select" as const,
          label: "Customer goal",
          options: customerGoalOptions,
          validation: { required: true },
        },
      ],
    },
    {
      name: "",
      fields: [
        {
          name: "line_of_business",
          type: "select" as const,
          label: "Select the specific line of business relevant to the journey map",
          options: commonData.line_of_business.map((business) => ({
            label: business.name,
            value: business.id,
          })),
          validation: { required: true },
        },
        {
          name: "business_objective",
          type: "textarea" as const,
          label:
            "Describe the key outcomes the business wants to achieve for the specific customer journey",
          validation: { required: true },
          rowSpan: 8,
        },
      ],
    },
  ];

  const handleSubmit = async (formData: FormData) => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);

      // Make sure we have the latest new_customer_goal value
      const finalData = {
        ...formData,
        new_customer_goal: formValues?.new_customer_goal || formData.new_customer_goal,
      };

      // If "Add new" is selected, create a new customer goal first
      if (finalData.customer_goal_select === "add_new" && finalData.new_customer_goal) {
        try {
          const newGoal = await customerGoalService.createCustomerGoal({
            name: finalData.new_customer_goal,
          });

          // Use the newly created goal's ID
          finalData.customer_goal = newGoal.id;
        } catch (error) {
          console.error("Failed to create new customer goal:", error);
          // If we can't create a new goal, we can't proceed
          setIsSubmitting(false);
          return;
        }
      } else {
        // Use the selected goal ID
        finalData.customer_goal = parseInt(finalData.customer_goal_select);
      }

      // Remove temporary fields
      delete finalData.customer_goal_select;
      delete finalData.new_customer_goal;

      const newMap = await mapService.postMap(finalData);
      router.push(`/journeys/map/${newMap.id}`);
      onClose();
    } catch (error) {
      console.error("Failed to create map:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="create-journey-map-modal">
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          color: "white",
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 8,
          padding: 3,
          width: "100%",
          maxWidth: 900,
          maxHeight: "90vh",
          overflow: "auto",
          outline: "none",
        }}
      >
        <Typography variant="h5" mb={2}>
          Create a new map
        </Typography>
        <Divider sx={{ mb: 3 }} />
        <Box position="relative">
          <FormBuilder
            groups={formGroups}
            initialValues={initialValues}
            onSubmit={handleSubmit}
            onCancel={onClose}
            submitButtonText="Generate a Map"
            onChange={handleFormChange}
          />

          {/* Conditional new goal input */}
          {showNewGoalInput && (
            <Box
              sx={{
                position: "absolute",
                bottom: "120px",
                left: "0",
                width: "48.5%",
                zIndex: 1,
                mt: 1,
              }}
            >
              <TextField
                inputRef={newGoalInputRef}
                fullWidth
                value={formValues?.new_customer_goal || ""}
                onChange={handleNewGoalChange}
                required
              />
            </Box>
          )}
        </Box>
      </Box>
    </Modal>
  );
};

export default NewMapDialog;

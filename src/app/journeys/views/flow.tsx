import React from "react";
import DataTable from "../components/DataTable";
import { Box, Button } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

const columns: {
  name: string;
  type: "text" | "date" | "user" | "tags" | "state";
}[] = [
  { name: "Title", type: "text" },
  { name: "Linked Map", type: "tags" },
  { name: "Line of business", type: "text" },
  { name: "Created by", type: "user" },
  { name: "Last update", type: "date" },
];

const data = [
  {
    Title: "Cell",
    "Linked Map": "Chip",
    "Line of business": "Cell",
    "Created by": "Xin Yue",
    "Last update": "{DD/MM/YY}",
  },
  {
    Title: "Cell",
    "Linked Map": "Chip",
    "Line of business": "Cell",
    "Created by": "Xin Yue",
    "Last update": "{DD/MM/YY}",
  },
];

const FlowView = () => {
  return (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
        <Button variant="contained" color="primary" startIcon={<AddIcon />}>
          Create New
        </Button>
      </Box>

      {/* Data Table */}
      <DataTable columns={columns} data={data} />
    </Box>
  );
};

export default FlowView;

// pages/data/index.tsx
"use client";

import { Box, Container, Tab, Tabs, Typography } from "@mui/material";
import React, { useState } from "react";
import FlowView from "./views/flow";
import MapView from "./views/map";

const JourneyPage = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Container>
      <Typography variant="h4" gutterBottom>
        Journeys
      </Typography>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="data views">
          <Tab label="Map" />
          <Tab label="Flow" />
        </Tabs>
      </Box>
      <Box sx={{ mt: 2 }}>
        {activeTab === 0 && <MapView />}
        {activeTab === 1 && <FlowView />}
      </Box>
    </Container>
  );
};

export default JourneyPage;

"use client";

import { useMap } from "@/api/v2/hooks/useMaps";
import { CustomerJourneyMap } from "@/components/mapCanvas/CustomerJourneyMap";
import { MapCanvasProvider, mapCanvasActions, useMapCanvas } from "@/context/MapCanvasProvider";
import { CircularProgress, Container, Typography } from "@mui/material";
import { useParams } from "next/navigation";
import { useEffect } from "react";

// Inner component that has access to MapCanvas context
function MapPageContent({ mapId }: { mapId: string }) {
  const { dispatch } = useMapCanvas();
  const { data: mapData, isLoading, error } = useMap(mapId);

  useEffect(() => {
    if (mapData) {
      console.log(mapData);
      dispatch(mapCanvasActions.setMapData(mapData));
    }
  }, [mapData, dispatch]);

  if (isLoading) {
    return (
      <Container sx={{ display: "flex", justifyContent: "center", py: 4 }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <Typography color="error">
          Error loading map: {error instanceof Error ? error.message : "Unknown error"}
        </Typography>
      </Container>
    );
  }

  if (!mapData) {
    return (
      <Container>
        <Typography color="error">Map not found</Typography>
      </Container>
    );
  }

  return <CustomerJourneyMap />;
}

export default function MapPage() {
  const { id } = useParams();

  if (!id) {
    return (
      <Container>
        <Typography color="error">Invalid map ID</Typography>
      </Container>
    );
  }

  return (
    <MapCanvasProvider>
      <MapPageContent mapId={id as string} />
    </MapCanvasProvider>
  );
}

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Avatar,
  IconButton,
  Typography,
  Chip,
  Stack,
  Menu,
  MenuItem,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";

interface DataTableProps {
  columns: {
    name: string; // The name of the column
    type: "text" | "user" | "tags" | "state" | "date"; // The type of the column
  }[];
  data: { [key: string]: string | string[] }[]; // Data can now handle arrays for chips
  clickRow?: (rowData: { [key: string]: string | string[] }) => void; // clickRow function as prop
  handleDelete?: (rowData: { [key: string]: string | string[] }) => void; // Add optional delete handler
}

const generateRandomAvatarUrl = (name: string) => {
  return `https://www.tapback.co/api/avatar/${name}.webp`;
};

const DataTable: React.FC<DataTableProps> = ({ columns, data, clickRow, handleDelete }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRow, setSelectedRow] = useState<null | number>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, rowIndex: number) => {
    event.stopPropagation(); // Stop event from bubbling up to the row
    setAnchorEl(event.currentTarget);
    setSelectedRow(rowIndex);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRow(null); // Reset when menu is closed
  };

  const handleMenuAction = (event: React.MouseEvent<HTMLElement>, action: string) => {
    event.stopPropagation();
    if (action === "Delete" && handleDelete && selectedRow !== null) {
      handleDelete(data[selectedRow]);
    }
    handleMenuClose();
  };

  return (
    <>
      <Table>
        <TableHead>
          <TableRow>
            {columns.map((column) => (
              <TableCell
                key={column.name}
                sx={{
                  borderBottom: "2px solid rgba(255, 255, 255, 0.12)", // Consistent bottom border
                }}
              >
                <Typography variant="body2" fontWeight="bold">
                  {column.name}
                </Typography>
              </TableCell>
            ))}
            <TableCell
              sx={{
                borderBottom: "2px solid rgba(255, 255, 255, 0.12)", // Ensure the last header cell has a bottom border
              }}
            />
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, rowIndex) => (
            <TableRow
              key={rowIndex}
              onClick={() => clickRow && clickRow(row)} // Bind clickRow method
              hover // Makes row visually interactive on hover
              sx={{ cursor: clickRow ? "pointer" : "default" }} // Show pointer if row is clickable
            >
              {columns.map((column) => (
                <TableCell
                  key={column.name}
                  sx={{
                    borderBottom: "1px solid rgba(255, 255, 255, 0.12)", // Ensure the row bottom border matches header
                  }}
                >
                  {/* Render content based on column type */}
                  {column.type === "user" &&
                  typeof row[column.name] === "string" ? (
                    <Stack direction="row" alignItems="center">
                      <Avatar
                        src={generateRandomAvatarUrl(row[column.name] as string)}
                        sx={{ width: 40, height: 40, marginRight: 1 }}
                      />
                      {row[column.name]}
                    </Stack>
                  ) : column.type === "tags" ? (
                    Array.isArray(row[column.name]) ? (
                      (row[column.name] as string[]).map((tag, tagIndex) => (
                        <Chip size="small" key={tagIndex} label={tag} />
                      ))
                    ) : (
                      <Chip size="small" label={row[column.name]} />
                    )
                  ) : column.type === "state" ? (
                    <Chip
                      label={row[column.name]}
                      size="small"
                      color={
                        row[column.name] === "Current"
                          ? "primary"
                          : row[column.name] === "Future"
                          ? "secondary"
                          : "default"
                      }
                    />
                  ) : column.type === "date" ? (
                    <Typography>{row[column.name]}</Typography>
                  ) : (
                    row[column.name] // Default rendering for text
                  )}
                </TableCell>
              ))}
              <TableCell
                sx={{
                  borderBottom: "1px solid rgba(255, 255, 255, 0.12)", // Match row bottom border
                }}
              >
                <IconButton
                  onClick={(event) => handleMenuOpen(event, rowIndex)} // Open menu on click
                >
                  <MoreVertIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Menu Component */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(event) => event.stopPropagation()} // Stop clicks inside menu from triggering row click
      >
        <MenuItem onClick={(event) => handleMenuAction(event, "Share")} disabled>
          Share
        </MenuItem>
        <MenuItem 
          onClick={(event) => handleMenuAction(event, "Delete")}
          disabled={!handleDelete} // Disable if no handler provided
        >
          Delete
        </MenuItem>
        <MenuItem onClick={(event) => handleMenuAction(event, "Download")} disabled>
          Download
        </MenuItem>
      </Menu>
    </>
  );
};

export default DataTable;
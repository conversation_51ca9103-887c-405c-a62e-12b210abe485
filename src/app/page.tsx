"use client";

import React, { useEffect, useState } from "react";
import SphericalHeatmap from "../charts/SphericalHeatmap";
import NodeMap from "../charts/NodeMap";
import { generateHierarchicalDataByLevel } from "@/utils/dataVizDataGenerator";
import {
  Button,
  IconButton,
  InputBase,
  Stack,
  Typography,
  Alert,
  CircularProgress,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import LayersIcon from "@mui/icons-material/Layers";
import componentService from "@/services/ComponentService";
import {
  ComponentData,
  NodeMapData,
} from "@/types/components";
import { parseComponentDataToNodeMap } from "@/utils/componentHelper";
import DataVizSettingDrawer from "@/components/DataVizSettingDrawer";
import mapService from "@/services/MapService";
import { parseMapData } from "@/utils/componentHelper";

const ValueChainPage = () => {
  const [nodeMapData, setNodeMapData] = useState<NodeMapData>(null);
  const dataByLevel = generateHierarchicalDataByLevel([6, 6, 6, 6]);
  const [selectedLevel, setSelectedLevel] = useState<number>(3);
  const levelData = dataByLevel[selectedLevel];
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [mapType, setMapType] = useState<"spherical" | "node">("node");
  const [viewType, setViewType] = useState<"all" | "customer" | "business">("all");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const customerComponents = await componentService.getComponents({
          framework: "customer",
          query: "",
        });

        const businessComponents = await componentService.getComponents({
          framework: "business",
          query: "",
        });

        const mapData = await mapService.getMaps();
        let details;
        if (mapData && mapData.length > 0) {
          const detailsPromises = mapData.map(map => mapService.getMapDetails(map.id));
          details = await Promise.all(detailsPromises);
        }

        const customerData = parseComponentDataToNodeMap(
          customerComponents as ComponentData[]
        );
        const businessData = parseComponentDataToNodeMap(
          businessComponents as ComponentData[]
        );
        setNodeMapData({ customer: customerData, business: businessData, map: parseMapData(details) });
        setErrorMessage(null);
      } catch (error) {
        setErrorMessage("Failed to fetch components: " + error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleZoomIn = () => {
    if (selectedLevel < 3) setSelectedLevel(selectedLevel + 1);
  };

  const handleZoomOut = () => {
    if (selectedLevel > 0) setSelectedLevel(selectedLevel - 1);
  };

  const toggleDrawer = (open: boolean) => {
    setIsDrawerOpen(open);
  };

  return (
    <Stack
      px={3}
      sx={{
        width: "100%",
        height: "calc(100vh - 10rem)",
        position: "relative",
      }}
    >
      {/* Search input */}
      <Stack
        direction="row"
        alignItems="center"
        spacing={1}
        sx={{
          borderColor: "divider",
          borderWidth: "1px",
          borderStyle: "solid",
          width: "20rem",
          borderRadius: "8px",
          padding: "5px 10px",
        }}
      >
        <SearchIcon />
        <InputBase
          placeholder="Search component model or map"
          sx={{ ml: 1, color: "#fff", width: 250 }}
        />
      </Stack>

      {/* Conditionally render the heatmap or node map */}
      <Stack
        justifyContent="center"
        alignItems="center"
        sx={{
          flexGrow: 1,
          position: "relative",
        }}
      >
        {isLoading ? (
          <CircularProgress
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
            }}
          />
        ) : errorMessage ? (
          <Alert
            severity="error"
            sx={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              minWidth: "300px",
            }}
          >
            {errorMessage}
          </Alert>
        ) : mapType === "spherical" ? (
          <SphericalHeatmap data={levelData} />
        ) : (
          nodeMapData && (
            <NodeMap
              data={nodeMapData}
              view={viewType} 
              level={selectedLevel}
            />
          )
        )}
      </Stack>

      {/* Parent Stack for the settings and zoom */}
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Button
          variant="contained"
          endIcon={<LayersIcon />}
          onClick={() => toggleDrawer(true)}
          sx={{ borderRadius: 10 }}
        >
          Setting
        </Button>

        <Stack
          direction="row"
          alignItems="center"
          spacing={1}
          sx={{ background: "#333", borderRadius: "25px", padding: "5px 10px" }}
        >
          <IconButton onClick={handleZoomOut}>
            <RemoveIcon />
          </IconButton>
          <Typography variant="body2" sx={{ mx: 1 }}>
            LEVEL {selectedLevel}
          </Typography>
          <IconButton onClick={handleZoomIn}>
            <AddIcon />
          </IconButton>
        </Stack>
      </Stack>

      {/* Settings Drawer */}
      <DataVizSettingDrawer
        isOpen={isDrawerOpen}
        toggleDrawer={toggleDrawer}
        mapType={mapType}
        setMapType={setMapType}
        viewType={viewType}
        setViewType={setViewType}
      />
    </Stack>
  );
};

export default ValueChainPage;

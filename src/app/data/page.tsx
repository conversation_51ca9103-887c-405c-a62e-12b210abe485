"use client";

import { <PERSON>, Card, Card<PERSON>ontent, Container, Grid2, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React from "react";

interface DataCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  onClick?: () => void;
}

const DataCard: React.FC<DataCardProps> = ({ icon, title, description, onClick }) => (
  <Grid2 size={{ xs: 12, md: 4 }}>
    <Card
      onClick={onClick}
      sx={{
        cursor: "pointer",
        transition: "transform 0.2s, box-shadow 0.2s",
        "&:hover": {
          transform: "translateY(-4px)",
          boxShadow: (theme) => theme.shadows[4],
        },
      }}
    >
      <CardContent>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          height={220}
          justifyContent="center"
          gap={2}
        >
          {icon}
          <Typography variant="subtitle1" gutterBottom>
            {title}
          </Typography>
          <Typography variant="body2" color="textSecondary" align="center">
            {description}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  </Grid2>
);

const DataPage = () => {
  const router = useRouter();

  return (
    <Container>
      <Typography variant="h4" gutterBottom mb={5}>
        Data
      </Typography>
      <Grid2 container spacing={3}>
        <DataCard
          icon={<Image src="/images/VoiceOfCustomer.svg" alt="Logo" width={40} height={40} />}
          title="Voice of customer"
          description="Customer verbatim that calculated by NPS, SES or CSAT"
          onClick={() => router.push("/data/4")}
        />
        <DataCard
          icon={<Image src="/images/Insights.svg" alt="Logo" width={40} height={40} />}
          title="Insights"
          description="Collected opportunity, pain points"
          onClick={() => router.push("/data/3")}
        />
        <DataCard
          icon={<Image src="/images/OperationContext.svg" alt="Logo" width={40} height={40} />}
          title="Operation context"
          description="List of internal system or regulations"
          onClick={() => router.push("/data/5")}
        />
        <DataCard
          icon={<Image src="/images/Analytics.svg" alt="Logo" width={40} height={40} />}
          title="Analytics"
          description="Traffic, spend time, conversion rate"
          onClick={() => router.push("/data/6")}
        />
      </Grid2>
    </Container>
  );
};

export default DataPage;

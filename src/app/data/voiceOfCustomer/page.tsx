"use client";

import AddIcon from "@mui/icons-material/Add";
import {
  Box,
  Breadcrumbs,
  Button,
  Container,
  Link,
  Menu,
  MenuItem,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

const VoiceOfCustomerPage = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Container>
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link underline="hover" color="inherit" href="/data">
          Data
        </Link>
        <Typography color="text.primary">Voice of customer</Typography>
      </Breadcrumbs>
      <Typography variant="h4" sx={{ mb: 5 }}>
        Voice of Customer
      </Typography>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        flexDirection="column"
        gap={2}
      >
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Upload data of voice of customer
        </Typography>
        <Button variant="contained" startIcon={<AddIcon />} onClick={handleClick}>
          Add
        </Button>
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem onClick={handleClose}>Add manually</MenuItem>
          <MenuItem onClick={handleClose}>Import</MenuItem>
        </Menu>
      </Box>
    </Container>
  );
};

export default VoiceOfCustomerPage;

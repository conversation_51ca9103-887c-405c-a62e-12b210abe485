import { BuildingBlockTypeDetail } from "@/types/buildingBlock";
import { CommonData } from "@/types/common";

/**
 * Process form values to handle date objects
 * @param values Form values to process
 * @returns Processed values with dates converted to strings
 */
export const processFormValues = (values: any) => {
  return Object.keys(values).reduce((acc, key) => {
    const value = values[key];

    // Handle null or undefined values
    if (value === null || value === undefined) {
      acc[key] = value;
      return acc;
    }

    // Handle dayjs objects (MUI DatePicker with dayjs adapter returns these)
    if (typeof value === "object" && typeof value.format === "function") {
      // Use dayjs format method to get YYYY-MM-DD string
      acc[key] = value.format("YYYY-MM-DD");
    }
    // Check if it's a standard Date object
    else if (value instanceof Date) {
      // Convert Date to ISO string format (YYYY-MM-DD)
      acc[key] = value.toISOString().split("T")[0];
    }
    // Handle any other type of date-like object with $d property
    else if (typeof value === "object" && value.$d instanceof Date) {
      // Convert the internal Date to ISO string format
      acc[key] = value.$d.toISOString().split("T")[0];
    }
    // Handle objects with toISOString method
    else if (typeof value === "object" && typeof value.toISOString === "function") {
      // Use toISOString method to get ISO string and extract the date part
      acc[key] = value.toISOString().split("T")[0];
    }
    // For all other values, pass them through unchanged
    else {
      acc[key] = value;
    }
    return acc;
  }, {} as Record<string, any>);
};

/**
 * Prepare form data for submission
 * @param processedValues Processed form values
 * @param buildingBlockType Building block type details
 * @param selectedSubType Selected sub-type ID
 * @returns Form data ready for submission
 */
export const prepareFormData = (
  processedValues: Record<string, any>,
  buildingBlockType: BuildingBlockTypeDetail | null,
  selectedSubType: number | null
) => {
  // Ensure required fields are present
  if (!processedValues.name) {
    processedValues.name = buildingBlockType?.name || "Unnamed";
  }

  if (!processedValues.desc) {
    processedValues.desc = buildingBlockType?.desc || "";
  }

  // Prioritize the sub_type from the form values if it exists
  // Otherwise, fall back to the selectedSubType from the component state
  const subType =
    processedValues.sub_type !== undefined && processedValues.sub_type !== ""
      ? Number(processedValues.sub_type)
      : selectedSubType || 0;

  // Add sub_type to the form values and ensure it matches the required interface
  return {
    ...processedValues,
    name: processedValues.name,
    desc: processedValues.desc,
    sub_type: subType,
  };
};

/**
 * Get form fields for the FormBuilder component
 * @param buildingBlockType Building block type details
 * @param commonData Common data for options
 * @returns Form groups for the FormBuilder
 */
export const getFormFields = (
  buildingBlockType: BuildingBlockTypeDetail | null,
  commonData: CommonData
) => {
  if (!buildingBlockType || !buildingBlockType.fields || !buildingBlockType.fields.fields) {
    return [];
  }

  // Create subtype field for FormBuilder
  const subtypeField =
    buildingBlockType.sub_types.length > 0
      ? [
          {
            name: "sub_type",
            type: "select" as const,
            label: `What's your ${buildingBlockType.name.toLowerCase()} type?`,
            validation: { required: true },
            options: buildingBlockType.sub_types.map((subType) => ({
              label: subType.name,
              value: subType.id,
            })),
          },
        ]
      : [];

  // Create form groups from the fields definition
  const formFields = buildingBlockType.fields.fields
    .filter((field: any) => {
      // For basic fields, only include name and description
      if (field.is_basic === true) {
        return field.name === "name" || field.name === "desc";
      }
      // Include all non-basic fields
      return field.is_basic !== true;
    })
    .map((field: any) => {
      // Map API field type to FormBuilder field type
      let fieldType: "text" | "number" | "date" | "select" | "textarea" = "text";

      switch (field.type) {
        case "date":
          fieldType = "date";
          break;
        case "number":
          fieldType = "number";
          break;
        case "foreign_key":
        case "multi_select":
          fieldType = "select";
          break;
        case "text":
          fieldType = "textarea";
          break;
        default:
          fieldType = "text";
      }

      // Create FormBuilder field
      const formField: any = {
        name: field.name,
        type: fieldType,
        label: field.label,
        description: field.description,
        validation: field.validation,
      };

      // Add options for select fields
      if (fieldType === "select") {
        if (field.values && field.values.options) {
          // Use options from field.values.options if available
          formField.options = [
            { label: "Select...", value: "" },
            ...field.values.options.map((option: any) => ({
              label: option.name,
              value: option.value,
            })),
          ];
        } else if (field.type === "foreign_key") {
          if (!field.values || !field.values.source) {
            // If no source is specified, use default options
            formField.options = [
              { label: "Select...", value: "" },
              { label: "Option 1", value: 1 },
              { label: "Option 2", value: 2 },
            ];
            return formField;
          }
          // For foreign_key fields, look up options from commonData
          const sourceName = field.values.source;
          let options = [];

          // Check if the source exists in commonData
          if (commonData[sourceName as keyof typeof commonData]) {
            const sourceData = commonData[sourceName as keyof typeof commonData] as any[];

            // Map the source data to options
            options = sourceData.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          }
          // Special case for "brand" which might be stored as "brands" in commonData
          else if (sourceName === "brand" && commonData.brands) {
            options = commonData.brands.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          }
          // Special case for "segment" which might be stored as "segments" in commonData
          else if (sourceName === "segment" && commonData.segments) {
            options = commonData.segments.map((item: any) => ({
              label: item.name,
              value: item.id,
            }));
          }

          formField.options = [{ label: "Select...", value: "" }, ...options];
        } else {
          // Default placeholder options
          formField.options = [
            { label: "Select...", value: "" },
            { label: "Option 1", value: 1 },
            { label: "Option 2", value: 2 },
          ];
        }
      }

      return formField;
    });
  const allFields = [...subtypeField, ...formFields];

  // Divide fields into two groups
  const halfLength = Math.ceil(allFields.length / 2);
  const firstHalfFields = allFields.slice(0, halfLength);
  const secondHalfFields = allFields.slice(halfLength);

  return [
    {
      fields: firstHalfFields,
    },
    {
      fields: secondHalfFields,
    },
  ];
};

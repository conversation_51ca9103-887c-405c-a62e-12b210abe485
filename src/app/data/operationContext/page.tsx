"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  Breadcrumbs,
  <PERSON><PERSON>,
  Container,
  Link,
  Menu,
  MenuItem,
  Typography,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

const OperationContextPage = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <Container>
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link underline="hover" color="inherit" href="/data">
          Data
        </Link>
        <Typography sx={{ color: "text.primary" }}>
          Operation context
        </Typography>
      </Breadcrumbs>
      <Typography variant="h4"  sx={{ mb: 5 }}>
        Operation context
      </Typography>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        flexDirection="column"
        gap={2}
      >
        <Typography
          variant="h6"
          color="text.secondary"
          maxWidth={800}
          gutterBottom
          align="center"
        >
          Add regulation that needs to be considered and they will automatically
          be retrieved into the relevant customer journey maps.
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleClick}
        >
          Add
        </Button>
        <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
          <MenuItem onClick={handleClose}>Add manually</MenuItem>
          <MenuItem onClick={handleClose}>Import</MenuItem>
        </Menu>
      </Box>
    </Container>
  );
};

export default OperationContextPage;

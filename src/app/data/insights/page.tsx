"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  Breadcrumbs,
  <PERSON>ton,
  Container,
  <PERSON>,
  Typo<PERSON>,
  Drawer,
  IconButton,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";

const InsightsPage = () => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const handleDrawerOpen = () => {
    setIsDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
  };

  return (
    <Container>
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link underline="hover" color="inherit" href="/data">
          Data
        </Link>
        <Typography color="text.primary">Voice of customer</Typography>
      </Breadcrumbs>
      <Typography variant="h4" sx={{ mb: 5 }}>
        Insights
      </Typography>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        flexDirection="column"
        gap={2}
      >
        <Typography
          variant="h6"
          color="text.secondary"
          maxWidth={600}
          gutterBottom
          align="center"
        >
          Add any opportunities you spot and they will automatically be
          retrieved into the relevant customer journey maps.
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleDrawerOpen}
        >
          Add
        </Button>
      </Box>

      <Drawer
        anchor="right"
        open={isDrawerOpen}
        onClose={handleDrawerClose}
        sx={{
          "& .MuiDrawer-paper": {
            width: "40%",
            minWidth: 400,
            padding: 3,
          },
        }}
      >
        <Box sx={{ width: "100%" }} display="flex" flexDirection="column" gap={2}>
          <IconButton
            sx={{ alignSelf: "flex-start" }}
            onClick={handleDrawerClose}
          >
            <ChevronRightIcon />
          </IconButton>
          <Typography variant="h5" gutterBottom>
            Title
          </Typography>
        </Box>
      </Drawer>
    </Container>
  );
};

export default InsightsPage;

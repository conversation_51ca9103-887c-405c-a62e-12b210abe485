import { BuildingBlockTypeDetail } from "@/types/buildingBlock";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  IconButton,
  InputAdornment,
  Menu,
  MenuItem,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import ImportModal from "./ImportModal";

interface DataTableProps {
  columns: string[]; // The table headers (display labels)
  data: Record<string, any>[]; // The parsed data
  fieldNames?: string[]; // Optional field names for accessing data
  onEdit: (row: Record<string, any>) => void; // Optional edit handler
  onDelete: (row: Record<string, any>) => void; // Optional delete handler
  onAdd?: () => void; // Optional add handler
  onRowClick?: (row: Record<string, any>) => void; // Optional row click handler
  buildingBlockType?: BuildingBlockTypeDetail; // Building block type for import modal
  onImportSuccess?: () => Promise<void>; // Optional import success handler
}

const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  fieldNames,
  onEdit,
  onDelete,
  onAdd,
  onRowClick,
  buildingBlockType,
  onImportSuccess,
}) => {
  console.log("DataTable rendered with onDelete:", onDelete);
  // If fieldNames are not provided, use columns as field names
  const fields = fieldNames || columns;
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRow, setSelectedRow] = useState<null | number>(null);
  const [addMenuAnchorEl, setAddMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [openImportModal, setOpenImportModal] = useState(false);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle pagination changes
  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // Reset to the first page
  };

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, rowIndex: number) => {
    event.stopPropagation(); // Prevent row click event
    setAnchorEl(event.currentTarget);
    setSelectedRow(rowIndex);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRow(null);
  };

  const handleDeleteBlock = () => {
    const rowData = filteredData[selectedRow];
    onDelete(rowData);
    handleMenuClose();
  };

  const handleEditBlock = () => {
    const rowData = filteredData[selectedRow];
    onEdit(rowData);
    handleMenuClose();
  };

  // Add button menu handlers
  const handleAddMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAddMenuAnchorEl(event.currentTarget);
  };

  const handleAddMenuClose = () => {
    setAddMenuAnchorEl(null);
  };

  const handleAddClick = () => {
    if (onAdd) {
      onAdd();
    }
    handleAddMenuClose();
  };

  const handleImportClick = () => {
    setOpenImportModal(true);
    handleAddMenuClose();
  };

  const handleImportModalClose = () => {
    setOpenImportModal(false);
  };

  // Filter data based on the search query
  const filteredData = data.filter((row) => {
    return Object.entries(row).some(([, value]) => {
      if (Array.isArray(value)) {
        return value.some((item) => String(item).toLowerCase().includes(searchQuery.toLowerCase()));
      }
      return String(value).toLowerCase().includes(searchQuery.toLowerCase());
    });
  });

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
        <TextField
          label="Search"
          variant="outlined"
          value={searchQuery}
          onChange={handleSearchChange}
          margin="normal"
          sx={{
            width: "20rem", // Fixed width for the search bar
            height: "5rem",
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: "rgba(255, 255, 255, 0.23)",
              },
              "&:hover fieldset": {
                borderColor: "rgba(255, 255, 255, 0.5)",
              },
            },
            "& .MuiInputLabel-root": {
              color: "rgba(255, 255, 255, 0.7)",
            },
            "& .MuiInputBase-input": {
              color: "rgba(255, 255, 255, 0.9)",
            },
          }}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: "rgba(255, 255, 255, 0.7)" }} />
                </InputAdornment>
              ),
            },
          }}
        />

        {onAdd && (
          <Box>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddMenuOpen}
              sx={{ height: 40, mt: 1 }}
            >
              Add
            </Button>
            <Menu
              anchorEl={addMenuAnchorEl}
              open={Boolean(addMenuAnchorEl)}
              onClose={handleAddMenuClose}
            >
              <MenuItem onClick={handleAddClick}>Add manually</MenuItem>
              <MenuItem onClick={handleImportClick}>Import</MenuItem>
            </Menu>
          </Box>
        )}
      </Stack>

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column}
                  sx={{
                    borderBottom: "2px solid rgba(255, 255, 255, 0.12)", // Consistent bottom border
                    color: "rgba(255, 255, 255, 0.9)",
                    padding: "16px",
                  }}
                >
                  <Typography variant="body2" fontWeight="bold">
                    {column}
                  </Typography>
                </TableCell>
              ))}
              {/* Add an extra header cell for the action button */}
              <TableCell
                sx={{
                  borderBottom: "2px solid rgba(255, 255, 255, 0.12)",
                  padding: "16px",
                  width: "48px", // Fixed width for action column
                }}
              />
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredData
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((row, rowIndex) => (
                <TableRow
                  key={rowIndex}
                  hover
                  onClick={() => onRowClick && onRowClick(row)}
                  sx={{
                    "&:hover": {
                      backgroundColor: "rgba(255, 255, 255, 0.08)",
                    },
                    cursor: onRowClick ? "pointer" : "default",
                  }}
                >
                  {columns.map((_column, cellIndex) => (
                    <TableCell
                      key={cellIndex}
                      sx={{
                        borderBottom: "1px solid rgba(255, 255, 255, 0.12)", // Consistent with header
                        color: "rgba(255, 255, 255, 0.9)",
                        padding: "16px",
                      }}
                    >
                      {row[fields[cellIndex]] !== undefined ? String(row[fields[cellIndex]]) : ""}
                    </TableCell>
                  ))}
                  {/* Add action button cell */}
                  <TableCell
                    sx={{
                      borderBottom: "1px solid rgba(255, 255, 255, 0.12)",
                      padding: "8px 16px",
                      width: "48px", // Fixed width for action column
                    }}
                  >
                    <IconButton
                      onClick={(event) => handleMenuOpen(event, rowIndex + page * rowsPerPage)}
                      size="small"
                      sx={{ color: "rgba(255, 255, 255, 0.7)" }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                colSpan={columns.length + 1} // +1 for the action column
                count={filteredData.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Rows per page"
                sx={{
                  color: "rgba(255, 255, 255, 0.7)",
                  borderBottom: "none",
                  "& .MuiTablePagination-selectIcon": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                  "& .MuiTablePagination-actions": {
                    color: "rgba(255, 255, 255, 0.7)",
                  },
                }}
              />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>

      {/* Row action dropdown Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        onClick={(event) => event.stopPropagation()}
        slotProps={{
          paper: {
            sx: {
              backgroundColor: "#1e1e1e", // Dark background for the menu
              color: "rgba(255, 255, 255, 0.9)",
              boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
            },
          },
        }}
      >
        <MenuItem
          onClick={handleEditBlock}
          disabled={!onEdit}
          sx={{
            "&.Mui-disabled": {
              opacity: 0.5,
              color: "rgba(255, 255, 255, 0.5)",
            },
          }}
        >
          Edit
        </MenuItem>
        <MenuItem
          onClick={handleDeleteBlock}
          disabled={!onDelete}
          sx={{
            "&.Mui-disabled": {
              opacity: 0.5,
              color: "rgba(255, 255, 255, 0.5)",
            },
          }}
        >
          Delete
        </MenuItem>
      </Menu>

      {/* Import Modal */}
      {buildingBlockType && (
        <ImportModal
          open={openImportModal}
          onClose={handleImportModalClose}
          buildingBlockType={buildingBlockType}
          onImportSuccess={onImportSuccess}
        />
      )}
    </Box>
  );
};

export default DataTable;

"use client";

import FileUpload from "@/components/FileUpload";
import Modal from "@/components/Modal";
import buildingBlockService from "@/services/BuildingBlockService";
import { BuildingBlockTypeDetail } from "@/types/buildingBlock";
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";

interface ImportModalProps {
  open: boolean;
  onClose: () => void;
  buildingBlockType: BuildingBlockTypeDetail;
  onImportSuccess?: () => Promise<void>;
}

const ImportModal: React.FC<ImportModalProps> = ({
  open,
  onClose,
  buildingBlockType,
  onImportSuccess,
}) => {
  const [selectedImportType, setSelectedImportType] = useState<number | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<string>("");
  const [taskId, setTaskId] = useState<string | null>(null);

  // Set default import type when modal opens
  useEffect(() => {
    if (open && buildingBlockType) {
      if (buildingBlockType.sub_types.length > 0) {
        setSelectedImportType(buildingBlockType.sub_types[0].id);
      } else {
        setSelectedImportType(buildingBlockType.id);
      }
    }
  }, [open, buildingBlockType]);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setSelectedImportType(null);
      setUploadedFile(null);
      setIsUploading(false);
      setUploadProgress(0);
      setUploadStatus("");
      setTaskId(null);
    }
  }, [open]);

  const handleTypeChange = (event: SelectChangeEvent<string>) => {
    setSelectedImportType(Number(event.target.value));
  };

  const handleFileUpload = async (file: File) => {
    // Validate that we received an actual File object
    if (!(file instanceof File)) {
      console.error("Invalid file: Expected a File object, received:", typeof file);
      setUploadStatus("Error: Invalid file object");
      return Promise.reject(new Error("Invalid file object"));
    }

    // Additional client-side validation
    if (!file.name.toLowerCase().endsWith(".csv")) {
      console.error("Invalid file type:", file.name);
      setUploadStatus("Error: Only CSV files are supported");
      return Promise.reject(new Error("Invalid file type"));
    }

    console.log("File selected:", {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
    });

    setUploadedFile(file);
    setUploadStatus(""); // Clear any previous error status
    return Promise.resolve();
  };

  const handleFileDelete = () => {
    setUploadedFile(null);
  };

  // Function to poll task status
  const pollTaskStatus = async (taskId: string) => {
    const maxAttempts = 60; // Poll for up to 5 minutes (60 * 5 seconds)
    let attempts = 0;

    const poll = async (): Promise<void> => {
      try {
        const statusResponse = await buildingBlockService.getBuildingBlockTaskStatus(taskId);

        setUploadStatus(statusResponse.message);

        if (statusResponse.ready) {
          setUploadProgress(100);
          setUploadStatus("Upload completed successfully!");

          // Call success callback to refresh data
          if (onImportSuccess) {
            await onImportSuccess();
          }

          // Close modal after a short delay
          setTimeout(() => {
            onClose();
          }, 1500);
          return;
        }

        attempts++;
        if (attempts >= maxAttempts) {
          throw new Error("Upload timeout - please check the status manually");
        }

        // Update progress based on attempts (rough estimate)
        setUploadProgress(Math.min(90, (attempts / maxAttempts) * 90));

        // Poll again after 5 seconds
        setTimeout(() => poll(), 5000);
      } catch (error) {
        console.error("Error polling task status:", error);
        setUploadStatus(`Error: ${error.message || "Failed to check upload status"}`);
        setIsUploading(false);
      }
    };

    await poll();
  };

  const handleImportSubmit = async () => {
    if (!uploadedFile) {
      console.warn("No file selected for import");
      setUploadStatus("Error: No file selected");
      return;
    }

    if (!selectedImportType) {
      console.warn("No import type selected");
      setUploadStatus("Error: No import type selected");
      return;
    }

    // Final validation that we have a proper File object
    if (!(uploadedFile instanceof File)) {
      console.error("Invalid file object at upload time:", uploadedFile);
      setUploadStatus("Error: Invalid file object");
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(10);
      setUploadStatus("Preparing file upload...");

      console.log("Starting upload with:", {
        subTypeId: selectedImportType,
        fileName: uploadedFile.name,
        fileSize: uploadedFile.size,
        fileType: uploadedFile.type,
        lastModified: uploadedFile.lastModified,
        isFileInstance: uploadedFile instanceof File,
        fileConstructor: uploadedFile.constructor.name,
      });

      // Additional check: try to read a small portion of the file to verify it has content
      const reader = new FileReader();
      reader.onload = (e) => {
        const preview = e.target?.result as string;
        console.log("File content preview (first 100 chars):", preview.substring(0, 100));
      };
      reader.readAsText(uploadedFile.slice(0, 100));

      // Upload the file (this sends the actual file content, not path)
      const uploadResponse = await buildingBlockService.uploadBuildingBlocks(
        selectedImportType,
        uploadedFile
      );

      console.log("Upload response:", uploadResponse);

      setTaskId(uploadResponse.task_id);
      setUploadProgress(20);
      setUploadStatus(uploadResponse.message);

      // Start polling for task status
      await pollTaskStatus(uploadResponse.task_id);
    } catch (error) {
      console.error("Import failed:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      setUploadStatus(`Upload failed: ${errorMessage}`);
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Modal open={open} onClose={onClose} title={`Import ${buildingBlockType.name}`}>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
        {/* Type Selection Dropdown */}
        {buildingBlockType.sub_types.length > 0 && (
          <FormControl fullWidth>
            <InputLabel id="import-type-select-label">Select type</InputLabel>
            <Select
              labelId="import-type-select-label"
              value={selectedImportType?.toString() || ""}
              label="Select type"
              onChange={handleTypeChange}
            >
              {buildingBlockType.sub_types.map((subType) => (
                <MenuItem key={subType.id} value={subType.id.toString()}>
                  {subType.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {/* File Upload Component */}
        <FileUpload
          onUpload={handleFileUpload}
          onFileSelect={setUploadedFile}
          onFileDelete={handleFileDelete}
        />

        {/* Upload Progress and Status */}
        {(isUploading || uploadStatus.startsWith("Error:")) && (
          <Box sx={{ mt: 2 }}>
            <Typography
              variant="body2"
              color={uploadStatus.startsWith("Error:") ? "error" : "text.secondary"}
              sx={{ mb: 1 }}
            >
              {uploadStatus}
            </Typography>
            {isUploading && (
              <>
                <LinearProgress variant="determinate" value={uploadProgress} />
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 0.5, display: "block" }}
                >
                  {Math.round(uploadProgress)}% complete
                </Typography>
              </>
            )}
          </Box>
        )}

        {/* Action Buttons */}
        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 2 }}>
          <Button variant="outlined" onClick={handleCancel} disabled={isUploading}>
            CANCEL
          </Button>
          <Button
            variant="contained"
            onClick={handleImportSubmit}
            disabled={!uploadedFile || isUploading}
          >
            {isUploading ? "UPLOADING..." : "UPLOAD"}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default ImportModal;

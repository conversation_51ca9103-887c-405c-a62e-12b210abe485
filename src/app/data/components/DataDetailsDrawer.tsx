"use client";

import { BuildingBlockTypeDetail } from "@/types/buildingBlock";
import CloseIcon from "@mui/icons-material/Close";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  Box,
  Chip,
  Divider,
  Drawer,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

interface DataDetailsDrawerProps {
  open: boolean;
  onClose: () => void;
  rowData: Record<string, any> | null;
  buildingBlockType: BuildingBlockTypeDetail;
  commonData?: any; // For looking up foreign key values
  onEdit?: (row: Record<string, any>) => void;
  onDelete?: (row: Record<string, any>) => void;
}

const DataDetailsDrawer: React.FC<DataDetailsDrawerProps> = ({
  open,
  onClose,
  rowData,
  buildingBlockType,
  onEdit,
  onDelete,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);

  if (!rowData) return null;

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(rowData);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    onDelete(rowData);
    handleMenuClose();
  };

  // Helper function to format field values
  const formatFieldValue = (fieldName: string, value: any, field: any) => {
    if (value === null || value === undefined) {
      return "N/A";
    }

    // If no field definition exists, treat as a simple content field and display the value as-is
    if (!field) {
      return value?.toString() || "N/A";
    }

    // Handle different field types
    switch (field.type) {
      case "date":
      case "datetime":
        if (value) {
          const date = new Date(value);
          return date.toLocaleDateString();
        }
        return "N/A";

      // case "foreign_key":
      //   // Look up the foreign key value from common data
      //   if (field.values?.source && commonData?.[field.values.source]) {
      //     const sourceData = commonData[field.values.source];
      //     const item = sourceData.find((item: any) => item.id === value);
      //     return item ? item.name : "N/A";
      //   }
      //   return "N/A";

      case "multi_select":
        if (field.values?.options) {
          const option = field.values.options.find((opt: any) => opt.value === value);
          return option ? option.name : value;
        }
        return value;

      case "number":
        return typeof value === "number" ? value.toString() : "N/A";

      default:
        return value?.toString() || "N/A";
    }
  };

  // Helper function to get field definition
  const getFieldDefinition = (fieldName: string) => {
    return buildingBlockType.fields?.fields?.find((field) => field.name === fieldName);
  };

  // Helper function to render sentiment chip with appropriate color
  const renderSentimentChip = (sentiment: string) => {
    const colors = {
      promoters: "success",
      passives: "warning",
      detractors: "error",
    } as const;

    const color = colors[sentiment?.toLowerCase() as keyof typeof colors] || "default";
    return <Chip label={sentiment} color={color} size="small" />;
  };

  // Get sub-type name
  const getSubTypeName = () => {
    const subTypeId = fullBlock.sub_type;
    if (subTypeId && buildingBlockType.sub_types) {
      const subType = buildingBlockType.sub_types.find((st) => st.id === subTypeId);
      return subType ? subType.name : "N/A";
    }
    return "N/A";
  };

  // Get the full block data if available
  const fullBlock = rowData._fullBlock || rowData;

  // Helper function to capitalize first letter
  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  // Generate detail fields dynamically from fullBlock.content
  const generateDetailFields = () => {
    const fields = [];

    // Add fixed fields first
    fields.push({ key: "created_by", label: "Created by", value: "N/A" }); // Placeholder for user
    fields.push({ key: "uploaded_date", label: "Uploaded date", value: fullBlock.created_at });

    // Add all content fields dynamically
    if (fullBlock.content && typeof fullBlock.content === "object") {
      Object.entries(fullBlock.content).forEach(([key, value]) => {
        fields.push({
          key,
          label: capitalizeFirstLetter(key.replace(/_/g, " ")), // Convert snake_case to Title Case
          value: value,
        });
      });
    }

    return fields;
  };

  const detailFields = generateDetailFields();

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        "& .MuiDrawer-paper": {
          width: "400px",
          backgroundColor: "#1a1a1a",
          color: "white",
          padding: 0,
        },
      }}
    >
      <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
        {/* Header */}
        <Box
          sx={{
            p: 2,
            borderBottom: "1px solid rgba(255, 255, 255, 0.12)",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <IconButton onClick={onClose} sx={{ color: "white" }}>
            <CloseIcon />
          </IconButton>
          <Stack direction="row" spacing={1}>
            <IconButton onClick={handleMenuOpen} sx={{ color: "white" }}>
              <MoreVertIcon />
            </IconButton>
          </Stack>
        </Box>

        {/* Dropdown Menu */}
        <Menu
          anchorEl={anchorEl}
          open={menuOpen}
          onClose={handleMenuClose}
          sx={{
            "& .MuiPaper-root": {
              backgroundColor: "#2a2a2a",
              color: "white",
            },
          }}
        >
          <MenuItem onClick={handleEdit} sx={{ color: "white" }}>
            Edit
          </MenuItem>
          <MenuItem onClick={handleDelete} sx={{ color: "white" }}>
            Delete
          </MenuItem>
        </Menu>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: "auto" }}>
          {/* Title */}
          <Box sx={{ p: 3, pb: 2 }}>
            <Typography variant="body2" sx={{ color: "white", fontWeight: "bold", mb: 2 }}>
              {getSubTypeName()}
            </Typography>
            <Typography variant="body2" sx={{ color: "white", fontWeight: "bold" }}>
              {fullBlock.name || "N/A"}
            </Typography>
          </Box>

          {/* Details Section */}
          <Box sx={{ px: 3 }}>
            {detailFields.map((field, index) => {
              const fieldDef = getFieldDefinition(field.key);
              let displayValue: string;

              // Handle special cases
              if (field.key === "uploaded_date") {
                displayValue = field.value
                  ? formatFieldValue("created_at", field.value, { type: "datetime" })
                  : "N/A";
              } else {
                displayValue = formatFieldValue(field.key, field.value, fieldDef);
              }

              return (
                <Box key={index} sx={{ mb: 2 }}>
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Typography variant="body2" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                      {field.label}
                    </Typography>
                    <Box>
                      {field.key === "sentiment" && field.value ? (
                        renderSentimentChip(field.value)
                      ) : (
                        <Typography variant="body2" sx={{ color: "white" }}>
                          {displayValue}
                        </Typography>
                      )}
                    </Box>
                  </Stack>
                </Box>
              );
            })}

            {/* Component Section */}
            <Box sx={{ mt: 3, mb: 2 }}>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="body2" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
                  Component
                </Typography>
                <Typography variant="body2" sx={{ color: "white" }}>
                  N/A
                </Typography>
              </Stack>
            </Box>

            <Divider sx={{ my: 3, borderColor: "rgba(255, 255, 255, 0.12)" }} />

            {/* Description Section */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ color: "white", mb: 2, fontWeight: "bold" }}>
                Description
              </Typography>
              <Typography variant="body2" sx={{ color: "rgba(255, 255, 255, 0.8)" }}>
                {fullBlock.desc || "N/A"}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Drawer>
  );
};

export default DataDetailsDrawer;

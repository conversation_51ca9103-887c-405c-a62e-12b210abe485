"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Typography } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

const AnalyticsPage = () => {
  return (
    <Container>
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link underline="hover" color="inherit" href="/data">
          Data
        </Link>
        <Typography sx={{ color: "text.primary" }}>Analytics</Typography>
      </Breadcrumbs>
      <Typography variant="h4" gutterBottom>
        Analytics
      </Typography>
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        flexDirection="column"
        gap={2}
      >
        <Typography
          variant="h6"
          color="text.secondary"
          maxWidth={800}
          gutterBottom
          align="center"
        >
          Add data when you&apos;re ready to integrate it. <br />
          This data will serve as a building block for your customer journey map.
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
        >
          Add
        </Button>
      </Box>
    </Container>
  );
};

export default AnalyticsPage;

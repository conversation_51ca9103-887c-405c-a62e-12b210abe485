"use client";

import ConfirmDialog from "@/components/ConfirmDialog";
import DataCreationModal from "@/components/DataCreationModal";
import { useAppContext } from "@/context/AppProvider";
import buildingBlockService from "@/services/BuildingBlockService";
import { BuildingBlockTypeDetail } from "@/types/buildingBlock";
import AddIcon from "@mui/icons-material/Add";
import {
  Box,
  Breadcrumbs,
  Button,
  CircularProgress,
  Container,
  Link,
  Menu,
  MenuItem,
  Tab,
  Tabs,
  Typography,
} from "@mui/material";
import dayjs from "dayjs";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import DataDetailsDrawer from "../components/DataDetailsDrawer";
import DataTable from "../components/DataTable";
import ImportModal from "../components/ImportModal";
import {
  getFormFields as getDataFormFields,
  prepareFormData,
  processFormValues,
} from "../forms/DataForm";

// Extend the BuildingBlockTypeDetail interface to include columns
interface ExtendedBuildingBlockTypeDetail extends BuildingBlockTypeDetail {
  columns?: { field_name: string }[];
}

// Define a simplified BuildingBlock interface for our needs
interface SimpleBuildingBlock {
  id: number;
  type: number | any;
  sub_type: number | any;
  role: number | null;
  brand: number | null;
  segment: number | null;
  name: string;
  desc: string;
  order: number;
  content?: any;
  created_at: string;
  updated_at: string;
  [key: string]: any;
}

const DataDetailsPage = () => {
  const { commonData } = useAppContext();

  // Helper function to convert date strings to dayjs objects for form fields
  const convertDatesToDayjs = (
    data: Record<string, any>,
    buildingBlockType: ExtendedBuildingBlockTypeDetail | null
  ) => {
    if (!buildingBlockType?.fields?.fields) return data;

    const convertedData = { ...data };

    // Find date fields in the building block type definition
    buildingBlockType.fields.fields.forEach((field: any) => {
      if (field.type === "date" && convertedData[field.name]) {
        const dateValue = convertedData[field.name];
        // Convert string dates to dayjs objects
        if (typeof dateValue === "string" && dateValue.trim() !== "") {
          try {
            // Try to create a dayjs object from the string
            const dayjsDate = dayjs(dateValue);
            // Only use the dayjs object if it's valid
            if (dayjsDate && typeof dayjsDate.isValid === "function" && dayjsDate.isValid()) {
              convertedData[field.name] = dayjsDate;
            } else {
              console.warn(`Invalid date value for field ${field.name}:`, dateValue);
              // For invalid dates, try to create a dayjs object from current date as fallback
              convertedData[field.name] = null;
            }
          } catch (error) {
            console.error(`Error converting date for field ${field.name}:`, error);
            // Set to null if conversion fails completely
            convertedData[field.name] = null;
          }
        }
      }
    });

    return convertedData;
  };
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [buildingBlockType, setBuildingBlockType] =
    useState<ExtendedBuildingBlockTypeDetail | null>(null);
  const [buildingBlocks, setBuildingBlocks] = useState<SimpleBuildingBlock[]>([]);
  const [openModal, setOpenModal] = useState(false);
  const [openImportModal, setOpenImportModal] = useState(false);
  const [selectedSubType, setSelectedSubType] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [hasData, setHasData] = useState(false);
  const [hasAnyData, setHasAnyData] = useState(false);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState<Record<string, any> | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deleteConfirmData, setDeleteConfirmData] = useState<{
    row: Record<string, any>;
    blockId: number;
  } | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingBlock, setEditingBlock] = useState<Record<string, any> | null>(null);
  const open = Boolean(anchorEl);
  const params = useParams();
  const typeId = params.id ? parseInt(params.id as string) : null;

  useEffect(() => {
    const fetchData = async () => {
      if (!typeId) {
        setError("Invalid building block type ID");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch building block type details
        const typeData = await buildingBlockService.getBuildingBlockTypeDetail(typeId);
        setBuildingBlockType(typeData as ExtendedBuildingBlockTypeDetail);

        // Fetch building blocks
        const blocksData = await buildingBlockService.getBuildingBlocks(typeId);
        // Convert to SimpleBuildingBlock format
        const simplifiedBlocks = blocksData.map((block) => ({
          id: block.id,
          type: typeof block.type === "object" ? block.type.id : block.type,
          sub_type: block.sub_type,
          role: block.role,
          brand: block.brand,
          segment: block.segment,
          name: block.name,
          desc: block.desc,
          order: block.order,
          content: (block as any).content,
          created_at: block.created_at,
          updated_at: block.updated_at,
        }));
        setBuildingBlocks(simplifiedBlocks);

        // Check if there's any data at all
        const anyData = simplifiedBlocks.length > 0;
        setHasAnyData(anyData);

        // Check if there's data for the current tab (or no tabs)
        const currentTabData =
          typeData.sub_types.length === 0
            ? anyData
            : simplifiedBlocks.some((block) => Number(block.sub_type) === typeData.sub_types[0].id);

        // Debug logging for hasData logic
        console.log("HasData debug:", {
          anyData,
          hasSubTypes: typeData.sub_types.length > 0,
          firstSubTypeId: typeData.sub_types.length > 0 ? typeData.sub_types[0].id : null,
          simplifiedBlocksSubTypes: simplifiedBlocks.map((b) => b.sub_type),
          currentTabData,
        });

        setHasData(currentTabData);

        // Set default selected sub-type and active tab if available
        if (typeData.sub_types.length > 0) {
          setSelectedSubType(typeData.sub_types[0].id);
          setActiveTab(0); // Select the first tab by default
        }
      } catch (err) {
        console.error("Failed to fetch data:", err);
        setError("Failed to load data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [typeId]);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleOpenModal = () => {
    setOpenModal(true);
    setAnchorEl(null);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setSelectedSubType(null);
    setIsEditMode(false);
    setEditingBlock(null);
  };

  const handleOpenImportModal = () => {
    setOpenImportModal(true);
    setAnchorEl(null);
  };

  const handleCloseImportModal = () => {
    setOpenImportModal(false);
  };

  const handleImportSuccess = async () => {
    if (!typeId) return;

    try {
      // Refresh the building blocks data for the current sub-type
      const blocksData = selectedSubType
        ? await buildingBlockService.getBuildingBlocks(typeId, selectedSubType)
        : await buildingBlockService.getBuildingBlocks(typeId);

      // Convert to SimpleBuildingBlock format
      const simplifiedBlocks = blocksData.map((block) => ({
        id: block.id,
        type: typeof block.type === "object" ? block.type.id : block.type,
        sub_type: block.sub_type,
        role: block.role,
        brand: block.brand,
        segment: block.segment,
        name: block.name,
        desc: block.desc,
        order: block.order,
        content: (block as any).content,
        created_at: block.created_at,
        updated_at: block.updated_at,
      }));

      setBuildingBlocks(simplifiedBlocks);

      // Update data state
      const currentTabHasData = simplifiedBlocks.length > 0;
      setHasData(currentTabHasData);

      // Also fetch all blocks to check if any tab has data
      const allBlocksData = await buildingBlockService.getBuildingBlocks(typeId);
      setHasAnyData(allBlocksData.length > 0);
    } catch (error) {
      console.error("Failed to refresh data after import:", error);
    }
  };

  const handleTabChange = async (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    if (buildingBlockType && buildingBlockType.sub_types.length > newValue) {
      const subTypeId = buildingBlockType.sub_types[newValue].id;
      setSelectedSubType(subTypeId);

      // Fetch building blocks for the selected sub-type
      if (typeId) {
        try {
          const blocksData = await buildingBlockService.getBuildingBlocks(typeId, subTypeId);
          // Convert to SimpleBuildingBlock format
          const simplifiedBlocks = blocksData.map((block) => ({
            id: block.id,
            type: typeof block.type === "object" ? block.type.id : block.type,
            sub_type: block.sub_type,
            role: block.role,
            brand: block.brand,
            segment: block.segment,
            name: block.name,
            desc: block.desc,
            order: block.order,
            content: (block as any).content,
            created_at: block.created_at,
            updated_at: block.updated_at,
          }));
          setBuildingBlocks(simplifiedBlocks);

          // Update hasData for the current tab
          setHasData(simplifiedBlocks.length > 0);
        } catch (err) {
          console.error("Failed to fetch building blocks for sub-type:", err);
        }
      }
    }
  };

  const handleFormSubmit = async (values: any) => {
    try {
      if (!typeId) return;

      // Process form values to handle date objects
      const processedValues = processFormValues(values);

      // Prepare form data for submission
      const formData = prepareFormData(processedValues, buildingBlockType, selectedSubType);

      await buildingBlockService.createBuildingBlock(typeId, formData);
      handleCloseModal();

      // Refresh the building blocks data for the current sub-type
      const blocksData = selectedSubType
        ? await buildingBlockService.getBuildingBlocks(typeId, selectedSubType)
        : await buildingBlockService.getBuildingBlocks(typeId);

      // Convert to SimpleBuildingBlock format
      const simplifiedBlocks = blocksData.map((block) => ({
        id: block.id,
        type: typeof block.type === "object" ? block.type.id : block.type,
        sub_type: block.sub_type,
        role: block.role,
        brand: block.brand,
        segment: block.segment,
        name: block.name,
        desc: block.desc,
        order: block.order,
        content: (block as any).content,
        created_at: block.created_at,
        updated_at: block.updated_at,
      }));

      setBuildingBlocks(simplifiedBlocks);

      // Update data state
      const currentTabHasData = simplifiedBlocks.length > 0;
      setHasData(currentTabHasData);

      // Also fetch all blocks to check if any tab has data
      const allBlocksData = await buildingBlockService.getBuildingBlocks(typeId);
      setHasAnyData(allBlocksData.length > 0);
    } catch (error) {
      console.error("Failed to create building block:", error);
    }
  };

  // Handler for edit form submission
  const handleEditFormSubmit = async (values: any) => {
    try {
      if (!typeId || !editingBlock?.id) return;

      // Process form values to handle date objects
      const processedValues = processFormValues(values);

      // Prepare form data for submission
      const formData = prepareFormData(processedValues, buildingBlockType, selectedSubType);

      await buildingBlockService.updateBuildingBlock(typeId, editingBlock.id, formData);
      handleCloseModal();

      // Refresh the building blocks data for the current sub-type
      const blocksData = selectedSubType
        ? await buildingBlockService.getBuildingBlocks(typeId, selectedSubType)
        : await buildingBlockService.getBuildingBlocks(typeId);

      // Convert to SimpleBuildingBlock format
      const simplifiedBlocks = blocksData.map((block) => ({
        id: block.id,
        type: typeof block.type === "object" ? block.type.id : block.type,
        sub_type: block.sub_type,
        role: block.role,
        brand: block.brand,
        segment: block.segment,
        name: block.name,
        desc: block.desc,
        order: block.order,
        content: (block as any).content,
        created_at: block.created_at,
        updated_at: block.updated_at,
      }));

      setBuildingBlocks(simplifiedBlocks);

      // Update data state
      const currentTabHasData = simplifiedBlocks.length > 0;
      setHasData(currentTabHasData);

      // Also fetch all blocks to check if any tab has data
      const allBlocksData = await buildingBlockService.getBuildingBlocks(typeId);
      setHasAnyData(allBlocksData.length > 0);
    } catch (error) {
      console.error("Failed to update building block:", error);
    }
  };

  // Handler for editing a building block
  const handleEditBlock = (row: Record<string, any>) => {
    console.log("Edit block:", row);

    // Get the full block data - check both _fullBlock and the row itself
    const fullBlock = row._fullBlock || row;
    const blockId = fullBlock.id || row.id;

    if (!blockId) {
      console.error("No block ID found for editing");
      return;
    }

    console.log("Full block data for editing:", fullBlock);
    console.log("Full block content:", fullBlock.content);

    // Set edit mode and store the block being edited
    setIsEditMode(true);
    setEditingBlock(fullBlock);

    // Set the selected sub type based on the block's sub_type
    setSelectedSubType(fullBlock.sub_type);

    // Open the modal
    setOpenModal(true);
  };

  // Handler for deleting a building block
  const handleDeleteBlock = (row: Record<string, any>) => {
    console.log("handleDeleteBlock called with:", row);

    // Get the actual ID from _fullBlock if available, otherwise use row.id
    const blockId = row._fullBlock?.id || row.id;
    console.log("Extracted blockId:", blockId, "typeId:", typeId);

    if (!typeId || !blockId) {
      console.log("Missing typeId or blockId, returning early");
      return;
    }

    // Set up confirmation dialog
    setDeleteConfirmData({ row, blockId });
    setDeleteConfirmOpen(true);
    console.log("Confirmation dialog should open");
  };

  // Handler for confirming deletion
  const handleConfirmDelete = async () => {
    if (!deleteConfirmData) return;

    try {
      await buildingBlockService.deleteBuildingBlock(typeId, deleteConfirmData.blockId);

      // Refresh the data after deletion
      const blocksData = selectedSubType
        ? await buildingBlockService.getBuildingBlocks(typeId, selectedSubType)
        : await buildingBlockService.getBuildingBlocks(typeId);

      // Convert to SimpleBuildingBlock format
      const simplifiedBlocks = blocksData.map((block) => ({
        id: block.id,
        type: typeof block.type === "object" ? block.type.id : block.type,
        sub_type: block.sub_type,
        role: block.role,
        brand: block.brand,
        segment: block.segment,
        name: block.name,
        desc: block.desc,
        order: block.order,
        content: (block as any).content,
        created_at: block.created_at,
        updated_at: block.updated_at,
      }));

      setBuildingBlocks(simplifiedBlocks);

      // Update data state
      const currentTabHasData = simplifiedBlocks.length > 0;
      setHasData(currentTabHasData);

      // Also fetch all blocks to check if any tab has data
      const allBlocksData = await buildingBlockService.getBuildingBlocks(typeId);
      setHasAnyData(allBlocksData.length > 0);

      // Close the confirmation dialog
      setDeleteConfirmOpen(false);
      setDeleteConfirmData(null);
      handleCloseDrawer();
    } catch (error) {
      console.error("Failed to delete building block:", error);
      // Close the confirmation dialog even on error
      setDeleteConfirmOpen(false);
      setDeleteConfirmData(null);
    }
  };

  // Handler for canceling deletion
  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setDeleteConfirmData(null);
  };

  // Handler for row click to open drawer
  const handleRowClick = (row: Record<string, any>) => {
    setSelectedRowData(row);
    setOpenDrawer(true);
  };

  // Handler for closing drawer
  const handleCloseDrawer = () => {
    setOpenDrawer(false);
    setSelectedRowData(null);
  };

  // Use the imported getFormFields function
  const getFormFields = () => {
    if (!buildingBlockType) {
      return [];
    }

    return getDataFormFields(buildingBlockType, commonData);
  };

  // Prepare data for the DataTable component
  const getTableData = () => {
    if (!buildingBlockType || !buildingBlockType.fields || !buildingBlockType.fields.fields) {
      return { columns: [], data: [] };
    }

    // Get fields that should be displayed in the table
    let displayFields = buildingBlockType.fields.fields.filter(
      (field) => field.display_in_table === true
    );

    // Fallback: if no fields are marked for table display, show basic fields
    if (displayFields.length === 0) {
      displayFields = buildingBlockType.fields.fields.filter(
        (field) => field.name === "name" || field.name === "desc"
      );
    }

    // Sort fields by display_order if available
    displayFields.sort((a, b) => {
      // If both have display_order, sort by it
      if (a.display_order !== null && b.display_order !== null) {
        return (a.display_order || 0) - (b.display_order || 0);
      }
      // If only a has display_order, a comes first
      if (a.display_order !== null) return -1;
      // If only b has display_order, b comes first
      if (b.display_order !== null) return 1;
      // If neither has display_order, keep original order
      return 0;
    });

    // Extract field names and labels
    const fieldNames = displayFields.map((field) => field.name);
    const columnLabels = displayFields.map((field) => field.label);

    // Filter building blocks for the current sub-type
    const filteredBlocks = buildingBlocks.filter((block) =>
      selectedSubType ? block.sub_type === selectedSubType : true
    );

    // Map the building blocks to the required format for the DataTable
    const tableData = filteredBlocks.map((block) => {
      const rowData: Record<string, any> = {};

      // Add all fields that should be displayed in the table
      fieldNames.forEach((fieldName) => {
        if (fieldName in block) {
          rowData[fieldName] = block[fieldName];
        } else if (block.content && fieldName in block.content) {
          rowData[fieldName] = block.content[fieldName];
        }
      });

      // Include the full block data for the drawer
      rowData._fullBlock = block;

      return rowData;
    });

    // Debug logging to help troubleshoot
    console.log("Table data debug:", {
      displayFields: displayFields.map((f) => ({
        name: f.name,
        display_in_table: f.display_in_table,
      })),
      fieldNames,
      columnLabels,
      filteredBlocksCount: filteredBlocks.length,
      tableDataCount: tableData.length,
      sampleTableData: tableData[0],
    });

    return { columns: columnLabels, data: tableData, fieldNames };
  };

  if (loading) {
    return (
      <Container>
        <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (error || !buildingBlockType) {
    return (
      <Container>
        <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
          <Typography color="error">{error || "Building block type not found"}</Typography>
        </Box>
      </Container>
    );
  }

  const formGroups = getFormFields();
  const { columns, data, fieldNames } = getTableData();

  return (
    <Container>
      <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
        <Link underline="hover" color="inherit" href="/data">
          Data
        </Link>
        <Typography color="text.primary">{buildingBlockType.name}</Typography>
      </Breadcrumbs>

      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
        <Typography variant="h4">{buildingBlockType.name}</Typography>
      </Box>

      {buildingBlockType.sub_types.length > 0 && hasAnyData && (
        <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 4 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            aria-label="sub-type tabs"
            textColor="primary"
            indicatorColor="primary"
            sx={{
              "& .MuiTabs-indicator": {
                height: 3,
              },
            }}
          >
            {buildingBlockType.sub_types.map((subType) => (
              <Tab
                key={subType.id}
                label={subType.name.toUpperCase()}
                sx={{
                  fontWeight: "bold",
                  "&.Mui-selected": { fontWeight: "bold" },
                }}
              />
            ))}
          </Tabs>
        </Box>
      )}

      {hasData ? (
        <Box sx={{ mt: 4 }}>
          <DataTable
            columns={columns}
            data={data}
            fieldNames={fieldNames}
            onEdit={handleEditBlock}
            onDelete={handleDeleteBlock}
            onAdd={handleOpenModal}
            onRowClick={handleRowClick}
            buildingBlockType={buildingBlockType}
            onImportSuccess={handleImportSuccess}
          />
        </Box>
      ) : (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          flexDirection="column"
          gap={3}
          sx={{ mt: 4, mb: 4, py: 6 }}
        >
          <Typography variant="h6" color="text.secondary" gutterBottom>
            Upload data for {buildingBlockType.name.toLowerCase()}
          </Typography>

          <Button variant="contained" startIcon={<AddIcon />} onClick={handleClick} sx={{ mt: 2 }}>
            Add
          </Button>
          <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
            <MenuItem onClick={handleOpenModal}>Add manually</MenuItem>
            <MenuItem onClick={handleOpenImportModal}>Import</MenuItem>
          </Menu>
        </Box>
      )}

      {/* Form Modal */}
      <DataCreationModal
        open={openModal}
        onClose={handleCloseModal}
        buildingBlockType={buildingBlockType}
        formGroups={formGroups}
        isEditMode={isEditMode}
        editingBlock={editingBlock}
        selectedSubType={selectedSubType}
        onSubmit={handleFormSubmit}
        onEditSubmit={handleEditFormSubmit}
        convertDatesToDayjs={convertDatesToDayjs}
      />

      {/* Import Modal */}
      <ImportModal
        open={openImportModal}
        onClose={handleCloseImportModal}
        buildingBlockType={buildingBlockType}
        onImportSuccess={handleImportSuccess}
      />

      {/* Data Details Drawer */}
      <DataDetailsDrawer
        open={openDrawer}
        onClose={handleCloseDrawer}
        rowData={selectedRowData}
        buildingBlockType={buildingBlockType}
        commonData={commonData}
        onEdit={handleEditBlock}
        onDelete={handleDeleteBlock}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteConfirmOpen}
        title="Confirm Delete"
        message={`Are you sure you want to delete "${deleteConfirmData?.row.name}"? This action cannot be undone.`}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />
    </Container>
  );
};

export default DataDetailsPage;

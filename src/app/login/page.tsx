"use client";

import { useState } from "react";
import { useAuth } from "@/context/AuthProvider";
import {
  Box,
  Button,
  TextField,
  Typography,
  InputAdornment,
  IconButton,
  Link,
  CircularProgress,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";

const LoginPage = () => {
  const { login } = useAuth();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false); // New state for loading

  const handleLogin = async () => {
    setLoading(true); // Start loading
    setError(""); // Clear any existing error messages
    try {
      await login(username, password);
    } catch (e) {
      setError("Lo<PERSON> failed. Please check your credentials. " + e);
    } finally {
      setLoading(false); // End loading after the login process is complete
    }
  };

  const handleClickShowPassword = () => setShowPassword(!showPassword);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleLogin(); // Trigger login on Enter key
    }
  };

  return (
    <Box
      sx={{
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundImage: `url('/path-to-background-image.jpg')`, // Add your background image here
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
      <Box
        display="flex"
        flexDirection="column"
        sx={{
          width: { xs: "90%", sm: 400 },
          background: "rgba(0, 0, 0, 0.6)",
          padding: 4,
          borderRadius: 4,
          boxShadow: "0px 10px 30px rgba(0, 0, 0, 0.5)",
        }}
        onKeyDown={handleKeyDown} // Handle Enter key press here
      >
        <Typography variant="h4" sx={{ fontWeight: "bold", marginBottom: 2 }}>
          Mul.apin
        </Typography>
        <Typography variant="body2" sx={{ marginBottom: 4, color: "#b0b0b0" }}>
          Welcome! Please sign in
        </Typography>

        {/* Error message */}
        {error && (
          <Typography color="error" sx={{ marginBottom: 2 }}>
            {error}
          </Typography>
        )}

        {/* Username Field */}
        <TextField
          label="Username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          fullWidth
          margin="normal"
          slotProps={{
            inputLabel: {
              shrink: true,
            },
          }}
          sx={{
            marginBottom: 2,
            "& input:-webkit-autofill": {
              WebkitBoxShadow: "0 0 0 100px rgba(0, 0, 0, 0.8) inset",
            },
            "& input:-moz-autofill": {
              backgroundColor: "transparent",
            },
            "& .MuiOutlinedInput-root": {
              "& fieldset": {
                borderColor: "divider",
              },
            },
          }}
        />

        {/* Password Field with visibility toggle */}
        <TextField
          label="Password"
          type={showPassword ? "text" : "password"}
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          fullWidth
          margin="normal"
          slotProps={{
            input: {
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleClickShowPassword}
                    edge="end"
                    sx={{ color: "#fff" }}
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            },
            inputLabel: {
              shrink: true,
            },
          }}
          sx={{
            marginBottom: 2,
            "& input:-webkit-autofill": {
              WebkitBoxShadow: "0 0 0 100px rgba(0, 0, 0, 0.8) inset",
            },
            "& input:-moz-autofill": {
              backgroundColor: "transparent",
            },
            "& .MuiOutlinedInput-root": {
              color: "#fff",
              "& fieldset": {
                borderColor: "#999",
              },
              "&:hover fieldset": {
                borderColor: "#fff",
              },
            },
          }}
        />

        {/* Sign In Button */}
        <Button
          onClick={handleLogin}
          variant="contained"
          size="medium"
          sx={{
            borderRadius: 2,
            mt: 2,
            mb: 3,
          }}
          disabled={loading} // Disable button when loading
        >
          {loading ? <CircularProgress size={24} color="inherit" /> : "Continue"}
        </Button>

        {/* Join Now Link */}
        <Typography
          variant="body2"
          sx={{ color: "#b0b0b0", textAlign: "center" }}
        >
          New to Mulapin?{" "}
          <Link href="#" underline="hover">
            Join Now
          </Link>
        </Typography>
      </Box>
    </Box>
  );
};

export default LoginPage;
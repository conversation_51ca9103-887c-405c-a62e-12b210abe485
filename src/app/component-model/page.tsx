'use client';

import React from 'react';
import { Typography, Container } from '@mui/material';
import AccordionWithTable from './AccordionWithTable'; // Local import within component-model

const ComponentModelPage = () => {
  return (
    <Container>
      <Typography variant="h4" mb={5}>
        Component Model
      </Typography>

      {/* Customer View Accordion */}
      <AccordionWithTable title="Customer View" frameworkId='1'/>

      {/* Business View Accordion */}
      <AccordionWithTable title="Business View" frameworkId='2' />
    </Container>
  );
};

export default ComponentModelPage;
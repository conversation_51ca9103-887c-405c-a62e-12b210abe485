"use client";

import React, { useState, useEffect } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Alert,
  CircularProgress,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import FileUpload from "../../components/FileUpload";
import DataTable from "../../components/Datatable";
import {
  parseComponentModelExcelFile,
  ComponentModelRow,
  parseComponentData,
} from "../../utils/componentHelper";
import componentService from "@/services/ComponentService";
import { ComponentData } from "@/types/components";

interface AccordionWithTableProps {
  title: string;
  frameworkId: string;
}

const COMPONENT_COLUMNS = ["ID", "Name", "Definition", "Examples"];

const AccordionWithTable: React.FC<AccordionWithTableProps> = ({
  title,
  frameworkId,
}) => {
  const [tableData, setTableData] = useState<ComponentModelRow[]>([]);
  const [columns, setColumns] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchComponents = async () => {
      try {
        setIsLoading(true);
        const response = await componentService.getComponents({
          framework: frameworkId === "1" ? "customer" : "business",
          query: "", 
        });
        if (response) {
          const parsedData = parseComponentData(response as ComponentData[]);
          setColumns(COMPONENT_COLUMNS);
          setTableData(parsedData);
          setErrorMessage(null);
        } else {
          setErrorMessage("No components found.");
        }
      } catch (error) {
        setErrorMessage("Failed to fetch components: " + error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchComponents();
  }, [frameworkId]);

  const handleFileSelect = async (file: File) => {
    try {
      const parsedData = await parseComponentModelExcelFile(file);
      if (parsedData.length > 0) {
        setTableData(parsedData);
        setErrorMessage(null);
      } else {
        setErrorMessage("No data found in the file.");
      }
    } catch (error) {
      setErrorMessage(error + "");
    }
  };

  const handleFileDelete = () => {
    setTableData([]);
    setColumns([]);
    setErrorMessage(null);
  };

  const handleUpload = async (file: File) => {
    return await componentService.uploadComponentFile(file, frameworkId);
  };

  return (
    <Accordion sx={{ mt: 3 }} defaultExpanded>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls={`${title.toLowerCase()}-view-content`}
        id={`${title.toLowerCase()}-view-header`}
      >
        <Typography variant="body1">{title}</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <FileUpload
          onUpload={handleUpload}
          onFileSelect={handleFileSelect}
          onFileDelete={handleFileDelete}
        />

        {errorMessage && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {errorMessage}
          </Alert>
        )}

        {isLoading ? (
          <CircularProgress sx={{ mt: 2 }} />
        ) : (
          tableData.length > 0 &&
          !errorMessage && <DataTable columns={columns} data={tableData} />
        )}
      </AccordionDetails>
    </Accordion>
  );
};

export default AccordionWithTable;

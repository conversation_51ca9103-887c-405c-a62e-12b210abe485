import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { useTheme } from "@mui/material/styles";
import { NodeMapData } from "@/types/components";
import {
  getAllDescendants,
  getVisibleNodesAndLinks,
  transformDataToNodesAndLinks,
} from "@/utils/nodeMapHelper";
import { ExpandableNode } from "@/utils/nodeMapHelper";

interface NodeMapProps {
  data: NodeMapData;
  view: "customer" | "business" | "all";
  level: number; // 0-3
}

const NodeMap: React.FC<NodeMapProps> = ({ data, view, level }) => {
  const theme = useTheme();
  const svgRef = useRef<SVGSVGElement | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const nodesRef = useRef<ExpandableNode[]>([]);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  const toggleNode = (nodeId: string) => {
    setExpandedNodes((prev) => {
      const next = new Set(prev);
      const node = nodesRef.current.find((n) => n.id === nodeId);

      if (!next.has(nodeId)) {
        // Node not expanded - expand it and its immediate children
        next.add(nodeId);
        if (node?.children) {
          node.children.forEach((childId) => next.add(childId));
        }
      } else if (node?.children) {
        // Node is expanded - check if all children are expanded
        const allChildrenExpanded = node.children.every((childId) =>
          next.has(childId)
        );

        if (allChildrenExpanded) {
          // All children expanded - collapse this node and its descendants
          next.delete(nodeId);
          const descendants = getAllDescendants(nodeId, nodesRef.current);
          descendants.forEach((descendantId) => next.delete(descendantId));
        } else {
          // Some children not expanded - expand them
          node.children.forEach((childId) => {
            if (!next.has(childId)) {
              next.add(childId);
            }
          });
        }
      }
      return next;
    });
  };

  const getLinkColor = (link: any, visibleNodes: ExpandableNode[]) => {
    const sourceNode = visibleNodes.find(
      (n) => n.id === (link.source.id || link.source)
    );
    const targetNode = visibleNodes.find(
      (n) => n.id === (link.target.id || link.target)
    );
    if (
      sourceNode?.framework === "map" &&
      (targetNode?.framework === "customer" ||
        targetNode?.framework === "business")
    ) {
      return theme.palette.nodeMap.mapComponentLink;
    }
    return "#999"; // Default color for other links
  };

  useEffect(() => {
    if (!data) return;
    const { nodes: newNodes } = transformDataToNodesAndLinks(data, view);
    nodesRef.current = newNodes;
  }, [data, view]);

  useEffect(() => {
    if (!data || !containerRef.current) return;

    const width = containerRef.current.clientWidth;
    const height = containerRef.current.clientHeight;

    const { nodes: newNodes, links: newLinks } = transformDataToNodesAndLinks(
      data,
      view
    );
    const { visibleNodes, visibleLinks } = getVisibleNodesAndLinks(
      newNodes,
      newLinks,
      expandedNodes
    );

    // Select SVG element and clear previous content
    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const simulation = d3
      .forceSimulation(visibleNodes)
      .force(
        "link",
        d3
          .forceLink(visibleLinks)
          .id((d: any) => d.id)
          .distance(30) // Increased from 15px to 30px
          .strength(2)
      )
      .force(
        "charge",
        d3
          .forceManyBody()
          .strength(-100) // Increased repulsion
          .distanceMax(60)
      )
      .force("collision", d3.forceCollide().radius(15).strength(1))
      .force(
        "x",
        d3
          .forceX((d: ExpandableNode) => {
            if (d.framework === "map") {
              return width * 0.5; // Center map nodes
            }
            const baseX =
              view === "all"
                ? d.framework === "customer"
                  ? width * 0.75
                  : width * 0.25
                : width * 0.5;

            const levelNodes = visibleNodes.filter((n) => n.level === d.level);
            const nodeIndex = levelNodes.findIndex((n) => n.id === d.id);
            const angleStep = (2 * Math.PI) / Math.max(1, levelNodes.length);
            const angle = nodeIndex * angleStep + Math.PI / 2;

            const levelRadius = d.level * 60;
            return baseX + levelRadius * Math.cos(angle);
          })
          .strength(1)
      )
      .force(
        "y",
        d3
          .forceY((d: ExpandableNode) => {
            const baseY = height / 2;

            if (d.framework === "map") {
              if (d.level === 0) {
                // Get all map root nodes and find current node's index
                const mapRoots = visibleNodes.filter(
                  (n) => n.framework === "map" && n.level === 0
                );
                const index = mapRoots.findIndex((n) => n.id === d.id);
                const totalRoots = mapRoots.length;

                // Calculate vertical position with spacing
                const spacing = 150; // Increased spacing between root nodes
                return baseY + (index - (totalRoots - 1) / 2) * spacing;
              }

              // For non-root map nodes, keep circular layout
              const levelNodes = visibleNodes.filter(
                (n) => n.level === d.level
              );
              const nodeIndex = levelNodes.findIndex((n) => n.id === d.id);
              const angleStep = (2 * Math.PI) / Math.max(1, levelNodes.length);
              const angle = nodeIndex * angleStep + Math.PI / 2;
              const levelRadius = d.level * 60;
              return baseY + levelRadius * Math.sin(angle);
            }

            // Existing logic for customer/business nodes
            const levelNodes = visibleNodes.filter((n) => n.level === d.level);
            const nodeIndex = levelNodes.findIndex((n) => n.id === d.id);
            const angleStep = (2 * Math.PI) / Math.max(1, levelNodes.length);
            const angle = nodeIndex * angleStep + Math.PI / 2;
            const levelRadius = d.level * 60;
            return baseY + levelRadius * Math.sin(angle) * 1.5;
          })
          .strength(1)
      )
      .velocityDecay(0.7)
      .alphaDecay(0.05);

    // Create tooltip
    const tooltip = d3
      .select(containerRef.current)
      .append("div")
      .attr("class", "tooltip")
      .style("position", "absolute")
      .style("background", "rgba(0, 0, 0, 0.8)")
      .style("color", "white")
      .style("padding", "8px")
      .style("border-radius", "4px")
      .style("font-size", "12px")
      .style("pointer-events", "none")
      .style("opacity", 0)
      .style("max-width", "300px")
      .style("z-index", "1000");

    // Update link creation section
    const link = svg
      .append("g")
      .attr("class", "links")
      .selectAll("line")
      .data(visibleLinks)
      .enter()
      .append("line")
      .attr("stroke-width", 2)
      .attr("stroke", (d) => getLinkColor(d, visibleNodes))
      .attr("stroke-opacity", 0.6);

    // Move getNodeColor definition inside useEffect
    const getNodeColor = (
      d: ExpandableNode,
      visibleLinks: any[],
      visibleNodes: ExpandableNode[]
    ) => {
      const isComponentNode = visibleLinks.some((link) => {
        const sourceNode = visibleNodes.find(
          (n) => n.id === (link.source.id || link.source)
        );
        const targetNode = visibleNodes.find(
          (n) => n.id === (link.target.id || link.target)
        );
        return (
          sourceNode?.framework === "map" &&
          targetNode?.id === d.id &&
          (d.framework === "customer" || d.framework === "business")
        );
      });

      if (isComponentNode) {
        return theme.palette.nodeMap.componentNode;
      }

      if (d.framework === "map") {
        switch (d.level) {
          case 0:
            return theme.palette.nodeMap.map;
          case 1:
            return theme.palette.nodeMap.phase;
          case 2:
            return theme.palette.nodeMap.buildingBlock;
          default:
            return theme.palette.nodeMap.map;
        }
      }

      if (d.numbering === "root") return theme.palette.nodeMap.root;
      switch (d.level) {
        case 1:
          return theme.palette.nodeMap.level1;
        case 2:
          return theme.palette.nodeMap.level2;
        case 3:
          return theme.palette.nodeMap.level3;
        default:
          return theme.palette.nodeMap.level1;
      }
    };

    // Update node creation to pass visibleLinks and visibleNodes
    const node = svg
      .append("g")
      .attr("class", "nodes")
      .selectAll("circle")
      .data(visibleNodes)
      .enter()
      .append("circle")
      .attr("r", 6)
      .attr("fill", (d) => getNodeColor(d, visibleLinks, visibleNodes))
      .attr("stroke", theme.palette.background.default)
      .attr("stroke-width", "2px")
      .style("cursor", (d) =>
        d.children && d.children.length ? "pointer" : "default"
      )
      .call(
        d3
          .drag<any, ExpandableNode>()
          .on("start", (event, d) => {
            if (!event.active) simulation.alphaTarget(0.01).restart();
            d.fx = event.x;
            d.fy = event.y;
          })
          .on("drag", (event, d) => {
            // Free movement - just update fixed position
            d.fx = event.x;
            d.fy = event.y;
          })
          .on("end", (event, d) => {
            // Keep the node where it was dragged
            d.x = event.x;
            d.y = event.y;
            d.fx = event.x;
            d.fy = event.y;
            if (!event.active) simulation.alphaTarget(0);
          })
      )
      .on("click", (event, d: ExpandableNode) => {
        if (d.children && d.children.length) {
          event.stopPropagation();
          toggleNode(d.id);
        }
      })
      .on("mouseover", (event, d: ExpandableNode) => {
        tooltip.transition().duration(200).style("opacity", 0.9);
        tooltip
          .html(
            `
          <strong>${d.numbering}</strong><br/>
          <strong>${d.name}</strong><br/>
          <small>${d.description || ""}</small>
        `
          )
          .style("left", event.pageX - 200 + "px")
          .style("top", event.pageY - 100 + "px");
      })
      .on("mouseout", () => {
        tooltip.transition().duration(500).style("opacity", 0);
      });

    simulation.on("tick", () => {
      link
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y);

      node.attr("cx", (d: any) => d.x).attr("cy", (d: any) => d.y);
    });

    return () => {
      simulation.stop();
      tooltip.remove();
    };
  }, [data, expandedNodes, view]);

  useEffect(() => {
    const { nodes } = transformDataToNodesAndLinks(data, view);
    nodesRef.current = nodes;

    setExpandedNodes((prev) => {
      const next = new Set(prev);

      // Keep only existing map nodes
      const currentMapNodes = Array.from(prev).filter((id) =>
        id.startsWith("map-")
      );

      // Clear previous nodes and add back map nodes
      next.clear();
      currentMapNodes.forEach((id) => next.add(id));

      // Add component nodes up to specified level
      nodes
        .filter((node) => node.framework !== "map" && node.level <= level)
        .forEach((node) => next.add(node.id));

      return next;
    });
  }, [data, level, view]);

  return (
    <div ref={containerRef} style={{ width: "100%", height: "100%" }}>
      <svg ref={svgRef} style={{ width: "100%", height: "100%" }} />
    </div>
  );
};

export default NodeMap;

import React, { useRef, useEffect, useState } from 'react';
import * as d3 from 'd3';

interface DataNode {
  id: string;
  count: number;
  level: number;
  parent: string | null;
}

interface SphericalHeatmapProps {
  data: DataNode[];
}

const SphericalHeatmap: React.FC<SphericalHeatmapProps> = ({ data }) => {
  const svgRef = useRef<SVGSVGElement | null>(null);
  const [rotation, setRotation] = useState<[number, number, number]>([0, -30, 0]);
  const [zoomLevel, setZoomLevel] = useState<number>(1); 

  useEffect(() => {
    const sphere = { type: "Sphere" };

    // Create SVG container dimensions based on window size
    const container = svgRef.current?.parentElement;
    const width = container?.clientWidth || 800; // Default width if no container
    const height = container?.clientHeight || 600; // Default height if no container

    // Define color scale for heatmap values
    const colorScale = d3.scaleSequential(d3.interpolateYlGnBu).domain([0, d3.max(data, d => d.count) || 1]);

    // Create the projection
    const projection = d3.geoOrthographic()
      .scale(250 * zoomLevel)
      .translate([width / 2, height / 2])
      .rotate(rotation)
      .precision(0.1);

    const path = d3.geoPath(projection);

    const svg = d3.select(svgRef.current)
      .attr("width", "100%")
      .attr("height", "100%")
      .attr("viewBox", `0 0 ${width} ${height}`) // Responsive SVG
      .style("cursor", "move");

    svg.selectAll("*").remove();

    // Render the sphere
    svg.append("path")
      .datum(sphere)
      .attr("d", path)
      .attr("fill", "none")
      .attr("stroke", "#444");

    // Grid overlay
    const graticule = d3.geoGraticule().step([10, 10]);

    svg.append("path")
      .datum(graticule())
      .attr("d", path)
      .attr("fill", "none")
      .attr("stroke", "#777");

    const tooltip = d3.select("body")
      .append("div")
      .attr("class", "tooltip")
      .style("position", "absolute")
      .style("background", "#fff")
      .style("border", "1px solid #ccc")
      .style("padding", "5px")
      .style("border-radius", "4px")
      .style("display", "none");

    // Generate grid cells
    const numRows = Math.ceil(Math.sqrt(data.length));
    const numCols = Math.ceil(data.length / numRows);

    const latStep = 180 / numRows;
    const lonStep = 360 / numCols;

    data.forEach((d, i) => {
      const row = Math.floor(i / numCols);
      const col = i % numCols;
      const fillColor = colorScale(d.count);

      const polygon: GeoJSON.Feature = {
        type: "Feature",
        geometry: {
          type: "Polygon",
          coordinates: [[
            [col * lonStep - 180, 90 - row * latStep],
            [(col + 1) * lonStep - 180, 90 - row * latStep],
            [(col + 1) * lonStep - 180, 90 - (row + 1) * latStep],
            [col * lonStep - 180, 90 - (row + 1) * latStep],
            [col * lonStep - 180, 90 - row * latStep]
          ]]
        },
        properties: {}
      };

      svg.append("path")
        .datum(polygon)
        .attr("d", path)
        .attr("fill", fillColor)
        .attr("stroke", "#222")
        .on("mouseover", function (event) {
          tooltip
            .style("display", "block")
            .html(`ID: ${d.id}<br>Count: ${d.count}`)
            .style("left", `${event.pageX + 10}px`)
            .style("top", `${event.pageY + 10}px`)
            .style("background", "#000");
        })
        .on("mousemove", function (event) {
          tooltip
            .style("left", `${event.pageX + 10}px`)
            .style("top", `${event.pageY + 10}px`);
        })
        .on("mouseout", function () {
          tooltip.style("display", "none");
        });
    });

    // Zoom and drag behavior for smooth rotation
    const zoom = d3.zoom()
      .scaleExtent([1, 2])
      .on("zoom", ({ transform }) => {
        const newRotation: [number, number, number] = [
          rotation[0] + transform.x / 5,
          rotation[1] - transform.y / 5,
          rotation[2],
        ];
        projection.rotate(newRotation);
        setRotation(newRotation); 
        svg.selectAll("path").attr("d", path); 
      });

    svg.call(zoom);

    // Handle zoom via mouse wheel
    svg.on("wheel.zoom", function (event: any) {
      event.preventDefault();
      if (event.deltaY < 0) {
        setZoomLevel((prev) => Math.min(prev + 0.1, 2));
      } else {
        setZoomLevel((prev) => Math.max(prev - 0.1, 1));
      }
    });

    // Keyboard controls for zooming in/out
    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === "+" || event.key === "=") {
        setZoomLevel((prev) => Math.min(prev + 0.1, 2));
      } else if (event.key === "-") {
        setZoomLevel((prev) => Math.max(prev - 0.1, 1));
      }
    };

    window.addEventListener("keydown", handleKeydown);

    return () => {
      svg.on(".zoom", null);
      tooltip.remove();
      window.removeEventListener("keydown", handleKeydown);
    };
  }, [data, rotation, zoomLevel]);

  return <svg ref={svgRef} />;
};

export default SphericalHeatmap;
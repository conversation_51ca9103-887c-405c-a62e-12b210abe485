"use client";

import React, { useState } from "react";
import {
  Avatar,
  Box,
  Typography,
  IconButton,
  Menu,
  MenuItem,
} from "@mui/material";

interface HeaderProps {
  username: string | null; // Username passed as a prop from RootLayout
  logout: () => void; // Logout function passed as a prop
}

const Header: React.FC<HeaderProps> = ({ username, logout }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout(); // Trigger the logout passed from RootLayout
    handleClose();
  };

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        px: "2rem",
        py: "1rem",
        borderBottom: "1px solid",
        borderColor: "divider",
        backgroundColor: "backgroundPrimary",
        color: "textPrimary",
      }}
    >
      {/* App Title */}
      <Typography variant="h6" fontWeight={500}>
        Mulapin
      </Typography>

      {/* User Avatar */}
      <Box>
        <IconButton onClick={handleClick} sx={{ p: 0 }}>
          <Avatar sx={{ bgcolor: "#4A90E2" }}>
            {username
              ? username
                  .split(" ")
                  .map((n) => n[0].toUpperCase())
                  .join("") 
              : "U"}
          </Avatar>
        </IconButton>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
        >
          {/* Menu with username and logout option */}
          <MenuItem disabled>
            <Typography>Signed in as {username || "User"}</Typography>
          </MenuItem>
          <MenuItem onClick={handleLogout}>Sign out</MenuItem>
        </Menu>
      </Box>
    </Box>
  );
};

export default Header;

'use client';

import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Box, Link, Typography, LinearProgress, IconButton } from '@mui/material';
import UploadIcon from '@mui/icons-material/UploadFile';
import DeleteIcon from '@mui/icons-material/Delete';

// Define the upload status states
enum UploadStatus {
  UPLOADING = 'uploading',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

interface FileUploadProps {
  onUpload: (file: File) => Promise<any>;
  onFileSelect?: (file: File) => void;
  onFileDelete?: () => void;
}

interface UploadedFile {
  file: File;
  name: string;
  size: number;
  status: UploadStatus;
  progress: number;
  error?: string;
}

export default function FileUpload({
  onUpload,
  onFileSelect,
  onFileDelete,
}: FileUploadProps) {
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null); // Only one file

  // Handle file upload logic
  const onDrop = async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]; // Only take the first file
    const newFile: UploadedFile = {
      file,
      name: file.name,
      size: file.size,
      status: UploadStatus.UPLOADING,
      progress: 0,
      error: '',
    };

    setUploadedFile(newFile);

    await uploadFileToServer(newFile);
  };

  // Simulate a local upload (for demo purposes)
  // const simulateLocalUpload = (file: UploadedFile) => {
  //   const uploadInterval = setInterval(() => {
  //     setUploadedFile((prevFile) =>
  //       prevFile && prevFile.name === file.name
  //         ? {
  //             ...prevFile,
  //             progress: prevFile.progress + 10,
  //             status:
  //               prevFile.progress >= 100
  //                 ? UploadStatus.COMPLETED
  //                 : prevFile.status,
  //           }
  //         : prevFile
  //     );

  //     if (file.progress >= 100) {
  //       clearInterval(uploadInterval);
  //     }
  //   }, 10);
  // };

  // Upload file to server (if upload URL is provided)
  const uploadFileToServer = async (file: UploadedFile) => {
    try {
      await onUpload(file.file);
      
      setUploadedFile(prev => 
        prev && prev.name === file.name
          ? { ...prev, status: UploadStatus.COMPLETED, progress: 100 }
          : prev
      );

      // Call onFileSelect with the uploaded file
      if (onFileSelect) {
        onFileSelect(file.file);
      }
    } catch (error) {
      setUploadedFile(prev =>
        prev && prev.name === file.name
          ? {
              ...prev,
              status: UploadStatus.FAILED,
              error: error.message || 'Upload failed',
              progress: 100,
            }
          : prev
      );
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'text/csv': ['.csv'], 'application/vnd.ms-excel': ['.xlsx'] },
    maxFiles: 1, // Only one file can be uploaded
    maxSize: 3145728, // 3MB
  });

  const handleDelete = () => {
    setUploadedFile(null); // Remove the file and show the dropzone again
    if (onFileDelete) {
      onFileDelete(); // Call the onFileDelete callback
    }
  };

  return (
    <Box>
      {/* File Upload Dropzone - Hidden when a file is being uploaded */}
      {!uploadedFile || uploadedFile.status === UploadStatus.FAILED ? (
        <Box
          {...getRootProps()}
          sx={{
            border: '1px dashed',
            borderRadius: '5px',
            borderColor: 'divider',
            padding: '20px',
            textAlign: 'center',
            cursor: 'pointer',
            mt: 2,
          }}
        >
          <input {...getInputProps()} />
          <UploadIcon color="primary" />
          {isDragActive ? (
            <Typography variant="body1">Drop the files here...</Typography>
          ) : (
            <Typography variant="body1" color="textPrimary">
              <Link>Click to upload</Link> or drag and drop
            </Typography>
          )}
          <Typography variant="caption" color="textSecondary">
            .csv or .xlsx (max: 3MB)
          </Typography>
        </Box>
      ) : null}

      {/* Uploaded File Status - Shows if a file is uploaded */}
      {uploadedFile && (
        <Box sx={{ mt: 2 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            {/* File Icon and File Info */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <UploadIcon sx={{ color: 'primary.main', mx: 2 }} />{' '}
              {/* File icon */}
              <Box>
                <Typography variant="body1" color="textPrimary">
                  {uploadedFile.name}
                </Typography>
                <Typography variant="caption" color="textSecondary">
                  {Math.round(uploadedFile.size / 1024)}kb{' '}
                  {uploadedFile.status === UploadStatus.UPLOADING && ' • Loading'}
                  {uploadedFile.status === UploadStatus.COMPLETED && ' • Complete'}
                  {uploadedFile.status === UploadStatus.FAILED && (
                    <span style={{ color: 'red' }}>
                      {' '}
                      • {uploadedFile.error || 'Failed'}
                    </span>
                  )}
                </Typography>
              </Box>
            </Box>
            {/* Delete Button */}
            <IconButton onClick={handleDelete} sx={{ color: 'action' }}>
              <DeleteIcon />
            </IconButton>
          </Box>

          {/* Progress Bar for uploading and failed */}
          {uploadedFile.status === UploadStatus.UPLOADING && (
            <LinearProgress
              variant="determinate"
              value={uploadedFile.progress}
              sx={{ mt: 1 }}
            />
          )}
          {uploadedFile.status === UploadStatus.FAILED && (
            <Typography variant="caption" sx={{ color: 'red', mt: 1 }}>
              File too large
            </Typography>
          )}
        </Box>
      )}
    </Box>
  );
}
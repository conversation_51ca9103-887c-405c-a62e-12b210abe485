"use client";

import {
  Analytics,
  Groups,
  Insights,
  Language,
  LinearScale,
  MenuRounded,
  ScatterPlot,
  TrackChanges,
} from "@mui/icons-material";
import {
  Box,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Toolbar,
} from "@mui/material";
import { usePathname, useRouter } from "next/navigation";
import React, { useEffect } from "react";

const drawerWidth = 240;

interface SidebarProps {
  toggleTheme: () => void;
  mode: "light" | "dark";
}

const Sidebar: React.FC<SidebarProps> = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = React.useState(false);
  const currentWidth = isCollapsed ? 65 : drawerWidth;

  const handleToggle = () => {
    setIsCollapsed(!isCollapsed);
  };

  useEffect(() => {
    setIsCollapsed(pathname.startsWith("/journeys/map/"));
  }, [pathname]);

  useEffect(() => {
    document.documentElement.style.setProperty(
      "--sidebar-width",
      `${isCollapsed ? "65px" : "240px"}`
    );
  }, [isCollapsed]);

  const menuItems = [
    { text: "Explore", path: "/", icon: <Insights /> },
    { text: "Journeys", path: "/journeys", icon: <LinearScale /> },
    { text: "Component Model", path: "/component-model", icon: <ScatterPlot /> },
    { text: "Data", path: "/data", icon: <Analytics /> },
    { text: "Persona", path: "/persona", icon: <Groups /> },
    { text: "Roadmap", path: "/roadmap", icon: <TrackChanges /> },
    { text: "Community", path: "/community", icon: <Language /> },
  ];

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: currentWidth,
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          width: currentWidth,
          boxSizing: "border-box",
          backgroundColor: "#2D2D2D",
          color: "#FFF",
          transition: "width 0.2s",
          overflowX: "hidden",
        },
      }}
    >
      <Toolbar sx={{ minHeight: "73px", px: "10px !important" }}>
        <IconButton onClick={handleToggle}>
          <MenuRounded />
        </IconButton>
      </Toolbar>

      <Box sx={{ flexGrow: 1 }}>
        <List>
          {menuItems.map((item) => (
            <ListItem
              key={item.text}
              onClick={() => router.push(item.path)}
              sx={{
                color: pathname === item.path ? "#fff" : "#b0b0b0",
                backgroundColor: pathname === item.path ? "#444" : "inherit",
                "&:hover": {
                  backgroundColor: "#555",
                  color: "#fff",
                  cursor: "pointer",
                },
              }}
            >
              <ListItemIcon sx={{ minWidth: isCollapsed ? 0 : 40, color: "inherit" }}>
                {item.icon}
              </ListItemIcon>
              {!isCollapsed && <ListItemText primary={item.text} />}
            </ListItem>
          ))}
        </List>
      </Box>
    </Drawer>
  );
};

export default Sidebar;

import { Box, ToggleButton, ToggleButtonGroup, Typography } from "@mui/material";
import React from "react";

interface DynamicButtonGroupProps {
  label?: string;
  states: string[]; // List of states to display as buttons
  stateValues?: string[]; // Optional values corresponding to states
  value?: string | null;
  onChange?: (value: string | null) => void;
}

const DynamicButtonGroup: React.FC<DynamicButtonGroupProps> = ({
  label,
  states,
  stateValues,
  value,
  onChange,
}) => {
  // If stateValues is provided, use it for the internal value
  const valueToLabelMap = React.useMemo(() => {
    if (!stateValues) return {};
    return states.reduce((acc, label, index) => {
      acc[stateValues[index]] = label;
      return acc;
    }, {} as Record<string, string>);
  }, [states, stateValues]);

  const labelToValueMap = React.useMemo(() => {
    if (!stateValues) return {};
    return states.reduce((acc, label, index) => {
      acc[label] = stateValues[index];
      return acc;
    }, {} as Record<string, string>);
  }, [states, stateValues]);

  // Find the display value (label) for the current value
  const displayValue = stateValues ? valueToLabelMap[value || ""] : value;

  const [selectedState, setSelectedState] = React.useState<string | null>(
    displayValue || states[0] || null
  );

  React.useEffect(() => {
    if (value !== undefined) {
      const newDisplayValue = stateValues ? valueToLabelMap[value] : value;
      if (newDisplayValue !== selectedState) {
        setSelectedState(newDisplayValue);
      }
    }
  }, [value, selectedState, valueToLabelMap, stateValues]);

  const handleStateChange = (event: React.MouseEvent<HTMLElement>, newState: string | null) => {
    if (newState !== null) {
      setSelectedState(newState);
      // Convert label to value if stateValues is provided
      const actualValue = stateValues ? labelToValueMap[newState] : newState;
      onChange?.(actualValue);
    }
  };

  return (
    <Box>
      {label && (
        <Typography variant="subtitle1" gutterBottom>
          {label}
        </Typography>
      )}
      <ToggleButtonGroup
        value={selectedState}
        exclusive
        onChange={handleStateChange}
        aria-label={label}
      >
        {states.map((state) => (
          <ToggleButton key={state} value={state} aria-label={state} sx={{ px: 3 }}>
            {state}
          </ToggleButton>
        ))}
      </ToggleButtonGroup>
    </Box>
  );
};

export default DynamicButtonGroup;

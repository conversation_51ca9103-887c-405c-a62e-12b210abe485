import React from "react";
import {
  Box,
  IconButton,
  Typography,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider,
  Select,
  MenuItem,
  Stack,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

interface SettingsPanelProps {
  isOpen: boolean;                     
  toggleDrawer: (open: boolean) => void;
  mapType: "spherical" | "node"; 
  setMapType: (type: "spherical" | "node") => void;
  viewType: "all" | "customer" | "business";
  setViewType: (type: "all" | "customer" | "business") => void;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({ 
  isOpen, 
  toggleDrawer, 
  mapType, 
  setMapType,
  viewType,
  setViewType
}) => {
  if (!isOpen) return null; // Do not render the panel if it's not open

  return (
    <Box
      sx={{
        position: "absolute", // Absolute positioning to make it float
        bottom: "0",
        width: 300,
        backgroundColor: "#222", // Optional: background color to match the theme
        boxShadow: 4,
        borderRadius: 2,
        p: 2,
        zIndex: 1300, // High z-index to ensure it floats above other content
      }}
    >
      {/* Close button */}
      <Stack direction="row" justifyContent="flex-end">
        <IconButton onClick={() => toggleDrawer(false)}>
          <CloseIcon />
        </IconButton>
      </Stack>

      {/* Map options */}
      <Typography variant="h6" gutterBottom>Map</Typography>
      <FormControl component="fieldset">
        <RadioGroup value={mapType} onChange={(e) => setMapType(e.target.value as "spherical" | "node")}>
          <FormControlLabel value="spherical" control={<Radio />} label="Spherical map" />
          <FormControlLabel value="node" control={<Radio />} label="Node map" />
        </RadioGroup>
      </FormControl>

      <Divider sx={{ my: 2 }} />

      {/* View options */}
      <Typography variant="h6" gutterBottom>View</Typography>
      <FormControl component="fieldset">
        <RadioGroup 
          value={viewType} 
          onChange={(e) => setViewType(e.target.value as "all" | "customer" | "business")}
        >
          <FormControlLabel value="all" control={<Radio />} label="All" />
          <FormControlLabel value="customer" control={<Radio />} label="Customer" />
          <FormControlLabel value="business" control={<Radio />} label="Business" />
        </RadioGroup>
      </FormControl>

      <Divider sx={{ my: 2 }} />

      {/* States options */}
      <Typography variant="h6" gutterBottom>States</Typography>
      <FormControl component="fieldset">
        <RadioGroup defaultValue="future">
          <FormControlLabel value="future" control={<Radio />} label="Future" />
          <FormControlLabel value="current" control={<Radio />} label="Current" />
          <FormControlLabel value="past" control={<Radio />} label="Past" />
        </RadioGroup>
      </FormControl>

      <Divider sx={{ my: 2 }} />

      {/* Segment dropdown */}
      <Typography variant="h6" gutterBottom>Segment</Typography>
      <FormControl fullWidth>
        <Select defaultValue="all">
          <MenuItem value="all">All</MenuItem>
          <MenuItem value="segment1">Segment 1</MenuItem>
          <MenuItem value="segment2">Segment 2</MenuItem>
        </Select>
      </FormControl>
    </Box>
  );
};

export default SettingsPanel;
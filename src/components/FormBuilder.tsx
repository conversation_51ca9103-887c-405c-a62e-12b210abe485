import { zodResolver } from "@hookform/resolvers/zod";
import {
  Box,
  Button,
  Divider,
  FormControl,
  FormHelperText,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers";
import React, { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import * as z from "zod";
import DynamicButtonGroup from "./DynamicButtonGroup";
import AITextarea from "./inputs/AITextArea";

interface FormField {
  name: string;
  type: "text" | "number" | "date" | "select" | "textarea" | "buttonGroup" | "aiTextarea";
  label: string;
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
  options?: { label: string; value: any }[];
  states?: string[]; // For buttonGroup type
  stateValues?: string[]; // Values corresponding to states
  description?: string;
  colSpan?: number; // Number of columns this field should span
  rowSpan?: number; // Number of rows this field should span
  renderValue?: (value: any) => string;
  aiHandler?: () => Promise<string>;
  placeholder?: string;
}

interface FormGroup {
  name?: string;
  fields: FormField[];
  description?: string;
}

const getChangedFields = (initialValues: any, currentValues: any) => {
  const changes: Record<string, any> = {};

  Object.keys(currentValues).forEach((key) => {
    if (Array.isArray(initialValues[key]) || Array.isArray(currentValues[key])) {
      const initialArray = initialValues[key] || [];
      const currentArray =
        typeof currentValues[key] === "string"
          ? currentValues[key].split("\n").filter(Boolean)
          : currentValues[key] || [];

      if (JSON.stringify(initialArray) !== JSON.stringify(currentArray)) {
        changes[key] = currentArray;
      }
    }
    // Handle regular fields
    else if (initialValues[key] !== currentValues[key]) {
      changes[key] = currentValues[key];
    }
  });

  return changes;
};

interface FormBuilderProps {
  groups: FormGroup[];
  initialValues: Record<string, any>;
  onSubmit: (values: any, changedValues?: Record<string, any>) => void; // Updated to include changedValues
  onCancel?: () => void;
  submitButtonText?: string;
  onChange?: (values: any) => void;
  trackChanges?: boolean;
}

const FormBuilder: React.FC<FormBuilderProps> = ({
  groups,
  initialValues,
  onSubmit,
  onCancel,
  submitButtonText = "Save",
  onChange,
  trackChanges = false,
}) => {
  const [originalValues] = React.useState(initialValues);
  const [hasChanges, setHasChanges] = React.useState(false);
  const [changedFields, setChangedFields] = React.useState<Record<string, any>>({});

  // Generate zod validation schema
  const generateValidationSchema = () => {
    const schema: Record<string, any> = {};

    groups.forEach((group) => {
      group.fields.forEach((field) => {
        let fieldSchema: any;

        switch (field.type) {
          case "text":
            fieldSchema = z.string();
            if (field.validation?.minLength) {
              fieldSchema = fieldSchema.min(
                field.validation.minLength,
                `Must be at least ${field.validation.minLength} characters`
              );
            }
            if (field.validation?.maxLength) {
              fieldSchema = fieldSchema.max(
                field.validation.maxLength,
                `Must be at most ${field.validation.maxLength} characters`
              );
            }
            break;

          case "number":
            fieldSchema = z.number();
            if (field.validation?.min !== undefined) {
              fieldSchema = fieldSchema.min(
                field.validation.min,
                `Must be at least ${field.validation.min}`
              );
            }
            if (field.validation?.max !== undefined) {
              fieldSchema = fieldSchema.max(
                field.validation.max,
                `Must be at most ${field.validation.max}`
              );
            }
            break;

          case "date":
            // Accept both Date objects and dayjs objects
            fieldSchema = z.any().refine(
              (value) => {
                // Accept null or undefined for optional fields
                if (value === null || value === undefined) return true;

                // Check if it's a Date object
                if (value instanceof Date) return true;

                // Check if it's a dayjs object (has format method)
                if (value && typeof value === "object" && typeof value.format === "function")
                  return true;

                // Check if it's a dayjs object with $d property
                if (value && typeof value === "object" && value.$d instanceof Date) return true;

                return false;
              },
              { message: "Invalid date format" }
            );
            break;

          case "select":
            fieldSchema = z.any();
            break;

          case "buttonGroup":
            fieldSchema = z.any();
            break;

          default:
            fieldSchema = z.any();
        }

        if (field.validation?.required) {
          schema[field.name] = fieldSchema;
        } else {
          schema[field.name] = fieldSchema.optional();
        }
      });
    });

    return z.object(schema);
  };

  const validationSchema = React.useMemo(() => generateValidationSchema(), []);

  const form = useForm({
    resolver: zodResolver(validationSchema),
    defaultValues: initialValues,
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
    watch,
  } = form;

  // Separate effect for onChange callback
  useEffect(() => {
    const subscription = watch((value) => {
      // Always call onChange if provided
      if (onChange) {
        onChange(value);
      }

      // Handle change tracking if enabled
      if (trackChanges) {
        const changes = getChangedFields(originalValues, value);
        setChangedFields(changes);
        setHasChanges(Object.keys(changes).length > 0);
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, originalValues, trackChanges, onChange]);

  const handleFormSubmit = handleSubmit((values) => {
    onSubmit(values, trackChanges ? changedFields : undefined);
  });

  const renderField = (field: FormField) => {
    switch (field.type) {
      case "buttonGroup":
        return (
          <Controller
            name={field.name}
            control={control}
            render={({ field: { onChange, value } }) => (
              <DynamicButtonGroup
                states={field.states || []}
                stateValues={field.stateValues}
                value={value}
                onChange={onChange}
              />
            )}
          />
        );

      case "select":
        return (
          <Controller
            name={field.name}
            control={control}
            render={({ field: { onChange, value, onBlur } }) => (
              <FormControl fullWidth error={!!errors[field.name]}>
                <Select
                  value={value || ""}
                  onChange={onChange}
                  onBlur={onBlur}
                  renderValue={field.renderValue ? field.renderValue : undefined}
                >
                  {field.options?.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
                {errors[field.name] && (
                  <FormHelperText>{errors[field.name]?.message as string}</FormHelperText>
                )}
              </FormControl>
            )}
          />
        );

      case "textarea":
        return (
          <Controller
            name={field.name}
            control={control}
            render={({ field: { onChange, value, onBlur, ref } }) => (
              <TextField
                fullWidth
                multiline
                rows={field.rowSpan || 4}
                value={field.renderValue ? field.renderValue(value) : value || ""}
                onChange={onChange}
                onBlur={onBlur}
                inputRef={ref}
                error={!!errors[field.name]}
                helperText={errors[field.name]?.message as string}
              />
            )}
          />
        );

      case "date":
        return (
          <Controller
            name={field.name}
            control={control}
            render={({ field: { onChange, value } }) => (
              <DatePicker
                value={value}
                onChange={(newValue) => {
                  // Just pass the dayjs object directly to onChange
                  // The form validation will handle it, and processFormValues will convert it later
                  onChange(newValue);
                }}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors[field.name],
                    helperText: errors[field.name]?.message as string,
                  },
                }}
              />
            )}
          />
        );

      case "number":
        return (
          <Controller
            name={field.name}
            control={control}
            render={({ field: { onChange, value, onBlur, ref } }) => (
              <TextField
                fullWidth
                type="number"
                value={value || ""}
                onChange={(e) => onChange(Number(e.target.value))}
                onBlur={onBlur}
                inputRef={ref}
                error={!!errors[field.name]}
                helperText={errors[field.name]?.message as string}
              />
            )}
          />
        );

      case "aiTextarea":
        return (
          <Box>
            <Controller
              name={field.name}
              control={control}
              render={({ field: { onChange, value, onBlur, ref } }) => (
                <AITextarea
                  name={field.name}
                  label={field.label}
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  inputRef={ref}
                  error={!!errors[field.name]}
                  helperText={errors[field.name]?.message as string}
                  placeholder={field.placeholder}
                  rows={field.rowSpan}
                  aiHandler={field.aiHandler}
                />
              )}
            />
          </Box>
        );

      default:
        return (
          <Controller
            name={field.name}
            control={control}
            render={({ field: { onChange, value, onBlur, ref } }) => (
              <TextField
                fullWidth
                type={field.type}
                value={value || ""}
                onChange={onChange}
                onBlur={onBlur}
                inputRef={ref}
                error={!!errors[field.name]}
                helperText={errors[field.name]?.message as string}
              />
            )}
          />
        );
    }
  };

  return (
    <form onSubmit={handleFormSubmit}>
      <Stack direction="row" spacing={2} sx={{ mt: 4 }}>
        {groups.map((group, index) => (
          <>
            <Box key={group?.name} sx={{ mb: 4, width: "100%" }}>
              {group.name && (
                <Typography variant="h6" gutterBottom>
                  {group.name}
                </Typography>
              )}
              {group.description && (
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                    mb: 2,
                  }}
                >
                  {group.description}
                </Typography>
              )}

              <Stack display="flex" direction="column">
                {group.fields.map((field) => (
                  <Box key={field.name} mb={2}>
                    {field.type !== "aiTextarea" && (
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 1,
                        }}
                      >
                        {field.label}
                      </Typography>
                    )}
                    {renderField(field)}
                  </Box>
                ))}
              </Stack>
            </Box>
            {index < groups.length - 1 && (
              <Divider sx={{ my: 3, borderColor: "divider" }} orientation="vertical" flexItem />
            )}
          </>
        ))}
      </Stack>
      <Divider sx={{ my: 3, borderColor: "divider" }} />
      <Stack direction="row" spacing={2} justifyContent="flex-end" sx={{ mt: 4 }}>
        {onCancel && (
          <Button onClick={onCancel} variant="text">
            Cancel
          </Button>
        )}
        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={trackChanges && !hasChanges}
        >
          {submitButtonText}
        </Button>
      </Stack>
    </form>
  );
};

export default FormBuilder;

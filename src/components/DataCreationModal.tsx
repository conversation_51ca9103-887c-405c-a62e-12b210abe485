"use client";

import { BuildingBlockTypeDetail } from "@/types/buildingBlock";
import React from "react";
import Form<PERSON>uilder from "./FormBuilder";
import Modal from "./Modal";

// Define a flexible BuildingBlock interface that can handle various data structures
interface FlexibleBuildingBlock {
  id?: number;
  type?: number | any;
  sub_type?: number | any;
  role?: number | null;
  brand?: number | null;
  segment?: number | null;
  name?: string;
  desc?: string;
  order?: number;
  content?: any;
  created_at?: string;
  updated_at?: string;
  [key: string]: any;
}

// Extend the BuildingBlockTypeDetail interface to include columns
interface ExtendedBuildingBlockTypeDetail extends BuildingBlockTypeDetail {
  columns?: { field_name: string }[];
}

interface DataCreationModalProps {
  open: boolean;
  onClose: () => void;
  buildingBlockType: ExtendedBuildingBlockTypeDetail;
  formGroups: any[];
  isEditMode: boolean;
  editingBlock?: FlexibleBuildingBlock | null;
  selectedSubType: number | null;
  onSubmit: (values: any) => Promise<void>;
  onEditSubmit: (values: any) => Promise<void>;
  convertDatesToDayjs: (
    data: Record<string, any>,
    buildingBlockType: ExtendedBuildingBlockTypeDetail | null
  ) => Record<string, any>;
}

const DataCreationModal: React.FC<DataCreationModalProps> = ({
  open,
  onClose,
  buildingBlockType,
  formGroups,
  isEditMode,
  editingBlock,
  selectedSubType,
  onSubmit,
  onEditSubmit,
  convertDatesToDayjs,
}) => {
  // Calculate initial values for the form
  const getInitialValues = () => {
    const rawInitialValues =
      isEditMode && editingBlock
        ? {
            // Include top-level properties
            name: editingBlock.name,
            desc: editingBlock.desc,
            sub_type: editingBlock.sub_type,
            role: editingBlock.role,
            brand: editingBlock.brand,
            segment: editingBlock.segment,
            order: editingBlock.order,
            // Include content properties (where custom fields are stored)
            ...(editingBlock.content || {}),
          }
        : {
            sub_type:
              selectedSubType ||
              (buildingBlockType.sub_types.length > 0 ? buildingBlockType.sub_types[0].id : 0),
          };

    // Convert date strings to dayjs objects for date fields
    const initialValues = convertDatesToDayjs(rawInitialValues, buildingBlockType);

    console.log("FormBuilder initialValues (raw):", rawInitialValues);
    console.log("FormBuilder initialValues (converted):", initialValues);
    return initialValues;
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      title={isEditMode ? `Edit ${buildingBlockType.name}` : `Add ${buildingBlockType.name}`}
    >
      <FormBuilder
        key={isEditMode ? `edit-${editingBlock?.id}` : "create"}
        groups={formGroups}
        initialValues={getInitialValues()}
        onSubmit={isEditMode ? onEditSubmit : onSubmit}
        onCancel={onClose}
        submitButtonText={isEditMode ? "Update" : "Create"}
      />
    </Modal>
  );
};

export default DataCreationModal;

import { Box, Divider, Modal as MuiModal, Typography } from "@mui/material";
import React from "react";

interface ModalProps {
  open: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  maxWidth?: number;
  headerBackgroundColor?: string;
}

const Modal: React.FC<ModalProps> = ({
  open,
  onClose,
  title,
  children,
  maxWidth = 900,
  headerBackgroundColor,
}) => {
  return (
    <MuiModal open={open} onClose={onClose} aria-labelledby="modal-title">
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 2,
          padding: 3,
          width: "100%",
          maxWidth,
          maxHeight: "90vh",
          overflow: "auto",
          outline: "none",
        }}
      >
        <Box
          sx={{
            backgroundColor: headerBackgroundColor || "background.paper",
            mx: -3,
            mt: -3,
            mb: 3,
            px: 3,
            py: 2,
          }}
        >
          <Typography variant="h5" color="textPrimary" id="modal-title">
            {title}
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        {children}
      </Box>
    </MuiModal>
  );
};

export default Modal;

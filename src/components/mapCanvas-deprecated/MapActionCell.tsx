import { Action } from "@/types/map";
import { useDroppable } from "@dnd-kit/core";
import AddIcon from "@mui/icons-material/Add";
import { Box, Button, Card } from "@mui/material";
import React, { useState } from "react";
import ActionCard from "./ActionCard";

type MapActionCellProps = {
  phaseId: number;
  type: string;
  role: string;
  actions: Action[];
  height: number;
  isPanningDisabled: boolean;
  onEditAction: (id: string, updatedText: string) => void;
  onDeleteAction: (id: string) => void;
  onAddAction: () => void; // onAddAction with type, role, phaseId
};

const MapActionCell: React.FC<MapActionCellProps> = ({
  phaseId,
  type,
  role,
  actions,
  height,
  // isPanningDisabled,
  onEditAction,
  onDeleteAction,
  onAddAction,
}) => {
  const [isHovering, setIsHovering] = useState(false);
  const cellId = `cell-${phaseId}-${type}-${role}`;

  const { setNodeRef, isOver } = useDroppable({
    id: cellId,
    data: {
      type: "action",
    },
    disabled: false,
  });

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  return (
    <Card
      ref={setNodeRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      sx={{
        width: "60rem",
        my: 2,
        p: 2,
        height: `${height}rem`,
        bgcolor: isOver ? "action.hover" : "map.cell",
        display: "grid",
        gridTemplateColumns: "repeat(3, 1fr)",
        gap: "1rem",
        position: "relative",
        transition: "background-color 0.2s",
      }}
    >
      {actions.map((action) => {
        return (
          <ActionCard
            key={action.id}
            action={action}
            onEdit={(newText) => onEditAction(action.id, newText)}
            onDelete={() => onDeleteAction(action.id)}
          />
        );
      })}
      {/* Add Action Button - only visible on hover */}
      <Box sx={{ position: "absolute", bottom: 8, right: 8 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={onAddAction}
          sx={{
            backgroundColor: "#c8ff00",
            color: "#000000",
            fontWeight: "bold",
            opacity: isHovering ? 1 : 0,
            transition: "opacity 0.2s ease-in-out",
            "&:hover": {
              backgroundColor: "#b3e600",
            },
          }}
        >
          ADD
        </Button>
      </Box>
    </Card>
  );
};

export default MapActionCell;

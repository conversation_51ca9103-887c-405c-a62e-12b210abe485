// import { MapCanvasActionType } from "@/context/MapCanvasProvider";
// import mapService from "@/services/MapService";
// import { useState } from "react";

// interface UseActionHandlersProps {
//   dispatch: React.Dispatch<any>;
//   mapId: string;
// }

// export const useActionHandlers = ({ dispatch, mapId }: UseActionHandlersProps) => {
//   const [selectedPhase, setSelectedPhase] = useState<number>(0);
//   const [selectedRoleType, setSelectedRoleType] = useState<string>("Customer");
//   const [editingRoleId, setEditingRoleId] = useState<number | null>(null);
//   const [pendingActionData, setPendingActionData] = useState<{
//     phase: number;
//     type: string | number;
//   } | null>(null);
//   const [selectPersonaModalOpen, setSelectPersonaModalOpen] = useState(false);

//   const handleActionDragEnd = async (activeId: string, overId: string) => {
//     if (overId.startsWith("cell-")) {
//       const [, phase, role] = overId.split("-");
//       const noteId = activeId.replace("note-", "");
//       const newAction = await mapService.patchAction(
//         { id: noteId, phase: parseInt(phase), map_role_id: parseInt(role) },
//         mapId
//       );
//       dispatch({
//         type: MapCanvasActionType.EDIT_ACTION,
//         payload: {
//           id: noteId,
//           updatedFields: { ...newAction },
//         },
//       });
//     }
//   };

//   const onEditAction = async (id: string, updatedText: string, cellType: string) => {
//     if (cellType === "action") {
//       const newAction = await mapService.patchAction(
//         { id: id, name: updatedText, desc: updatedText },
//         mapId
//       );
//       dispatch({
//         type: MapCanvasActionType.EDIT_ACTION,
//         payload: { id, updatedFields: { ...newAction } },
//       });
//     } else {
//       dispatch({
//         type: MapCanvasActionType.EDIT_BUILDING_BLOCK,
//         payload: { id: Number(id), updatedFields: { desc: updatedText } },
//       });
//     }
//   };

//   const onDeleteAction = async (id: string, cellType: string) => {
//     if (cellType === "action") {
//       await mapService.deleteAction(id, mapId);
//       dispatch({ type: MapCanvasActionType.DELETE_ACTION, payload: { id } });
//     } else {
//       // For building blocks, we need to call the API to delete the connection
//       try {
//         // The id is the connection_id for building blocks
//         await mapService.deleteBuildingBlock(Number(id), mapId);

//         // Update the state after successful deletion
//         dispatch({
//           type: MapCanvasActionType.DELETE_BUILDING_BLOCK,
//           payload: { id: Number(id) },
//         });
//       } catch (error) {
//         console.error("Failed to delete building block:", error);
//       }
//     }
//   };

//   const onAddAction = async (phase: number, type: string | number, _role?: number) => {
//     setSelectedPhase(phase);

//     // Determine role type based on the type parameter
//     const roleType = typeof type === "string" ? type : "Customer";
//     setSelectedRoleType(roleType);

//     // Open persona selection modal and store pending action data
//     setPendingActionData({ phase, type });
//     setSelectPersonaModalOpen(true);
//   };

//   const onEditPersona = (roleId: number) => {
//     setEditingRoleId(roleId);
//     setSelectPersonaModalOpen(true);
//   };

//   const handleClosePersonaModal = () => {
//     setSelectPersonaModalOpen(false);
//     setEditingRoleId(null);
//     setPendingActionData(null);
//   };

//   /**
//    * Creates a new action with the provided data
//    * @param phaseId - The phase ID to associate the action with
//    * @param roleId - The role ID to associate the action with
//    * @param actionType - The type of action (e.g., "Application")
//    * @param name - The name of the action (defaults to "New action")
//    * @param desc - The description of the action (defaults to empty string)
//    * @returns The newly created action
//    */
//   const handleCreateAction = async (
//     phaseId: number,
//     roleId: number,
//     actionType: string = "Application",
//     name: string = "New action",
//     desc: string = "New action created, click to edit"
//   ) => {
//     try {
//       // Create the action payload
//       const actionPayload: any = {
//         name,
//         desc,
//         phase: phaseId,
//         role: roleId,
//         source_type: "M", // Manual creation
//         type: actionType,
//         channel: [], // Empty channel array
//       };

//       // Call the API to create the action
//       const newAction = await mapService.postAction(actionPayload, mapId);

//       // Update the state with the new action
//       dispatch({
//         type: MapCanvasActionType.ADD_ACTION,
//         payload: {
//           action: newAction,
//         },
//       });

//       return newAction;
//     } catch (error) {
//       console.error("Failed to create action:", error);
//       throw error;
//     }
//   };

//   return {
//     handleActionDragEnd,
//     onEditAction,
//     onDeleteAction,
//     onAddAction,
//     onEditPersona,
//     handleCreateAction,
//     selectedPhase,
//     selectedRoleType,
//     editingRoleId,
//     pendingActionData,
//     selectPersonaModalOpen,
//     setSelectPersonaModalOpen,
//     handleClosePersonaModal,
//   };
// };

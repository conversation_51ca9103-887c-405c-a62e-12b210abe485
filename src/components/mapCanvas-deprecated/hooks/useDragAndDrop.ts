// import { useState } from 'react';
// import {
//   DragStartEvent,
//   DragEndEvent,
//   Modifier,
//   MeasuringStrategy,
//   MeasuringFrequency
// } from '@dnd-kit/core';
// import { MapCanvasActionType } from '@/context/MapCanvasProvider';
// import { arrayMove } from '@dnd-kit/sortable';

// interface UseDragAndDropProps {
//   dispatch: any;
//   state: any;
//   transformValues: {
//     scale: number;
//     positionX: number;
//     positionY: number;
//   };
//   onActionDragEnd: (activeId: string, overId: string) => void;
// }

// export const useDragAndDrop = ({ dispatch, state, transformValues, onActionDragEnd }: UseDragAndDropProps) => {
//   const [activeId, setActiveId] = useState<string | null>(null);

//   const handleDragStart = (event: DragStartEvent) => {
//     setActiveId(event.active.id as string);
//   };

//   const handlePhaseDragEnd = (activeId: string, overId: string) => {
//     const activePhaseId = activeId.replace("phase-", "");
//     const overPhaseId = overId.replace("phase-", "");

//     const oldIndex = state.phases.findIndex(
//       (phase) => phase.id.toString() === activePhaseId
//     );
//     const newIndex = state.phases.findIndex(
//       (phase) => phase.id.toString() === overPhaseId
//     );

//     if (oldIndex !== -1 && newIndex !== -1) {
//       const updatedPhases = arrayMove(state.phases, oldIndex, newIndex).map(
//         (phase: Record<string, unknown>, index) => ({ ...phase, order: index })
//       );

//       dispatch({
//         type: MapCanvasActionType.REORDER_PHASES,
//         payload: updatedPhases,
//       });
//     }
//   };

//   const handleDragEnd = (event: DragEndEvent) => {
//     const { active, over } = event;
//     setActiveId(null);

//     if (!over) return;

//     const activeId = active.id as string;
//     const overId = over.id as string;

//     if (activeId.startsWith("note-")) {
//       onActionDragEnd(activeId, overId);
//     } else if (activeId.startsWith("phase-")) {
//       handlePhaseDragEnd(activeId, overId);
//     }
//   };

//   const adjustScaleModifier: Modifier = ({ transform }) => {
//     const { scale, positionX, positionY } = transformValues;
//     const scrollX = window.scrollX;
//     const scrollY = window.scrollY;

//     return {
//       ...transform,
//       x: (transform.x - scrollX) / scale - positionX / scale,
//       y: (transform.y - scrollY) / scale - positionY / scale,
//       scaleX: 1,
//       scaleY: 1
//     };
//   };

//   const measuringStrategy = {
//     droppable: {
//       strategy: MeasuringStrategy.Always,
//       frequency: MeasuringFrequency.Optimized,
//       measure: (element: HTMLElement) => {
//         const rect = element.getBoundingClientRect();
//         const scrollX = window.scrollX;
//         const scrollY = window.scrollY;

//         return {
//           ...rect,
//           top: (rect.top + scrollY) / transformValues.scale - transformValues.positionY / transformValues.scale,
//           left: (rect.left + scrollX) / transformValues.scale - transformValues.positionX / transformValues.scale,
//           width: rect.width / transformValues.scale,
//           height: rect.height / transformValues.scale,
//         };
//       }
//     }
//   };

//   return {
//     activeId,
//     handleDragStart,
//     handleDragEnd,
//     adjustScaleModifier,
//     measuringStrategy
//   };
// };

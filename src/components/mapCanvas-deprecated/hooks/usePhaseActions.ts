// import { MapCanvasActionType } from "@/context/MapCanvasProvider";
// import mapService from "@/services/MapService";
// import { Phase } from "@/types/map";
// import { useRef, useState } from "react";

// interface UsePhaseActionsProps {
//   dispatch: React.Dispatch<any>;
//   mapId: string;
// }

// export const usePhaseActions = ({ dispatch, mapId }: UsePhaseActionsProps) => {
//   const [initializedPhases, setInitializedPhases] = useState(false);
//   const initializationRef = useRef(false);

//   const onEditPhase = async (phase: Phase) => {
//     const newPhase = await mapService.patchPhase(phase, mapId);
//     dispatch({
//       type: MapCanvasActionType.EDIT_PHASE,
//       payload: { phase: newPhase },
//     });
//   };

//   const onDeletePhase = async (id: number) => {
//     await mapService.deletePhase(id, mapId);
//     dispatch({ type: MapCanvasActionType.DELETE_PHASE, payload: { id } });
//   };

//   const onAddPhase = async () => {
//     const newPhase = await mapService.postPhase(
//       { name: "New Phase", order: 0 }, // Order will be updated in the reducer
//       mapId
//     );
//     dispatch({ type: MapCanvasActionType.ADD_PHASE, payload: { phase: newPhase } });
//   };

//   // No need for this useEffect as we're handling initialization in the MapCanvas component

//   return {
//     onEditPhase,
//     onDeletePhase,
//     onAddPhase,
//     initializedPhases,
//     setInitializedPhases,
//     initializationRef,
//   };
// };

import { useCallback, useEffect, useRef, useState } from "react";

interface ContentBounds {
  width: number;
  height: number;
  minScale: number;
  maxScale: number;
}

interface MapInteractionValue {
  scale: number;
  translation: { x: number; y: number };
}

interface UseCanvasProps {
  onZoomChange?: (scale: number) => void;
  onFitView?: (scale: number, translation: { x: number; y: number }) => void;
  initialScale?: number;
  initialTranslation?: { x: number; y: number };
}

export const useCanvas = ({
  onZoomChange,
  onFitView,
  initialScale = 0.5,
  initialTranslation = { x: 100, y: 50 },
}: UseCanvasProps = {}) => {
  // Map interaction state
  const [mapInteractionValue, setMapInteractionValue] = useState<MapInteractionValue>({
    scale: initialScale,
    translation: initialTranslation,
  });

  // Content measurement for bounds calculation
  const contentRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [contentBounds, setContentBounds] = useState<ContentBounds>({
    width: 1200,
    height: 800,
    minScale: 0.1,
    maxScale: 3,
  });

  // Calculate content bounds for translation limits
  const calculateContentBounds = useCallback(() => {
    if (contentRef.current && containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();

      const contentWidth = contentRef.current.scrollWidth;
      const contentHeight = contentRef.current.scrollHeight;

      // Calculate minimum scale to fit content with some margin
      const scaleToFitWidth = (containerRect.width - 200) / contentWidth;
      const scaleToFitHeight = (containerRect.height - 200) / contentHeight;
      const minScale = Math.min(scaleToFitWidth, scaleToFitHeight, 1);

      const maxScale = 3;

      // Adjust scrollable area based on current zoom level
      const currentScale = mapInteractionValue.scale;
      const scaledWidth = contentWidth * currentScale;
      const scaledHeight = contentHeight * currentScale;

      setContentBounds({
        width: scaledWidth,
        height: scaledHeight,
        minScale: Math.max(0.2, minScale * 0.9),
        maxScale,
      });
    }
  }, [mapInteractionValue.scale]);


  const handleFitView = useCallback(() => {
    const scale = contentBounds.minScale;
    const translation = { x: 50, y: 50 };
    setMapInteractionValue({ scale, translation });
    onFitView?.(scale, translation);

    // Also scroll container to top-left
    if (containerRef.current) {
      containerRef.current.scrollTo(0, 0);
    }
  }, [contentBounds.minScale, onFitView, setMapInteractionValue]);

  // Transform state handler for react-zoom-pan-pinch
  const handleTransformChange = useCallback((_ref: any, state: any) => {
    setMapInteractionValue({
      scale: state.scale,
      translation: { x: state.positionX, y: state.positionY }
    });
    // Call onZoomChange callback when scale changes
    if (onZoomChange) {
      onZoomChange(state.scale);
    }
  }, [onZoomChange]);

  // Update the custom fit view when content bounds change
  useEffect(() => {
    if (contentBounds.width > 0) {
      handleFitView();
    }
  }, [contentBounds, handleFitView]);

  // Update bounds when content changes
  const setupResizeObserver = useCallback(
    () => {
      const resizeObserver = new ResizeObserver(() => {
        calculateContentBounds();
      });

      if (contentRef.current) {
        resizeObserver.observe(contentRef.current);
      }

      if (containerRef.current) {
        resizeObserver.observe(containerRef.current);
      }

      setTimeout(calculateContentBounds, 100);

      return () => {
        resizeObserver.disconnect();
      };
    },
    [calculateContentBounds]
  );

  // Space key state for panning/pinch mode
  const [isSpacePressed, setIsSpacePressed] = useState(false);
  const [isPanningEnabled, setIsPanningEnabled] = useState(false);

  // Handle space key press for temporary pinch/pan mode
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space' && !isSpacePressed) {
        e.preventDefault();
        setIsSpacePressed(true);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space' && isSpacePressed) {
        e.preventDefault();
        setIsSpacePressed(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isSpacePressed]);

  // Transform wrapper zoom functions that work with setTransform
  const createZoomInFunction = useCallback((setTransform: any) => {
    return () => {
      if (setTransform) {
        // Use our internal state instead of relying on TransformWrapper state
        const currentScale = mapInteractionValue.scale;
        const currentX = mapInteractionValue.translation.x;
        const currentY = mapInteractionValue.translation.y;
        
        const newScale = Math.min(currentScale * 1.1, contentBounds.maxScale);
        
        // Calculate position to keep top-left corner fixed
        const newX = currentX * (newScale / currentScale);
        const newY = currentY * (newScale / currentScale);
        
        setTransform(newX, newY, newScale, 200, "easeOut");
      }
    };
  }, [contentBounds.maxScale, mapInteractionValue]);

  const createZoomOutFunction = useCallback((setTransform: any) => {
    return () => {
      if (setTransform) {
        // Use our internal state instead of relying on TransformWrapper state
        const currentScale = mapInteractionValue.scale;
        const currentX = mapInteractionValue.translation.x;
        const currentY = mapInteractionValue.translation.y;
        
        const newScale = Math.max(currentScale / 1.1, contentBounds.minScale);
        
        // Calculate position to keep top-left corner fixed
        const newX = currentX * (newScale / currentScale);
        const newY = currentY * (newScale / currentScale);
        
        setTransform(newX, newY, newScale, 200, "easeOut");
      }
    };
  }, [contentBounds.minScale, mapInteractionValue]);

  // Get dynamic container styles based on zoom level
  const getContainerStyles = useCallback((mainContainer?: HTMLElement, dynamicWidth?: number, dynamicHeight?: number) => {
    const container = mainContainer || containerRef.current;
    const content = contentRef.current;
    
    if (!container || !content) {
      return {
        overflowX: 'auto' as const,
        overflowY: 'auto' as const,
      };
    }
    
    // Get the actual dimensions
    const containerRect = container.getBoundingClientRect();
    
    // Use provided dynamic dimensions or calculate from content
    const contentWidth = dynamicWidth || content.scrollWidth;
    const contentHeight = dynamicHeight || content.scrollHeight;
    
    // The content is already scaled by the dynamic size calculation
    // So we don't need to multiply by scale again
    const effectiveContentWidth = contentWidth;
    const effectiveContentHeight = contentHeight;
    
    // Determine if scrolling should be enabled
    // Add some margin to account for padding and borders
    const margin = 100; // Increased margin for better UX
    const needsHorizontalScroll = effectiveContentWidth > (containerRect.width - margin);
    const needsVerticalScroll = effectiveContentHeight > (containerRect.height - margin);
    
    return {
      overflowX: needsHorizontalScroll ? 'auto' : 'hidden',
      overflowY: needsVerticalScroll ? 'auto' : 'hidden',
    };
  }, []);

  return {
    handleFitView,
    currentScale: mapInteractionValue.scale,
    mapInteractionValue,
    setMapInteractionValue,
    contentBounds,
    contentRef,
    containerRef,
    setupResizeObserver,
    handleTransformChange,
    isSpacePressed,
    isPanningEnabled,
    setIsPanningEnabled,
    createZoomInFunction,
    createZoomOutFunction,
    getContainerStyles,
  };
};

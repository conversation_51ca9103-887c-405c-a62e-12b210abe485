// import { MapCanvasActionType } from "@/context/MapCanvasProvider";
// import { processToggleMenuBuildingBlockTypes } from "@/utils/mapHelper";
// import { useEffect } from "react";

// interface UseMapInitializationProps {
//   map: any;
//   dispatch: any;
//   commonData: any;
//   initializedPhases: boolean;
//   onAddPhase: () => void;
//   initializationRef: React.MutableRefObject<boolean>;
//   setInitializedPhases: (value: boolean) => void;
// }

// export const useMapInitialization = ({
//   map,
//   dispatch,
//   commonData,
//   initializedPhases,
//   onAddPhase,
//   initializationRef,
//   setInitializedPhases,
// }: UseMapInitializationProps) => {
//   // Initialize phases if needed
//   useEffect(() => {
//     const initializePhases = () => {
//       if (!initializationRef.current && map && map.phases.length === 0) {
//         initializationRef.current = true;
//         onAddPhase();
//         onAddPhase();
//         setInitializedPhases(true);
//       } else if (map) {
//         setInitializedPhases(true);
//       }
//       dispatch({
//         type: MapCanvasActionType.SET_DATA,
//         payload: {
//           ...map,
//           id: map.id,
//         },
//       });
//     };

//     if (!initializedPhases) {
//       initializePhases();
//     }
//   }, [map, dispatch, initializedPhases, onAddPhase, initializationRef, setInitializedPhases]);

//   // Set visible role types
//   useEffect(() => {
//     dispatch({
//       type: MapCanvasActionType.SET_VISIBLE_ROLE_TYPES,
//       payload: commonData.role_types.map((role: any) => role.name),
//     });
//   }, [commonData.role_types, dispatch]);

//   // Set visible building block types
//   useEffect(() => {
//     const allBuildingBlockTypes = processToggleMenuBuildingBlockTypes(
//       commonData.building_block_types
//     );
//     const allTypeNames = allBuildingBlockTypes.map((type) => type.name);
//     dispatch({
//       type: MapCanvasActionType.SET_VISIBLE_BUILDING_BLOCK_TYPES,
//       payload: allTypeNames,
//     });
//   }, [commonData.building_block_types, dispatch]);
// };

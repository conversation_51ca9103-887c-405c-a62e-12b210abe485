import ConfirmDialog from "@/components/ConfirmDialog";
import mapService from "@/services/MapService";
import InfoIcon from "@mui/icons-material/InfoOutlined";
import ArrowDown from "@mui/icons-material/KeyboardArrowDownOutlined";
import ArrowUp from "@mui/icons-material/KeyboardArrowUpOutlined";
import {
  Box,
  Chip,
  ClickAwayListener,
  Divider,
  IconButton,
  List,
  ListItemButton,
  ListItemText,
  Paper,
  TextField,
  Typography,
} from "@mui/material";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

interface FloatingMenuProps {
  name: string;
  mapId: string;
  back: () => void;
  status: string;
}

const FloatingMenu: React.FC<FloatingMenuProps> = ({ name, mapId, back, status }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(name);
  const router = useRouter();

  const handleClickAway = () => {
    setIsOpen(false);
  };

  const handleDeleteClick = () => {
    setDeleteConfirmOpen(true);
    setIsOpen(false);
  };

  const handleDeleteConfirm = async () => {
    try {
      await mapService.deleteMap(mapId);
      router.push("/journeys");
    } catch (error) {
      console.error("Failed to delete map:", error);
    }
  };

  const handleNameClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering back action
    setIsEditing(true);
  };

  const handleNameSave = async () => {
    try {
      await mapService.patchMap({ id: mapId, name: editedName });
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to update map name:", error);
    }
  };

  const handleNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleNameSave();
    } else if (e.key === "Escape") {
      setEditedName(name);
      setIsEditing(false);
    }
  };

  return (
    <>
      <Box
        sx={{
          position: "fixed",
          top: "20px",
          left: "calc(var(--sidebar-width, 0px) + 30px)",
          zIndex: 1000,
          transition: "left 0.2s",
        }}
      >
        <ClickAwayListener onClickAway={handleClickAway}>
          <Paper
            elevation={3}
            sx={{
              overflow: "hidden",
              transition: "max-height 0.3s ease-out",
              maxHeight: isOpen ? "500px" : "50px",
              background: "none",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                px: "16px",
                borderRadius: "8px",
                backgroundColor: "background.paper",
                border: "1px solid",
                borderColor: "divider",
                height: "48px",
              }}
            >
              <Box
                onClick={back}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  flex: 1,
                  cursor: "pointer",
                  "&:hover": {
                    opacity: 0.8,
                  },
                }}
              >
                <Typography variant="body1" fontWeight="bold" sx={{ marginRight: 1 }}>
                  Mul.apin
                </Typography>
              </Box>
              <Divider orientation="vertical" flexItem sx={{ mx: 1.5 }} />
              {isEditing ? (
                <TextField
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  onBlur={handleNameSave}
                  onKeyDown={handleNameKeyDown}
                  autoFocus
                  size="small"
                  sx={{
                    marginRight: 1,
                    width: "200px",
                    "& .MuiInputBase-input": {
                      color: "#888",
                    },
                  }}
                  onClick={(e) => e.stopPropagation()} // Prevent triggering back action
                />
              ) : (
                <Typography
                  variant="body1"
                  sx={{
                    marginRight: 1,
                    color: "#888",
                    cursor: "text",
                    "&:hover": {
                      textDecoration: "underline",
                    },
                  }}
                  onClick={handleNameClick}
                >
                  {editedName}
                </Typography>
              )}
              <Chip label={status} size="small" variant="outlined" />
              <IconButton size="small" sx={{ color: "#888", marginRight: 1 }}>
                <InfoIcon fontSize="small" />
              </IconButton>
              <IconButton size="small" onClick={() => setIsOpen(!isOpen)} sx={{ color: "#fff" }}>
                {isOpen ? <ArrowUp fontSize="small" /> : <ArrowDown fontSize="small" />}
              </IconButton>
            </Box>
            {isOpen && (
              <Paper
                sx={{
                  width: "100%",
                  mt: "2px",
                  borderRadius: "5px",
                }}
              >
                <List sx={{ p: 0 }}>
                  <ListItemButton onClick={back}>
                    <ListItemText primary="Back to list" />
                  </ListItemButton>
                  <Divider />
                  <ListItemButton>
                    <ListItemText primary="Undo" />
                  </ListItemButton>
                  <ListItemButton>
                    <ListItemText primary="Redo" />
                  </ListItemButton>
                  <Divider />
                  <ListItemButton>
                    <ListItemText primary="Find" />
                  </ListItemButton>
                  <Divider />
                  <ListItemButton>
                    <ListItemText primary="Export" />
                  </ListItemButton>
                  <ListItemButton>
                    <ListItemText primary="Export to PDF" />
                  </ListItemButton>
                  <Divider />
                  <ListItemButton>
                    <ListItemText primary="View map info" />
                  </ListItemButton>
                  <ListItemButton onClick={handleDeleteClick}>
                    <ListItemText primary="Delete map" />
                  </ListItemButton>
                </List>
              </Paper>
            )}
          </Paper>
        </ClickAwayListener>
      </Box>

      <ConfirmDialog
        open={deleteConfirmOpen}
        title="Delete Map"
        message={`Are you sure you want to delete "${name}"? This action cannot be undone.`}
        onConfirm={handleDeleteConfirm}
        onCancel={() => setDeleteConfirmOpen(false)}
      />
    </>
  );
};

export default FloatingMenu;

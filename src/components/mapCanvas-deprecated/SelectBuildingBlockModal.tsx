"use client";

import { useAppContext } from "@/context/AppProvider";
import buildingBlockService from "@/services/BuildingBlockService";
import { BuildingBlock as BuildingBlockType } from "@/types/buildingBlock";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  Checkbox,
  InputAdornment,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import Modal from "../Modal";

interface SelectBuildingBlockModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (selectedBlocks: BuildingBlockType[]) => void;
  title?: string;
  typeId: number;
  typeName: string;
  subTypeId?: number; // Optional subtype ID for filtering
  existingBuildingBlocks?: any[]; // Building blocks already added to the map
}

const SelectBuildingBlockModal: React.FC<SelectBuildingBlockModalProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  typeId,
  typeName,
  subTypeId,
  existingBuildingBlocks = [],
}) => {
  const { commonData } = useAppContext();
  const [buildingBlocks, setBuildingBlocks] = useState<BuildingBlockType[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBlocks, setSelectedBlocks] = useState<Set<number>>(new Set());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Fetch building blocks when modal opens
  useEffect(() => {
    const fetchBuildingBlocks = async () => {
      if (!open || !typeId) return;

      try {
        setLoading(true);
        const data = await buildingBlockService.getBuildingBlocks(typeId, subTypeId);
        setBuildingBlocks(data);
      } catch (error) {
        console.error("Failed to fetch building blocks:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchBuildingBlocks();
  }, [open, typeId, subTypeId]);

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setSearchQuery("");
      setSelectedBlocks(new Set());
      setPage(0);
    }
  }, [open]);

  // Filter building blocks based on search
  const filteredBlocks = useMemo(() => {
    let filtered = buildingBlocks;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (block) =>
          block.name?.toLowerCase().includes(query) || block.desc?.toLowerCase().includes(query)
      );
    }

    // Filter out blocks that are already added to the map
    const existingBlockIds = new Set(
      existingBuildingBlocks
        .filter((block) => {
          const blockTypeId = typeof block.type === "object" ? block.type.id : block.type;
          return blockTypeId === typeId;
        })
        .map((block) => block.id)
    );

    filtered = filtered.filter((block) => !existingBlockIds.has(block.id));

    return filtered;
  }, [buildingBlocks, searchQuery, existingBuildingBlocks, typeId]);

  // Paginated building blocks
  const paginatedBlocks = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredBlocks.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredBlocks, page, rowsPerPage]);

  // Handle checkbox selection
  const handleSelectBlock = (blockId: number) => {
    const newSelected = new Set(selectedBlocks);
    if (newSelected.has(blockId)) {
      newSelected.delete(blockId);
    } else {
      newSelected.add(blockId);
    }
    setSelectedBlocks(newSelected);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedBlocks.size === paginatedBlocks.length) {
      setSelectedBlocks(new Set());
    } else {
      const allIds = new Set(paginatedBlocks.map((block) => block.id));
      setSelectedBlocks(allIds);
    }
  };

  // Handle confirm
  const handleConfirm = () => {
    const selectedBlockData = buildingBlocks.filter((block) => selectedBlocks.has(block.id));
    onConfirm?.(selectedBlockData);
    onClose();
  };

  // Handle page change
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Modal open={open} onClose={onClose} title={title || `Select ${typeName}`} maxWidth={1200}>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
        {/* Search Filter */}
        <TextField
          placeholder="Search"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            },
          }}
          sx={{ maxWidth: 400 }}
        />

        {/* Table */}
        <TableContainer sx={{ maxHeight: 400 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={
                      selectedBlocks.size > 0 && selectedBlocks.size < paginatedBlocks.length
                    }
                    checked={
                      paginatedBlocks.length > 0 && selectedBlocks.size === paginatedBlocks.length
                    }
                    onChange={handleSelectAll}
                    sx={{
                      color: "grey.500",
                      "&.Mui-checked": {
                        color: "primary.main",
                      },
                    }}
                  />
                </TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Description</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    <Typography>Loading...</Typography>
                  </TableCell>
                </TableRow>
              ) : paginatedBlocks.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    <Typography color="text.secondary">
                      {filteredBlocks.length === 0 ? "No data available" : "No results found"}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedBlocks.map((block) => (
                  <TableRow
                    key={block.id}
                    hover
                    onClick={() => handleSelectBlock(block.id)}
                    sx={{ cursor: "pointer" }}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedBlocks.has(block.id)}
                        sx={{
                          color: "grey.500",
                          "&.Mui-checked": {
                            color: "primary.main",
                          },
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{block.name || "Untitled"}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {block.desc || "No description"}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          component="div"
          count={filteredBlocks.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25]}
        />

        {/* Actions */}
        <Stack direction="row" spacing={2} justifyContent="flex-end">
          <Button variant="outlined" onClick={onClose}>
            CANCEL
          </Button>
          <Button variant="contained" onClick={handleConfirm} disabled={selectedBlocks.size === 0}>
            CONFIRM
          </Button>
        </Stack>
      </Box>
    </Modal>
  );
};

export default SelectBuildingBlockModal;

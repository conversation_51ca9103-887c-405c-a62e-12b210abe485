"use client";

import AccessTimeIcon from "@mui/icons-material/AccessTime";
import AddIcon from "@mui/icons-material/Add";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import {
  Box,
  Button,
  Collapse,
  IconButton,
  ListItem,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import TouchpointFileUpload from "./TouchpointFileUpload";

export interface TouchpointItem {
  id: string;
  title: string;
  description: string;
  status: "pending" | "scheduled" | "completed";
  isActive: boolean;
  referenceImage?: string;
  sections?: TouchpointSection[];
}

export interface TouchpointSection {
  id: string;
  title: string;
  items?: string[];
}

interface TouchpointCardProps {
  touchpoint: TouchpointItem;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onExpand?: (id: string) => void;
  onImageUpload?: (id: string, file: File) => void;
  onSectionAdd?: (touchpointId: string, sectionTitle: string) => void;
  onSectionItemAdd?: (touchpointId: string, sectionId: string, item: string) => void;
}

const TouchpointCard: React.FC<TouchpointCardProps> = ({
  touchpoint,
  onEdit,
  onDelete,
  onExpand,
  onImageUpload,
  onSectionAdd,
  onSectionItemAdd,
}) => {
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [newSectionTitle, setNewSectionTitle] = useState("");
  const [newSectionItems, setNewSectionItems] = useState<{ [key: string]: string }>({});

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const handleEdit = () => {
    if (onEdit) {
      onEdit(touchpoint.id);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(touchpoint.id);
    }
    handleMenuClose();
  };

  const handleExpand = () => {
    setIsExpanded(!isExpanded);
    if (onExpand) {
      onExpand(touchpoint.id);
    }
  };

  const handleImageUpload = async (file: File) => {
    if (onImageUpload) {
      onImageUpload(touchpoint.id, file);
    }
  };

  const handleAddSection = () => {
    if (newSectionTitle.trim() && onSectionAdd) {
      onSectionAdd(touchpoint.id, newSectionTitle.trim());
      setNewSectionTitle("");
    }
  };

  const handleAddSectionItem = (sectionId: string) => {
    const item = newSectionItems[sectionId];
    if (item?.trim() && onSectionItemAdd) {
      onSectionItemAdd(touchpoint.id, sectionId, item.trim());
      setNewSectionItems((prev) => ({ ...prev, [sectionId]: "" }));
    }
  };

  const getStatusIcon = (status: TouchpointItem["status"]) => {
    switch (status) {
      case "pending":
        return <AccessTimeIcon sx={{ color: "#ff9800", fontSize: 16 }} />;
      case "scheduled":
        return <CalendarTodayIcon sx={{ color: "#2196f3", fontSize: 16 }} />;
      case "completed":
        return <CheckCircleIcon sx={{ color: "#4caf50", fontSize: 16 }} />;
      default:
        return <AccessTimeIcon sx={{ color: "#ff9800", fontSize: 16 }} />;
    }
  };

  return (
    <>
      <ListItem
        sx={{
          backgroundColor: "#404040",
          borderRadius: isExpanded ? "8px 8px 0 0" : 1,
          mb: isExpanded ? 0 : 1,
          "&:hover": {
            backgroundColor: "#4a4a4a",
          },
        }}
      >
        <ListItemIcon sx={{ minWidth: 40 }}>{getStatusIcon(touchpoint.status)}</ListItemIcon>
        <ListItemText
          primary={
            <Typography variant="body2" sx={{ color: "white", fontWeight: 500 }}>
              {touchpoint.title}
            </Typography>
          }
          secondary={
            <Typography variant="caption" sx={{ color: "rgba(255, 255, 255, 0.7)" }}>
              {touchpoint.description}
            </Typography>
          }
        />
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <IconButton
            size="small"
            onClick={handleMenuClick}
            sx={{ color: "rgba(255, 255, 255, 0.7)" }}
          >
            <MoreHorizIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={handleExpand}
            sx={{
              color: "rgba(255, 255, 255, 0.7)",
              transform: isExpanded ? "rotate(180deg)" : "rotate(0deg)",
              transition: "transform 0.2s ease-in-out",
            }}
          >
            <ExpandMoreIcon fontSize="small" />
          </IconButton>
        </Box>
      </ListItem>

      {/* Expanded Content */}
      <Collapse in={isExpanded} timeout="auto" unmountOnExit>
        <Box
          sx={{
            backgroundColor: "#404040",
            borderRadius: "0 0 8px 8px",
            mb: 1,
            p: 3,
          }}
        >
          {/* Reference Screen Section */}
          <Typography variant="body2" sx={{ color: "white", mb: 2, fontWeight: 500 }}>
            Reference Screen
          </Typography>

          <TouchpointFileUpload
            onUpload={handleImageUpload}
            uploadedImage={touchpoint.referenceImage}
          />

          {/* Dynamic Sections */}
          {touchpoint.sections?.map((section) => (
            <Box key={section.id} sx={{ mt: 3 }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  mb: 2,
                }}
              >
                <Typography variant="body2" sx={{ color: "white", fontWeight: 500 }}>
                  {section.title}
                </Typography>
                <Button
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => handleAddSectionItem(section.id)}
                  sx={{
                    color: "#2196f3",
                    textTransform: "none",
                    fontSize: "0.75rem",
                    minWidth: "auto",
                    px: 1,
                  }}
                >
                  ADD
                </Button>
              </Box>

              {/* Section Items */}
              {section.items?.map((item, index) => (
                <Typography
                  key={index}
                  variant="body2"
                  sx={{ color: "rgba(255, 255, 255, 0.8)", mb: 1, pl: 2 }}
                >
                  • {item}
                </Typography>
              ))}
            </Box>
          ))}
        </Box>
      </Collapse>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            backgroundColor: "#2a2a2a",
            color: "white",
          },
        }}
      >
        <MenuItem onClick={handleEdit}>Edit</MenuItem>
        <MenuItem onClick={handleDelete}>Delete</MenuItem>
      </Menu>
    </>
  );
};

export default TouchpointCard;

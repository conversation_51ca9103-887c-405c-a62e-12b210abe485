import { MapRole } from "@/types/map";
import { Card, Typography } from "@mui/material";
import React from "react";

type MapRowHeaderProps = {
  type: string;
  role?: number;
  roles?: MapRole[];
  height: number;
};

const MapRowHeader: React.FC<MapRowHeaderProps> = ({ type, role, roles, height }) => {
  const roleName =
    role && roles ? roles.find((r) => r.map_role_id === role)?.name ?? "Unknown Role" : null;

  return (
    <Card
      sx={{
        height: `${height}rem`,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        padding: 2,
        marginTop: 2,
        backgroundColor: "map.header",
        gap: "1rem",
      }}
    >
      <Typography variant="h6" align="center">
        {type}
      </Typography>
      {role && (
        <Typography variant="h6" align="center">
          {roleName}
        </Typography>
      )}
    </Card>
  );
};

export default MapRowHeader;

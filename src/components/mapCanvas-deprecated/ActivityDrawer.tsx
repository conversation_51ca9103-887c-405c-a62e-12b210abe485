"use client";

import AddIcon from "@mui/icons-material/Add";
import MoreHorizIcon from "@mui/icons-material/MoreHoriz";
import {
  Avatar,
  Box,
  Button,
  Drawer,
  IconButton,
  List,
  Menu,
  MenuItem,
  Tab,
  Tabs,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import TouchpointCard, { TouchpointItem } from "./TouchpointCard";

interface ActivityDrawerProps {
  open: boolean;
  onClose: () => void;
  userAvatar?: string;
  userName?: string;
  userDescription?: string;
  touchpoints?: TouchpointItem[];
  onAddTouchpoint?: () => void;
  onEditTouchpoint?: (id: string) => void;
  onDeleteTouchpoint?: (id: string) => void;
}

const ActivityDrawer: React.FC<ActivityDrawerProps> = ({
  open,
  onClose,
  userAvatar = "https://i.pravatar.cc/150?img=8",
  userName = "Customer",
  userDescription = "I conduct a high level review of the customer information and spread the accounts to determine if it is possible to proceed further, identifying additional information required",
  touchpoints = [],
  onAddTouchpoint,
  onEditTouchpoint,
  onDeleteTouchpoint,
}) => {
  const [headerMenuAnchor, setHeaderMenuAnchor] = useState<null | HTMLElement>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleHeaderMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setHeaderMenuAnchor(event.currentTarget);
  };

  const handleHeaderMenuClose = () => {
    setHeaderMenuAnchor(null);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const filteredTouchpoints = touchpoints.filter((tp) =>
    activeTab === 0 ? tp.isActive : !tp.isActive
  );

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        "& .MuiDrawer-paper": {
          width: isFullscreen ? "100vw" : "400px",
          height: "100vh", // Always use full viewport height for proper scrolling
          backgroundColor: "#2a2a2a",
          color: "white",
          padding: 0,
        },
      }}
    >
      <Box
        sx={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          backgroundColor: "#2a2a2a",
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            borderBottom: "1px solid rgba(255, 255, 255, 0.12)",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            backgroundColor: "#2a2a2a",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
              <IconButton
                onClick={onClose}
                sx={{
                  color: "white",
                  p: 0.5,
                  "& .MuiSvgIcon-root": { fontSize: 20 },
                }}
              >
                {/* Using a simple chevron right icon inline since import isn't working */}
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z" />
                </svg>
              </IconButton>
              <Typography
                variant="body2"
                sx={{
                  color: "white",
                  fontSize: "14px",
                  fontWeight: 400,
                }}
              >
                Close
              </Typography>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
              <IconButton
                onClick={handleToggleFullscreen}
                sx={{
                  color: "white",
                  p: 0.5,
                  "& .MuiSvgIcon-root": { fontSize: 20 },
                }}
              >
                {isFullscreen ? (
                  /* Exit fullscreen icon */
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z" />
                  </svg>
                ) : (
                  /* Expand icon */
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z" />
                  </svg>
                )}
              </IconButton>
              <Typography
                variant="body2"
                sx={{
                  color: "white",
                  fontSize: "14px",
                  fontWeight: 400,
                }}
              >
                {isFullscreen ? "Exit fullscreen" : "Expand"}
              </Typography>
            </Box>
          </Box>

          <IconButton
            onClick={handleHeaderMenuClick}
            sx={{
              color: "white",
              p: 0.5,
              "& .MuiSvgIcon-root": { fontSize: 20 },
            }}
          >
            <MoreHorizIcon />
          </IconButton>
        </Box>

        {/* Title */}
        <Box sx={{ p: 3, pb: 2 }}>
          <Typography variant="h5" sx={{ color: "white", fontWeight: 500 }}>
            Customer activity
          </Typography>
        </Box>

        {/* User Section */}
        <Box sx={{ px: 3, pb: 3 }}>
          <Box
            sx={{
              backgroundColor: "#3a3a3a",
              borderRadius: 1,
              p: 2,
              m: "5px",
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              gap: 2,
            }}
          >
            <Avatar
              src={userAvatar}
              alt={userName}
              sx={{
                width: 40,
                height: 40,
                flexShrink: 0,
              }}
            />
            <Typography
              variant="body2"
              sx={{
                color: "rgba(255, 255, 255, 0.9)",
                lineHeight: 1.5,
                fontSize: "14px",
              }}
            >
              {userDescription}
            </Typography>
          </Box>
        </Box>

        {/* Touchpoints Section */}
        <Box sx={{ px: 3, pb: 2 }}>
          <Box
            sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", mb: 2 }}
          >
            <Typography variant="h6" sx={{ color: "white", fontWeight: 500 }}>
              Touchpoints
            </Typography>
            <Button
              variant="contained"
              size="small"
              startIcon={<AddIcon />}
              onClick={onAddTouchpoint}
              sx={{
                backgroundColor: "#2196f3",
                color: "white",
                textTransform: "uppercase",
                fontSize: "0.75rem",
                "&:hover": {
                  backgroundColor: "#1976d2",
                },
              }}
            >
              ADD
            </Button>
          </Box>

          {/* Tabs */}
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              "& .MuiTabs-indicator": {
                backgroundColor: "#2196f3",
              },
              "& .MuiTab-root": {
                color: "rgba(255, 255, 255, 0.7)",
                textTransform: "uppercase",
                fontSize: "0.75rem",
                fontWeight: 500,
                minWidth: "auto",
                padding: "8px 16px",
                "&.Mui-selected": {
                  color: "#2196f3",
                },
              },
            }}
          >
            <Tab label="ACTIVE" />
            <Tab label="ARCHIVED" />
          </Tabs>
        </Box>

        {/* Touchpoints List */}
        <Box
          sx={{
            flex: 1,
            overflow: "auto",
            minHeight: 0, // Ensures flex child can shrink below content size
          }}
        >
          <List sx={{ px: 2, pb: 2 }}>
            {filteredTouchpoints.map((touchpoint) => (
              <TouchpointCard
                key={touchpoint.id}
                touchpoint={touchpoint}
                onEdit={onEditTouchpoint}
                onDelete={onDeleteTouchpoint}
                onExpand={(id) => console.log("Expand touchpoint:", id)}
              />
            ))}
          </List>
        </Box>

        {/* Header Menu */}
        <Menu
          anchorEl={headerMenuAnchor}
          open={Boolean(headerMenuAnchor)}
          onClose={handleHeaderMenuClose}
          PaperProps={{
            sx: {
              backgroundColor: "#2a2a2a",
              color: "white",
            },
          }}
        >
          <MenuItem onClick={handleHeaderMenuClose}>Settings</MenuItem>
          <MenuItem onClick={handleHeaderMenuClose}>Export</MenuItem>
        </Menu>
      </Box>
    </Drawer>
  );
};

export default ActivityDrawer;

"use client";

import Modal from "@/components/Modal";
import { useAppContext } from "@/context/AppProvider";
import buildingBlockService from "@/services/BuildingBlockService";
import { BuildingBlock } from "@/types/buildingBlock";
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  Checkbox,
  Chip,
  FormControl,
  FormControlLabel,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import React, { useCallback, useEffect, useMemo, useState } from "react";

interface SelectVoiceOfCustomerModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (selectedData: VoiceOfCustomerData[]) => void;
  title?: string;
  subTypeId: number; // Required subtype ID for filtering (NPS, CES, CSAT)
  existingBuildingBlocks?: any[]; // Building blocks already added to the map
}

interface VoiceOfCustomerData extends BuildingBlock {
  content?: {
    age?: number;
    date?: string;
    brand?: string;
    channel?: string;
    s_score?: number;
    segment?: string;
    raw_score?: number;
    sentiment?: string;
    customerid?: number;
  };
}

const SelectVoiceOfCustomerModal: React.FC<SelectVoiceOfCustomerModalProps> = ({
  open,
  onClose,
  onConfirm,
  title = "Select Voice of Customer data",
  subTypeId,
  existingBuildingBlocks = [],
}) => {
  const { commonData } = useAppContext();
  const [voiceOfCustomerData, setVoiceOfCustomerData] = useState<VoiceOfCustomerData[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedBrand, setSelectedBrand] = useState("All");
  const [selectedSegment, setSelectedSegment] = useState("All");
  const [relevantSearch, setRelevantSearch] = useState(true);
  const [selectedData, setSelectedData] = useState<Set<number>>(new Set());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Fetch Voice of Customer data
  const fetchVoiceOfCustomerData = useCallback(async () => {
    if (!open || !subTypeId) return;

    try {
      setLoading(true);
      // Fetch Voice of Customer data (typeId: 4, subTypeId: provided)
      const data = await buildingBlockService.getBuildingBlocks(4, subTypeId);
      setVoiceOfCustomerData(data);
    } catch (error) {
      console.error("Failed to fetch Voice of Customer data:", error);
    } finally {
      setLoading(false);
    }
  }, [open, subTypeId]);

  useEffect(() => {
    fetchVoiceOfCustomerData();
  }, [fetchVoiceOfCustomerData]);

  // Filter data based on search and filters
  const filteredData = useMemo(() => {
    let filtered = voiceOfCustomerData;

    // Filter out already added building blocks
    const existingIds = new Set(existingBuildingBlocks.map((block) => block.id));
    filtered = filtered.filter((item) => !existingIds.has(item.id));

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (item) =>
          item.name?.toLowerCase().includes(query) || item.desc?.toLowerCase().includes(query)
      );
    }

    // Category filter (sentiment)
    if (selectedCategory !== "All") {
      filtered = filtered.filter((item) => item.content?.sentiment === selectedCategory);
    }

    // Brand filter
    if (selectedBrand !== "All") {
      filtered = filtered.filter((item) => item.content?.brand === selectedBrand);
    }

    // Segment filter
    if (selectedSegment !== "All") {
      filtered = filtered.filter((item) => item.content?.segment === selectedSegment);
    }

    return filtered;
  }, [
    voiceOfCustomerData,
    searchQuery,
    selectedCategory,
    selectedBrand,
    selectedSegment,
    existingBuildingBlocks,
  ]);

  // Paginated data
  const paginatedData = useMemo(() => {
    const startIndex = page * rowsPerPage;
    return filteredData.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredData, page, rowsPerPage]);

  // Handle checkbox selection
  const handleSelectData = (dataId: number) => {
    const newSelected = new Set(selectedData);
    if (newSelected.has(dataId)) {
      newSelected.delete(dataId);
    } else {
      newSelected.add(dataId);
    }
    setSelectedData(newSelected);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedData.size === paginatedData.length) {
      setSelectedData(new Set());
    } else {
      const allIds = new Set(paginatedData.map((item) => item.id));
      setSelectedData(allIds);
    }
  };

  // Handle confirm
  const handleConfirm = () => {
    const selectedVoiceOfCustomerData = voiceOfCustomerData.filter((item) =>
      selectedData.has(item.id)
    );
    onConfirm?.(selectedVoiceOfCustomerData);
    onClose();
  };

  // Handle cancel
  const handleCancel = () => {
    setSelectedData(new Set());
    onClose();
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      setSearchQuery("");
      setSelectedCategory("All");
      setSelectedBrand("All");
      setSelectedSegment("All");
      setSelectedData(new Set());
      setPage(0);
    }
  }, [open]);

  // Get unique category options (sentiment values)
  const categoryOptions = useMemo(() => {
    const options = ["All"];
    const sentiments = new Set(
      voiceOfCustomerData.map((item) => item.content?.sentiment).filter((sentiment) => sentiment)
    );
    options.push(...Array.from(sentiments));
    return options;
  }, [voiceOfCustomerData]);

  // Get unique brand options
  const brandOptions = useMemo(() => {
    const options = ["All"];
    const brands = new Set(
      voiceOfCustomerData.map((item) => item.content?.brand).filter((brand) => brand)
    );
    options.push(...Array.from(brands));
    return options;
  }, [voiceOfCustomerData]);

  // Get segment options from common data
  const segmentOptions = useMemo(() => {
    const options = ["All"];
    if (commonData.segments) {
      options.push(...commonData.segments.map((segment) => segment.name));
    }
    return options;
  }, [commonData.segments]);

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  // Capitalize first letter
  const capitalize = (str?: string) => {
    if (!str) return "";
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  return (
    <Modal
      open={open}
      onClose={onClose}
      title={title}
      maxWidth={1200}
      headerBackgroundColor="transparent"
    >
      <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
        {/* Filters */}
        <Stack direction="row" spacing={2} alignItems="center">
          {/* Search */}
          <TextField
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
            sx={{ minWidth: 300 }}
          />

          {/* Category Filter (Sentiment) */}
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Category</InputLabel>
            <Select
              value={selectedCategory}
              label="Category"
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              {categoryOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {capitalize(option)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Brand Filter */}
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Brand</InputLabel>
            <Select
              value={selectedBrand}
              label="Brand"
              onChange={(e) => setSelectedBrand(e.target.value)}
            >
              {brandOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Segment Filter */}
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Segment</InputLabel>
            <Select
              value={selectedSegment}
              label="Segment"
              onChange={(e) => setSelectedSegment(e.target.value)}
            >
              {segmentOptions.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Relevant Search Checkbox */}
          <FormControlLabel
            control={
              <Checkbox
                checked={relevantSearch}
                onChange={(e) => setRelevantSearch(e.target.checked)}
                sx={{
                  color: "grey.500",
                  "&.Mui-checked": {
                    color: "primary.main",
                  },
                }}
              />
            }
            label="Relevant search"
          />
        </Stack>

        {/* Data Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={
                      selectedData.size > 0 && selectedData.size < paginatedData.length
                    }
                    checked={paginatedData.length > 0 && selectedData.size === paginatedData.length}
                    onChange={handleSelectAll}
                    sx={{
                      color: "grey.500",
                      "&.Mui-checked": {
                        color: "primary.main",
                      },
                      "&.MuiCheckbox-indeterminate": {
                        color: "primary.main",
                      },
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Timestamp
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Sentiment
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="subtitle2" fontWeight="bold">
                    Description
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography>Loading...</Typography>
                  </TableCell>
                </TableRow>
              ) : paginatedData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography color="text.secondary">No data available</Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedData.map((item) => (
                  <TableRow
                    key={item.id}
                    hover
                    onClick={() => handleSelectData(item.id)}
                    sx={{ cursor: "pointer" }}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedData.has(item.id)}
                        sx={{
                          color: "grey.500",
                          "&.Mui-checked": {
                            color: "primary.main",
                          },
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{formatDate(item.content?.date)}</Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={capitalize(item.content?.sentiment)}
                        color={
                          item.content?.sentiment === "promoters"
                            ? "success"
                            : item.content?.sentiment === "detractors"
                            ? "error"
                            : item.content?.sentiment === "passives"
                            ? "warning"
                            : "default"
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{item.desc}</Typography>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25]}
                  colSpan={4}
                  count={filteredData.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={(_, newPage) => setPage(newPage)}
                  onRowsPerPageChange={(event) => {
                    setRowsPerPage(parseInt(event.target.value, 10));
                    setPage(0);
                  }}
                />
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>

        {/* Action Buttons */}
        <Stack direction="row" spacing={2} justifyContent="flex-end">
          <Button variant="outlined" onClick={handleCancel}>
            CANCEL
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirm}
            disabled={selectedData.size === 0}
            sx={{
              backgroundColor: "#c8ff00",
              color: "#000000",
              fontWeight: "bold",
              "&:hover": {
                backgroundColor: "#b3e600",
              },
              "&:disabled": {
                backgroundColor: "grey.300",
                color: "grey.500",
              },
            }}
          >
            CONFIRM
          </Button>
        </Stack>
      </Box>
    </Modal>
  );
};

export default SelectVoiceOfCustomerModal;

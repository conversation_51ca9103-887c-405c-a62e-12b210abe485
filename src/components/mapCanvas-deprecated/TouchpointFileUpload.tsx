"use client";

import { useState } from "react";
import { useDropzone } from "react-dropzone";
import { Box, Link, Typography, IconButton } from "@mui/material";
import UploadIcon from "@mui/icons-material/UploadFile";
import DeleteIcon from "@mui/icons-material/Delete";

interface TouchpointFileUploadProps {
  onUpload?: (file: File) => Promise<any>;
  onFileSelect?: (file: File) => void;
  onFileDelete?: () => void;
  uploadedImage?: string | null;
}

interface UploadedFile {
  file: File;
  name: string;
  size: number;
  preview: string;
}

export default function TouchpointFileUpload({
  onUpload,
  onFileSelect,
  onFileDelete,
  uploadedImage,
}: TouchpointFileUploadProps) {
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);

  // Handle file upload logic
  const onDrop = async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0]; // Only take the first file
    const preview = URL.createObjectURL(file);
    
    const newFile: UploadedFile = {
      file,
      name: file.name,
      size: file.size,
      preview,
    };

    setUploadedFile(newFile);

    if (onUpload) {
      try {
        await onUpload(file);
      } catch (error) {
        console.error("Upload failed:", error);
      }
    }

    if (onFileSelect) {
      onFileSelect(file);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 
      'image/png': ['.png'], 
      'image/jpeg': ['.jpg', '.jpeg'] 
    },
    maxFiles: 1,
    maxSize: 3145728, // 3MB
  });

  const handleDelete = () => {
    if (uploadedFile) {
      URL.revokeObjectURL(uploadedFile.preview);
    }
    setUploadedFile(null);
    if (onFileDelete) {
      onFileDelete();
    }
  };

  // Show uploaded image if available
  const imageToShow = uploadedFile?.preview || uploadedImage;

  return (
    <Box>
      {/* File Upload Dropzone - Hidden when an image is uploaded */}
      {!imageToShow ? (
        <Box
          {...getRootProps()}
          sx={{
            border: '1px dashed rgba(255, 255, 255, 0.3)',
            borderRadius: '8px',
            padding: '40px 20px',
            textAlign: 'center',
            cursor: 'pointer',
            backgroundColor: 'rgba(255, 255, 255, 0.02)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
            },
          }}
        >
          <input {...getInputProps()} />
          <UploadIcon sx={{ color: '#2196f3', fontSize: 32, mb: 2 }} />
          {isDragActive ? (
            <Typography variant="body1" sx={{ color: 'white', mb: 1 }}>
              Drop the files here...
            </Typography>
          ) : (
            <Typography variant="body1" sx={{ color: 'white', mb: 1 }}>
              <Link sx={{ color: '#2196f3', textDecoration: 'underline', cursor: 'pointer' }}>
                Click to upload
              </Link>{' '}
              or drag and drop
            </Typography>
          )}
          <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            .png or .jpeg (max. 3MB)
          </Typography>
        </Box>
      ) : (
        /* Uploaded Image Display */
        <Box sx={{ position: 'relative' }}>
          <Box
            component="img"
            src={imageToShow}
            alt="Uploaded reference"
            sx={{
              width: '100%',
              height: 'auto',
              maxHeight: '400px',
              borderRadius: '8px',
              objectFit: 'contain',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            }}
          />
          <IconButton
            onClick={handleDelete}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
              },
            }}
            size="small"
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Box>
      )}
    </Box>
  );
}

// import {
//   getFormFields as getDataFormFields,
//   prepareFormData,
//   processFormValues,
// } from "@/app/data/forms/DataForm";
// import SelectInsightModal from "@/app/journeys/views/SelectInsightModal";
// import DataCreationModal from "@/components/DataCreationModal";
// import { useAppContext } from "@/context/AppProvider";
// import { useMapCanvas } from "@/context/MapCanvasProvider";
// import buildingBlockService from "@/services/BuildingBlockService";
// import mapService from "@/services/MapService";
// import { BuildingBlockTypeDetail } from "@/types/buildingBlock";
// import { BuildingBlock } from "@/types/map";
// import { useDroppable } from "@dnd-kit/core";
// import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
// import { Box, Button, Card, Menu, MenuItem } from "@mui/material";
// import dayjs from "dayjs";
// import React, { useState } from "react";
// import BuildingBlockCard from "./BuildingBlockCard";
// import SelectBuildingBlockModal from "./SelectBuildingBlockModal";
// import SelectVoiceOfCustomerModal from "./SelectVoiceOfCustomerModal";

// // BuildingBlockCardWrapper component that selects the appropriate card based on type
// const BuildingBlockCardWrapper: React.FC<{
//   buildingBlock: BuildingBlock;
//   typeName: string;
//   onEdit?: (newText: string) => void;
//   onDelete?: () => void;
// }> = ({ buildingBlock, typeName, onEdit, onDelete }) => {
//   // Check if this is a Painpoint or Opportunity (use bordered variant)
//   if (typeName === "Painpoints" || typeName === "Opportunity") {
//     return (
//       <BuildingBlockCard
//         buildingBlock={buildingBlock}
//         type={typeName as "Painpoints" | "Opportunity"}
//         variant="bordered"
//         onEdit={onEdit}
//         onDelete={onDelete}
//       />
//     );
//   }

//   // Check if this is a Voice of Customer subtype (NPS, CES, CSAT)
//   if (typeName === "NPS" || typeName === "CES" || typeName === "CSAT") {
//     return (
//       <BuildingBlockCard
//         buildingBlock={buildingBlock}
//         type={typeName as "NPS" | "CES" | "CSAT"}
//         variant="default"
//         onEdit={onEdit}
//         onDelete={onDelete}
//       />
//     );
//   }

//   // For Operation context and other types, use default variant (no borders)
//   return (
//     <BuildingBlockCard
//       buildingBlock={buildingBlock}
//       type="Operation context"
//       variant="default"
//       onEdit={onEdit}
//       onDelete={onDelete}
//     />
//   );
// };

// type MapBuildingBlockCellProps = {
//   phaseId: number;
//   type: string;
//   typeId: number;
//   buildingBlocks: BuildingBlock[];
//   height: number;
//   isPanningDisabled: boolean;
//   onEditBuildingBlock: (id: number, updatedText: string) => void;
//   onDeleteBuildingBlock: (id: number) => void;
//   onAddBuildingBlock: () => void;
//   onViewExistingData?: (insights: any[]) => void;
//   onBuildingBlockCreated?: (buildingBlock: BuildingBlock) => void; // Add callback for new building block creation
//   mapId?: string; // Add map ID prop
//   transformValues: {
//     scale: number;
//     positionX: number;
//     positionY: number;
//   };
// };

// const MapBuildingBlockCell: React.FC<MapBuildingBlockCellProps> = ({
//   phaseId,
//   type: typeName, // Used for determining the card type
//   typeId,
//   buildingBlocks,
//   height,
//   // isPanningDisabled,
//   onEditBuildingBlock,
//   onDeleteBuildingBlock,
//   onAddBuildingBlock,
//   onViewExistingData,
//   onBuildingBlockCreated,
//   mapId,
//   transformValues,
// }) => {
//   const { commonData } = useAppContext();
//   const { state } = useMapCanvas();
//   const [isHovering, setIsHovering] = useState(false);
//   const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
//   const [selectInsightModalOpen, setSelectInsightModalOpen] = useState(false);
//   const [selectBuildingBlockModalOpen, setSelectBuildingBlockModalOpen] = useState(false);
//   const [selectVoiceOfCustomerModalOpen, setSelectVoiceOfCustomerModalOpen] = useState(false);
//   const [dataCreationModalOpen, setDataCreationModalOpen] = useState(false);
//   const [buildingBlockType, setBuildingBlockType] = useState<BuildingBlockTypeDetail | null>(null);
//   const [formGroups, setFormGroups] = useState<any[]>([]);
//   const [selectedSubType, setSelectedSubType] = useState<number | null>(null);
//   const cellId = `cell-block-${phaseId}-${typeId}`;
//   const open = Boolean(anchorEl);

//   const { setNodeRef, isOver } = useDroppable({
//     id: cellId,
//     data: {
//       type: "buildingBlock",
//     },
//     disabled: false,
//   });

//   const handleMouseEnter = () => {
//     setIsHovering(true);
//   };

//   const handleMouseLeave = () => {
//     setIsHovering(false);
//   };

//   const handleImportClick = (event: React.MouseEvent<HTMLButtonElement>) => {
//     setAnchorEl(event.currentTarget);
//   };

//   const handleClose = () => {
//     setAnchorEl(null);
//   };

//   const handleViewExistingData = () => {
//     // Check if this is an Insights type (painpoints or opportunities)
//     const buildingBlockType = commonData.building_block_types.find((type) => type.id === typeId);
//     if (buildingBlockType && buildingBlockType.name === "Insights") {
//       setSelectInsightModalOpen(true);
//     } else if (buildingBlockType && buildingBlockType.name === "Operation context") {
//       setSelectBuildingBlockModalOpen(true);
//     } else if (buildingBlockType && buildingBlockType.name === "Voice of customer") {
//       setSelectBuildingBlockModalOpen(true);
//     }
//     handleClose();
//   };

//   const handleInsightModalClose = () => {
//     setSelectInsightModalOpen(false);
//   };

//   const handleInsightConfirm = (selectedInsights: any[]) => {
//     if (onViewExistingData) {
//       onViewExistingData(selectedInsights);
//     }
//     setSelectInsightModalOpen(false);
//   };

//   const handleBuildingBlockModalClose = () => {
//     setSelectBuildingBlockModalOpen(false);
//     setSelectedSubType(null); // Reset selected subtype when modal closes
//   };

//   const handleBuildingBlockConfirm = (selectedBlocks: any[]) => {
//     if (onViewExistingData) {
//       onViewExistingData(selectedBlocks);
//     }
//     setSelectBuildingBlockModalOpen(false);
//   };

//   // Handle Voice of Customer subtype selection - opens modal filtered by subtype
//   const handleViewVoiceOfCustomerData = (subTypeId: number) => {
//     setSelectedSubType(subTypeId);
//     setSelectVoiceOfCustomerModalOpen(true);
//     handleClose();
//   };

//   const handleVoiceOfCustomerModalClose = () => {
//     setSelectVoiceOfCustomerModalOpen(false);
//     setSelectedSubType(null); // Reset selected subtype when modal closes
//   };

//   const handleVoiceOfCustomerConfirm = (selectedData: any[]) => {
//     if (onViewExistingData) {
//       onViewExistingData(selectedData);
//     }
//     setSelectVoiceOfCustomerModalOpen(false);
//   };

//   // Determine the insight type for the modal
//   const getInsightType = () => {
//     if (typeName === "Painpoints") return "painpoint";
//     if (typeName === "Opportunity") return "opportunity";
//     return "both";
//   };

//   // Helper function to determine button text based on building block type
//   const getButtonText = () => {
//     const buildingBlockType = commonData.building_block_types.find((type) => type.id === typeId);
//     if (buildingBlockType && buildingBlockType.name === "Voice of customer") {
//       return "IMPORT";
//     }
//     return "CREATE";
//   };

//   // Helper function to get Voice of Customer subtypes for dropdown
//   const getVoiceOfCustomerSubtypes = () => {
//     const buildingBlockType = commonData.building_block_types.find((type) => type.id === typeId);
//     if (
//       buildingBlockType &&
//       buildingBlockType.name === "Voice of customer" &&
//       (buildingBlockType as any).sub_types
//     ) {
//       return (buildingBlockType as any).sub_types || [];
//     }
//     return [];
//   };

//   // Helper function to check if this is Voice of Customer type
//   const isVoiceOfCustomerType = () => {
//     const buildingBlockType = commonData.building_block_types.find((type) => type.id === typeId);
//     return buildingBlockType && buildingBlockType.name === "Voice of customer";
//   };

//   // Helper function to get subtype name by ID
//   const getSubTypeName = (subTypeId: number) => {
//     const buildingBlockType = commonData.building_block_types.find((type) => type.id === typeId);
//     if (buildingBlockType && (buildingBlockType as any).sub_types) {
//       const subType = (buildingBlockType as any).sub_types.find((st: any) => st.id === subTypeId);
//       return subType ? subType.name : "";
//     }
//     return "";
//   };

//   // Helper function to convert date strings to dayjs objects for form fields
//   const convertDatesToDayjs = (
//     data: Record<string, any>,
//     buildingBlockType: BuildingBlockTypeDetail | null
//   ) => {
//     if (!buildingBlockType?.fields?.fields) return data;

//     const convertedData = { ...data };

//     // Find date fields in the building block type definition
//     buildingBlockType.fields.fields.forEach((field: any) => {
//       if (field.type === "date" && convertedData[field.name]) {
//         const dateValue = convertedData[field.name];
//         // Convert string dates to dayjs objects
//         if (typeof dateValue === "string" && dateValue.trim() !== "") {
//           try {
//             // Try to create a dayjs object from the string
//             const dayjsDate = dayjs(dateValue);
//             // Only use the dayjs object if it's valid
//             if (dayjsDate && typeof dayjsDate.isValid === "function" && dayjsDate.isValid()) {
//               convertedData[field.name] = dayjsDate;
//             } else {
//               console.warn(`Invalid date value for field ${field.name}:`, dateValue);
//             }
//           } catch (error) {
//             console.warn(`Error converting date for field ${field.name}:`, error);
//           }
//         }
//       }
//     });

//     return convertedData;
//   };

//   // Handle opening the data creation modal
//   const handleAddNewData = async () => {
//     try {
//       // Fetch building block type details
//       const typeData = await buildingBlockService.getBuildingBlockTypeDetail(typeId);
//       setBuildingBlockType(typeData);

//       // Generate form groups
//       const formGroups = getDataFormFields(typeData, commonData);
//       setFormGroups(formGroups);

//       // Set default sub-type
//       const defaultSubType = typeData.sub_types.length > 0 ? typeData.sub_types[0].id : null;
//       setSelectedSubType(defaultSubType);

//       // Open the modal
//       setDataCreationModalOpen(true);
//     } catch (error) {
//       console.error("Error loading building block type details:", error);
//     }
//     handleClose();
//   };

//   // Handle form submission for creating new building block
//   const handleFormSubmit = async (values: any) => {
//     try {
//       if (!buildingBlockType) return;

//       // Process form values to handle date objects
//       const processedValues = processFormValues(values);

//       // Prepare form data for submission
//       const formData = prepareFormData(processedValues, buildingBlockType, selectedSubType);

//       // Create building block data for map API
//       const buildingBlockData = {
//         type: typeId,
//         sub_type: formData.sub_type,
//         phase: phaseId,
//         name: formData.name,
//         desc: formData.desc,
//         order: 100,
//         content: processedValues, // Include all custom fields in content
//       };

//       // Call the map service to create the building block
//       if (!mapId) {
//         console.error("Map ID is required to create building block");
//         return;
//       }
//       const createdBuildingBlock = await mapService.postBuildingBlock(buildingBlockData, mapId);

//       // Close the modal
//       setDataCreationModalOpen(false);

//       // Call the callback to update the parent component's state
//       if (onBuildingBlockCreated && createdBuildingBlock) {
//         // Create a building block object with the necessary structure for immediate display
//         const buildingBlockForDisplay = {
//           ...createdBuildingBlock,
//           phase: phaseId,
//           type: buildingBlockType.id,
//           sub_type: selectedSubType,
//           // Include type and sub_type objects for proper rendering
//           typeObject: buildingBlockType,
//           subTypeObject: buildingBlockType.sub_types.find((st) => st.id === selectedSubType),
//         };
//         onBuildingBlockCreated(buildingBlockForDisplay);
//       }
//     } catch (error) {
//       console.error("Error creating building block:", error);
//     }
//   };

//   return (
//     <Card
//       ref={setNodeRef}
//       onMouseEnter={handleMouseEnter}
//       onMouseLeave={handleMouseLeave}
//       sx={{
//         width: "60rem",
//         my: 2,
//         p: 2,
//         height: `${height}rem`,
//         bgcolor: isOver ? "action.hover" : "map.cell",
//         display: "grid",
//         gridTemplateColumns: "repeat(3, 1fr)",
//         gap: "1rem",
//         position: "relative",
//         transition: "background-color 0.2s",
//       }}
//     >
//       {buildingBlocks.map((block) => {
//         // Extract type and sub_type IDs from block (handle both number and object types)
//         const typeId = typeof block.type === "object" ? block.type.id : block.type;
//         const subTypeId = typeof block.sub_type === "object" ? block.sub_type.id : block.sub_type;

//         // Determine the correct typeName for this block
//         let blockTypeName = typeName;

//         // Special handling for Insights type (type 3)
//         if (typeId === 3 && subTypeId) {
//           // First check if we have the subtype object directly
//           if (typeof block.sub_type === "object" && block.sub_type.name) {
//             blockTypeName = block.sub_type.name;
//           } else {
//             // Fallback to finding in commonData
//             const insightsType = commonData.building_block_types.find((type) => type.id === 3);
//             if (insightsType && (insightsType as any).sub_types) {
//               const subType = (insightsType as any).sub_types.find(
//                 (subType: any) => subType.id === subTypeId
//               );
//               if (subType) {
//                 blockTypeName = subType.name;
//               }
//             }
//           }
//         }

//         // Special handling for Voice of Customer type (type 4)
//         // Keep the building blocks showing their subtype names for proper card rendering
//         if (typeId === 4 && subTypeId) {
//           // First check if we have the subtype object directly
//           if (typeof block.sub_type === "object" && block.sub_type.name) {
//             blockTypeName = block.sub_type.name;
//           } else {
//             // Fallback to finding in commonData
//             const voiceOfCustomerType = commonData.building_block_types.find(
//               (type) => type.id === 4
//             );
//             if (voiceOfCustomerType && (voiceOfCustomerType as any).sub_types) {
//               const subType = (voiceOfCustomerType as any).sub_types.find(
//                 (subType: any) => subType.id === subTypeId
//               );
//               if (subType) {
//                 blockTypeName = subType.name;
//               }
//             }
//           }
//         }

//         // Use connection_id for delete operation if available, otherwise fall back to id
//         const connectionId = block.connection_id || block.id;

//         return (
//           <BuildingBlockCardWrapper
//             key={block.id}
//             buildingBlock={block}
//             typeName={blockTypeName}
//             onEdit={(newText) => onEditBuildingBlock(block.id, newText)}
//             onDelete={() => onDeleteBuildingBlock(connectionId)}
//           />
//         );
//       })}
//       {/* Import Building Block Button - only visible on hover */}
//       <Box sx={{ position: "absolute", bottom: 8, right: 8 }}>
//         <Button
//           variant="contained"
//           endIcon={<KeyboardArrowDownIcon />}
//           onClick={handleImportClick}
//           sx={{
//             backgroundColor: "#c8ff00",
//             color: "#000000",
//             fontWeight: "bold",
//             opacity: isHovering ? 1 : 0,
//             transition: "opacity 0.2s ease-in-out",
//             "&:hover": {
//               backgroundColor: "#b3e600",
//             },
//           }}
//         >
//           {getButtonText()}
//         </Button>
//         <Menu
//           anchorEl={anchorEl}
//           open={open}
//           onClose={handleClose}
//           anchorOrigin={{
//             vertical: "top",
//             horizontal: "left",
//           }}
//           transformOrigin={{
//             vertical: "bottom",
//             horizontal: "left",
//           }}
//         >
//           {isVoiceOfCustomerType()
//             ? [
//                 // Voice of Customer specific menu items - only show subtypes
//                 ...getVoiceOfCustomerSubtypes().map((subType: any) => (
//                   <MenuItem
//                     key={subType.id}
//                     onClick={() => {
//                       setSelectedSubType(subType.id);
//                       handleViewVoiceOfCustomerData(subType.id);
//                     }}
//                   >
//                     {subType.name}
//                   </MenuItem>
//                 )),
//               ]
//             : [
//                 // Default menu items for other building block types
//                 <MenuItem key="view-existing" onClick={handleViewExistingData}>
//                   View existing data
//                 </MenuItem>,
//                 <MenuItem key="add-new" onClick={handleAddNewData}>
//                   Add a new
//                 </MenuItem>,
//               ]}
//         </Menu>
//       </Box>

//       {/* Select Insight Modal */}
//       <SelectInsightModal
//         open={selectInsightModalOpen}
//         onClose={handleInsightModalClose}
//         onConfirm={handleInsightConfirm}
//         title={`Select ${typeName.toLowerCase()}`}
//         type={getInsightType() as "painpoint" | "opportunity" | "both"}
//         existingBuildingBlocks={state.buildingBlocks}
//       />

//       {/* Select Building Block Modal */}
//       <SelectBuildingBlockModal
//         open={selectBuildingBlockModalOpen}
//         onClose={handleBuildingBlockModalClose}
//         onConfirm={handleBuildingBlockConfirm}
//         title={
//           selectedSubType
//             ? `Select ${getSubTypeName(selectedSubType)} data`
//             : `Select ${typeName.toLowerCase()}`
//         }
//         typeId={typeId}
//         typeName={typeName}
//         subTypeId={selectedSubType || undefined}
//         existingBuildingBlocks={state.buildingBlocks}
//       />

//       {/* Select Voice of Customer Modal */}
//       {selectedSubType && (
//         <SelectVoiceOfCustomerModal
//           open={selectVoiceOfCustomerModalOpen}
//           onClose={handleVoiceOfCustomerModalClose}
//           onConfirm={handleVoiceOfCustomerConfirm}
//           title={`Select ${getSubTypeName(selectedSubType)} data`}
//           subTypeId={selectedSubType}
//           existingBuildingBlocks={state.buildingBlocks}
//         />
//       )}

//       {/* Data Creation Modal */}
//       {buildingBlockType && (
//         <DataCreationModal
//           open={dataCreationModalOpen}
//           onClose={() => setDataCreationModalOpen(false)}
//           buildingBlockType={buildingBlockType}
//           formGroups={formGroups}
//           isEditMode={false}
//           editingBlock={null}
//           selectedSubType={selectedSubType}
//           onSubmit={handleFormSubmit}
//           onEditSubmit={handleFormSubmit} // Not used in create mode
//           convertDatesToDayjs={convertDatesToDayjs}
//         />
//       )}
//     </Card>
//   );
// };

// export default MapBuildingBlockCell;

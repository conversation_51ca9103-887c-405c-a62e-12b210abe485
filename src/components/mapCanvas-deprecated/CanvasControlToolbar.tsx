import { BuildingBlockType, RoleType } from "@/types/common";
import AddIcon from "@mui/icons-material/Add";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import CropFreeIcon from "@mui/icons-material/CropFree";
import PanToolIcon from "@mui/icons-material/PanTool";
import RemoveIcon from "@mui/icons-material/Remove";
import TuneIcon from "@mui/icons-material/Tune";
import { Box, FormControlLabel, IconButton, Menu, MenuItem, Switch } from "@mui/material";
import React, { useState } from "react";

type CanvasControlToolbarProps = {
  onReset: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onTogglePanning: () => void;
  isPanningDisabled: boolean;
  toggleStoryMode?: () => void;
  isStoryModeOn?: boolean;
  roleTypes: RoleType[];
  visibleRoles: string[];
  onRoleToggle: (roleType: string) => void;
  buildingBlockTypes: BuildingBlockType[];
  visibleBuildingBlockTypes: string[];
  onBuildingBlockTypeToggle: (buildingBlockType: string) => void;
};

const CanvasControlToolbar: React.FC<CanvasControlToolbarProps> = ({
  onReset,
  onZoomIn,
  onZoomOut,
  onTogglePanning,
  isPanningDisabled,
  toggleStoryMode,
  isStoryModeOn = false,
  roleTypes,
  visibleRoles,
  onRoleToggle,
  buildingBlockTypes,
  visibleBuildingBlockTypes,
  onBuildingBlockTypeToggle,
}) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [actionMenuAnchorEl, setActionMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [blockMenuAnchorEl, setBlockMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [isStoryView, setIsStoryView] = useState(isStoryModeOn);

  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleCloseAllMenus = () => {
    setMenuAnchorEl(null);
    setActionMenuAnchorEl(null);
    setBlockMenuAnchorEl(null);
  };

  const handleCloseSubMenus = () => {
    setActionMenuAnchorEl(null);
    setBlockMenuAnchorEl(null);
  };

  const handleToggleSwitch = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation();
    setIsStoryView(!isStoryView);
    toggleStoryMode?.();
    handleCloseSubMenus();
  };

  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setActionMenuAnchorEl(event.currentTarget);
    setBlockMenuAnchorEl(null);
  };

  const handleBlockMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setBlockMenuAnchorEl(event.currentTarget);
    setActionMenuAnchorEl(null);
  };

  const handleRoleToggle = (role: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation();
    onRoleToggle(role);
  };

  const handleBuildingBlockTypeToggle =
    (buildingBlockType: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
      event.stopPropagation();
      onBuildingBlockTypeToggle(buildingBlockType);
    };

  return (
    <Box
      sx={{
        zIndex: 1,
        position: "fixed",
        left: "calc(var(--sidebar-width, 0px) + 30px)",
        top: "20%",
        display: "flex",
        flexDirection: "column",
        gap: 1,
        backgroundColor: "background.paper",
        border: "1px solid",
        borderColor: "divider",
        padding: 1,
        borderRadius: 2,
        transition: "left 0.2s",
      }}
    >
      <IconButton onClick={onReset} color="primary">
        <CropFreeIcon />
      </IconButton>
      <IconButton onClick={onTogglePanning} color="primary">
        <PanToolIcon color={isPanningDisabled ? "inherit" : "action"} />
      </IconButton>
      <IconButton onClick={onZoomIn} color="primary">
        <AddIcon />
      </IconButton>
      <IconButton onClick={onZoomOut} color="primary">
        <RemoveIcon />
      </IconButton>
      <IconButton onClick={handleOpenMenu} color="primary">
        <TuneIcon />
      </IconButton>

      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleCloseAllMenus}
        anchorOrigin={{
          vertical: "center",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "left",
        }}
      >
        <MenuItem onClick={(e) => e.stopPropagation()}>
          <FormControlLabel
            control={
              <Switch
                checked={isStoryView}
                onChange={handleToggleSwitch}
                size="small"
                color="primary"
              />
            }
            label="Story Mode"
            labelPlacement="start"
            sx={{ margin: 0, justifyContent: "space-between", width: "100%" }}
          />
        </MenuItem>
        <MenuItem
          onClick={handleActionMenuOpen}
          sx={{ display: "flex", justifyContent: "space-between" }}
        >
          Action swim lane
          <ChevronRightIcon />
        </MenuItem>
        <MenuItem
          onClick={handleBlockMenuOpen}
          sx={{ display: "flex", justifyContent: "space-between" }}
        >
          Building blocks
          <ChevronRightIcon />
        </MenuItem>
      </Menu>

      <Menu
        anchorEl={actionMenuAnchorEl}
        open={Boolean(actionMenuAnchorEl)}
        onClose={handleCloseSubMenus}
        anchorOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        {roleTypes.map((roleType) => (
          <MenuItem key={roleType.id} onClick={(e) => e.stopPropagation()}>
            <FormControlLabel
              control={
                <Switch
                  checked={visibleRoles.includes(roleType.name)}
                  onChange={handleRoleToggle(roleType.name)}
                  size="small"
                  color="primary"
                />
              }
              label={roleType.name}
              labelPlacement="start"
              sx={{ margin: 0, justifyContent: "space-between", width: "100%" }}
            />
          </MenuItem>
        ))}
      </Menu>

      <Menu
        anchorEl={blockMenuAnchorEl}
        open={Boolean(blockMenuAnchorEl)}
        onClose={handleCloseSubMenus}
        anchorOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        {buildingBlockTypes.map((buildingBlockType) => (
          <MenuItem key={buildingBlockType.id} onClick={(e) => e.stopPropagation()}>
            <FormControlLabel
              control={
                <Switch
                  checked={visibleBuildingBlockTypes.includes(buildingBlockType.name)}
                  onChange={handleBuildingBlockTypeToggle(buildingBlockType.name)}
                  size="small"
                  color="primary"
                />
              }
              label={buildingBlockType.name}
              labelPlacement="start"
              sx={{ margin: 0, justifyContent: "space-between", width: "100%" }}
            />
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};

export default CanvasControlToolbar;

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material";
import React, { useState } from "react";
import { Persona } from "@/types/api/client";

interface SelectPersonaModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (persona: Persona) => void;
  roleType?: string;
  mapId?: number | string;
  personas?: Persona[];
}

const SelectPersonaModal: React.FC<SelectPersonaModalProps> = ({
  open,
  onClose,
  onConfirm,
  roleType = "Customer",
  personas = [],
}) => {
  const [selectedPersona, setSelectedPersona] = useState<Persona | null>(null);

  const handlePersonaSelect = (persona: Persona) => {
    setSelectedPersona(persona);
  };

  const handleConfirm = () => {
    if (selectedPersona && onConfirm) {
      onConfirm(selectedPersona);
    }
    onClose();
  };

  return (
    <Modal open={open} onClose={onClose} aria-labelledby="select-persona-modal">
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 2,
          width: "90%",
          maxWidth: 1000,
          maxHeight: "90vh",
          overflow: "auto",
          outline: "none",
          p: 0,
        }}
      >
        <Box sx={{ p: 3, borderBottom: 1, borderColor: "divider" }}>
          <Typography variant="h5">Select a persona ({roleType})</Typography>
        </Box>

        <Stack direction={{ xs: "column", md: "row" }} sx={{ height: "100%" }}>
          {/* Left side - Persona selection */}
          <Box
            sx={{
              flex: 7,
              borderRight: "1px solid",
              borderBottom: "1px solid",
              borderColor: "divider",
              p: 3,
            }}
          >
            <Stack spacing={3}>
              <Stack direction="row" flexWrap="wrap" spacing={2} useFlexGap>
                {personas.map((persona) => (
                  <Box
                    key={persona.persona_id}
                    onClick={() => handlePersonaSelect(persona)}
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      cursor: "pointer",
                      p: 1,
                      borderRadius: 1,
                      width: { xs: "120px", sm: "140px" },
                      bgcolor:
                        selectedPersona?.persona_id === persona.persona_id
                          ? "action.selected"
                          : "transparent",
                      "&:hover": {
                        bgcolor: "action.hover",
                      },
                    }}
                  >
                    <Avatar
                      src={persona.image_url || ""}
                      alt={persona.name}
                      sx={{ width: 80, height: 80, mb: 1 }}
                    />
                    <Typography 
                      align="center" 
                      variant="body2" 
                      sx={{ 
                        wordWrap: "break-word",
                        hyphens: "auto",
                        lineHeight: 1.2,
                        minHeight: "2.4em"
                      }}
                    >
                      {persona.name}
                    </Typography>
                  </Box>
                ))}
              </Stack>
            </Stack>
          </Box>

          {/* Right side - Persona details */}
          <Box sx={{ flex: 5, p: 3 }}>
            {selectedPersona ? (
              <Stack spacing={3}>
                <Typography variant="h6">Profile</Typography>

                <Stack spacing={2}>
                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Age:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.age || "Not specified"}
                    </Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Role:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.role || "Not specified"}
                    </Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Status:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.status || "Not specified"}
                    </Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Location:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.location || "Not specified"}
                    </Typography>
                  </Stack>

                  <Stack direction="row" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Persona Type:
                    </Typography>
                    <Typography variant="body1">
                      {selectedPersona.persona_type || "Not specified"}
                    </Typography>
                  </Stack>
                </Stack>

                <Typography variant="h6">About</Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  {selectedPersona.description || "No description available."}
                </Typography>

                <Typography variant="h6">Tagline</Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  {selectedPersona.tagline || "No tagline specified."}
                </Typography>

                <Typography variant="h6">Goals</Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  {selectedPersona.goals?.join(', ') || "No goals specified."}
                </Typography>

                <Typography variant="h6">Motivations</Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  {selectedPersona.motivations?.join(', ') || "No motivations specified."}
                </Typography>

                <Typography variant="h6">Frustrations</Typography>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  {selectedPersona.frustrations?.join(', ') || "No frustrations specified."}
                </Typography>
              </Stack>
            ) : (
              <Typography variant="body1" color="text.secondary" align="center" sx={{ mt: 10 }}>
                Select a persona to view details
              </Typography>
            )}
          </Box>
        </Stack>

        <Box
          sx={{
            p: 2,
            borderTop: 1,
            borderColor: "divider",
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          <Button onClick={onClose} sx={{ mr: 1 }}>
            CANCEL
          </Button>
          <Button variant="contained" onClick={handleConfirm} disabled={!selectedPersona}>
            CONFIRM
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default SelectPersonaModal;
import { useMapCanvas } from "@/context/MapCanvasProvider";
import { Activity } from "@/types/grid.types";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import {
  Avatar,
  Box,
  Button,
  Card,
  CircularProgress,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";

export interface Attachment {
  name: string;
  image: string;
  status: string;
}

interface ActivityCardProps {
  activity: Activity;
  onClick?: () => void;
  onEdit?: (newText: string) => void;
  onDelete?: () => void;
  onEditPersona?: (roleId: number) => void;
  roleId?: number;
  attachments?: Attachment[];
  avatarUrl?: string;
  personaType?: string;
  activityType?: "customer" | "front_stage" | "back_stage" | "system";
}

const ActivityCard: React.FC<ActivityCardProps> = ({
  activity,
  onClick,
  onEdit,
  onDelete,
  onEditPersona,
  attachments,
  avatarUrl,
  personaType,
  activityType = "customer",
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(activity.description || "");
  const [isRevising, setIsRevising] = useState(false);
  const { updateActivity } = useMapCanvas();
  const open = Boolean(anchorEl);

  // Sync editText with activity.desc when it changes (after successful update)
  useEffect(() => {
    if (!isEditing) {
      setEditText(activity.description || "");
    }
  }, [activity.description, isEditing]);

  // Helper function to get status icon and color
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return {
          icon: React.createElement(AccessTimeIcon, { fontSize: "inherit" }),
          color: "#FF9800", // Orange
          bgColor: "rgba(255, 152, 0, 0.1)",
        };
      case "scheduled":
        return {
          icon: React.createElement(CalendarTodayIcon, { fontSize: "inherit" }),
          color: "#2196F3", // Light blue
          bgColor: "rgba(33, 150, 243, 0.1)",
        };
      case "completed":
        return {
          icon: React.createElement(CheckCircleIcon, { fontSize: "inherit" }),
          color: "#4CAF50", // Green
          bgColor: "rgba(76, 175, 80, 0.1)",
        };
      default:
        return {
          icon: React.createElement(AccessTimeIcon, { fontSize: "inherit" }),
          color: "#FF9800",
          bgColor: "rgba(255, 152, 0, 0.1)",
        };
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleDelete = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onDelete) onDelete();
    handleClose();
  };

  const handleTextClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    setIsEditing(true);
  };

  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEditText(event.target.value);
  };

  const handleSave = async (event: React.MouseEvent) => {
    event.stopPropagation();

    try {
      // Update via MapCanvasProvider
      await updateActivity(activity.activity_id, {
        name: editText,
        description: editText,
        activity_type: activityType,
        estimated_duration: "Unknown",
        complexity_level: "medium",
        personas: [],
        sequence: 1,
        status: "active",
      });

      // Also call the original onEdit callback for backward compatibility
      if (onEdit) onEdit(editText);

      setIsEditing(false);
    } catch (error) {
      console.error("Failed to save activity:", error);
      // Still close editing mode even if save fails
      setIsEditing(false);
    }
  };

  const handleCancel = (event: React.MouseEvent) => {
    event.stopPropagation();
    setEditText(activity.description || "");
    setIsEditing(false);
  };

  const handleAvatarClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    if (onEditPersona && activity.created_by?.user_id) {
      onEditPersona(parseInt(activity.created_by.user_id));
    }
  };

  const handleReviseSentence = async (event: React.MouseEvent) => {
    event.stopPropagation();
    handleClose();
    setIsRevising(true);
    setIsEditing(true);

    try {
      // Extract a title from the text (first few words or first sentence)
      const desc = activity.description || "";
      const title = desc.split(".")[0].trim().substring(0, 30) + (desc.length > 30 ? "..." : "");

      // Call the AI service
      const payload = {
        title: title,
        type: activity.created_by?.name || "Customer",
      };

      // const result = await aiService.generateText("activity", payload);
      const result = "AI Generated Text";

      // Extract just the generated sentence from the response
      // The response format is expected to be something like:
      // "Customer\n\nExpected Output: \"The customer withdraws money to manage their personal expenses.\""
      const match = result.match(/"([^"]+)"/);
      if (match && match[1]) {
        setEditText(match[1]);
      } else {
        // If we can't extract the quoted text, use the whole result
        setEditText(result);
      }
    } catch (error) {
      console.error("Failed to revise sentence:", error);
    } finally {
      setIsRevising(false);
    }
  };

  // Create tooltip content
  const tooltipContent = () => {
    if (activity.created_by?.name) {
      return (
        <Box>
          <Typography variant="body2">
            <strong>{activity.created_by.name}</strong>
          </Typography>
        </Box>
      );
    }
  };

  return (
    <Card
      onClick={onClick}
      sx={{
        backgroundColor: "background.paper",
        color: "text.primary",
        padding: 2,
        borderRadius: 2,
        width: "350px",
        height: "200px",
        overflow: "auto",
        cursor: isEditing ? "default" : onClick ? "pointer" : "default",
        display: "flex",
        flexDirection: "column",
        position: "relative",
        "&:hover":
          !isEditing && onClick
            ? {
                backgroundColor: "activity.hover",
              }
            : undefined,
      }}
    >
      <Box
        sx={{ display: "flex", flexDirection: "column", alignItems: "flex-start", mb: 1, gap: 2 }}
      >
        {(avatarUrl || personaType === "system") && (
          <Tooltip title={tooltipContent()} arrow placement="top">
            <Avatar
              src={personaType === "system" ? undefined : avatarUrl}
              alt={activity.created_by?.name || "Profile"}
              sx={{
                width: 40,
                height: 40,
                mr: 2,
                cursor: "pointer",
                bgcolor: personaType === "system" ? "grey.600" : undefined,
                "&:hover": {
                  boxShadow: "0 0 0 2px rgba(25, 118, 210, 0.5)",
                },
              }}
              onClick={handleAvatarClick}
            >
              {personaType === "system" &&
                React.createElement(
                  "svg",
                  {
                    width: "24",
                    height: "24",
                    viewBox: "0 0 24 24",
                    fill: "currentColor",
                  },
                  React.createElement("path", {
                    d: "M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z",
                  })
                )}
            </Avatar>
          </Tooltip>
        )}

        <Box sx={{ flex: 1, width: "100%" }}>
          {isEditing ? (
            <Box onClick={(e) => e.stopPropagation()} onMouseDown={(e) => e.stopPropagation()}>
              {isRevising ? (
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Revising...
                  </Typography>
                </Box>
              ) : null}
              <TextField
                fullWidth
                multiline
                value={editText}
                onChange={handleTextChange}
                autoFocus
                variant="outlined"
                size="small"
                sx={{
                  mb: 1,
                  "& .MuiInputBase-root": {
                    cursor: "text",
                  },
                  "& .MuiInputBase-input": {
                    cursor: "text",
                    maxHeight: "150px",
                    overflow: "auto",
                  },
                }}
                onClick={(e) => e.stopPropagation()}
                onMouseDown={(e) => e.stopPropagation()}
                onKeyDown={(e) => e.stopPropagation()}
                disabled={isRevising}
                maxRows={6}
              />
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 1,
                  position: "sticky",
                  bottom: 0,
                  pt: 1,
                  zIndex: 1,
                }}
              >
                <Button
                  size="small"
                  variant="outlined"
                  onClick={handleCancel}
                  onMouseDown={(e) => e.stopPropagation()}
                  disabled={isRevising}
                >
                  Cancel
                </Button>
                <Button
                  size="small"
                  variant="contained"
                  onClick={handleSave}
                  onMouseDown={(e) => e.stopPropagation()}
                  disabled={isRevising}
                >
                  Save
                </Button>
              </Box>
            </Box>
          ) : (
            <Typography
              variant="body1"
              onClick={handleTextClick}
              sx={{
                wordBreak: "break-word",
                whiteSpace: "pre-wrap",
                pr: onEdit || onDelete ? 4 : 0,
                cursor: "text",
                "&:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                },
              }}
            >
              {activity.description}
            </Typography>
          )}
        </Box>

        {!isEditing && (onEdit || onDelete) && (
          <Box sx={{ position: "absolute", top: 8, right: 8 }}>
            <IconButton
              aria-label="more"
              aria-controls={open ? "activity-menu" : undefined}
              aria-haspopup="true"
              aria-expanded={open ? "true" : undefined}
              onClick={handleMenuClick}
              size="small"
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
            <Menu
              id="activity-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              onClick={(e) => e.stopPropagation()}
            >
              <MenuItem onClick={handleReviseSentence}>
                <AutoFixHighIcon fontSize="small" sx={{ mr: 1 }} />
                Revise Sentence
              </MenuItem>
              {onDelete && <MenuItem onClick={handleDelete}>Delete</MenuItem>}
            </Menu>
          </Box>
        )}
      </Box>

      {/* Attachments Section */}
      {attachments && attachments.length > 0 && (
        <Box
          sx={{
            mt: 2,
            pt: 2,
            borderTop: "1px solid",
            borderColor: "divider",
            display: "flex",
            gap: 1,
            alignItems: "center",
          }}
        >
          {attachments.slice(0, 2).map((attachment, index) => {
            const statusInfo = getStatusIcon(attachment.status);
            return (
              <Tooltip key={index} title={attachment.name} arrow placement="top">
                <Box
                  sx={{
                    position: "relative",
                    width: 120,
                    height: 80,
                    borderRadius: 1,
                    overflow: "hidden",
                    cursor: "pointer",
                    "&:hover": {
                      transform: "scale(1.02)",
                      transition: "transform 0.2s ease-in-out",
                    },
                  }}
                >
                  {/* Attachment Image */}
                  <Box
                    component="img"
                    src={attachment.image}
                    alt={attachment.name}
                    sx={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                      display: "block",
                    }}
                  />
                  {/* Status Icon */}
                  <Box
                    sx={{
                      position: "absolute",
                      top: 4,
                      right: 4,
                      width: 25,
                      height: 25,
                      borderRadius: "50%",
                      bgcolor: statusInfo.color,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "white",
                      fontSize: "15px",
                      boxShadow: "0 1px 3px rgba(0,0,0,0.2)",
                    }}
                  >
                    {statusInfo.icon}
                  </Box>
                </Box>
              </Tooltip>
            );
          })}
          {attachments.length > 2 && (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: 120,
                height: 80,
                borderRadius: 1,
                bgcolor: "primary.main",
                color: "white",
                cursor: "pointer",
                "&:hover": {
                  bgcolor: "primary.dark",
                  transform: "scale(1.02)",
                  transition: "transform 0.2s ease-in-out",
                },
              }}
            >
              <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                +{attachments.length - 2}
              </Typography>
            </Box>
          )}
        </Box>
      )}
    </Card>
  );
};

export default ActivityCard;

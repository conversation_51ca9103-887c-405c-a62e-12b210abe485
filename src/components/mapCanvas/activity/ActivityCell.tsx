import { Activity } from "@/types/grid.types";
import { ActivityCreate } from "@/types/api/client";
import { useDroppable } from "@dnd-kit/core";
import AddIcon from "@mui/icons-material/Add";
import { <PERSON>, But<PERSON>, Card } from "@mui/material";
import React, { useState } from "react";
import { useMapCanvas } from "@/context/MapCanvasProvider";
import ActivityCard from "./ActivityCard";
import SelectPersonaModal from "./SelectPersonaModal";

type ActivityCellProps = {
  momentId: number | string;
  activityType: string;
  role: string;
  activities: Activity[];
  isPanningDisabled?: boolean;
  onEditActivity: (id: string, updatedText: string) => void;
  onDeleteActivity: (id: string) => void;
  avatarUrl?: string;
  personaType?: string;
  personaName?: string;
};

export const ActivityCell: React.FC<ActivityCellProps> = ({
  momentId,
  activityType,
  role,
  activities,
  onEditActivity,
  onDeleteActivity,
  avatarUrl,
  personaType,
  personaName,
}) => {
  const [isHovering, setIsHovering] = useState(false);
  const [isPersonaModalOpen, setIsPersonaModalOpen] = useState(false);
  const { state, addActivity } = useMapCanvas();
  
  // Get personas for the current role type
  const getRoleTypeKey = (role: string): keyof typeof state.availablePersonas => {
    const normalized = role.toLowerCase().replace(/\s+/g, '');
    const typeMapping: Record<string, keyof typeof state.availablePersonas> = {
      customer: 'customer',
      frontstage: 'front_stage',
      'front_stage': 'front_stage',
      backstage: 'back_stage',
      'back_stage': 'back_stage',
      system: 'system',
    };
    return typeMapping[normalized] || 'customer';
  };
  
  const roleTypeKey = getRoleTypeKey(role);
  const availablePersonas = state.availablePersonas[roleTypeKey] || [];
  
  // Helper to get persona info for activities
  const getActivityPersonaInfo = (activity: Activity) => {
    if (activity.personas && activity.personas.length > 0) {
      // Find persona by ID from personas array in activity
      const personaId = activity.personas[0]; // Use first persona if multiple
      const activityPersona = state.allPersonas.find(p => p.persona_id === personaId);
      if (activityPersona) {
        return {
          avatarUrl: activityPersona.image_url,
          personaName: activityPersona.name,
        };
      }
    }
    // Fallback to the cell's persona info
    return {
      avatarUrl,
      personaName,
    };
  };
  const cellId = `cell-${momentId}-${activityType}-${role}`;

  const { setNodeRef, isOver } = useDroppable({
    id: cellId,
    data: {
      type: "activity",
    },
    disabled: false,
  });

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  return (
    <Card
      ref={setNodeRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      sx={{
        width: "100%",
        height: "100%",
        p: 2,
        bgcolor: isOver ? "action.hover" : "transparent",
        display: "grid",
        gridTemplateColumns: "repeat(3, 1fr)",
        gap: "1rem",
        position: "relative",
        transition: "background-color 0.2s",
        overflow: "auto",
        borderRadius: 0,
      }}
    >
      {activities.map((activity) => {
        const activityPersonaInfo = getActivityPersonaInfo(activity);
        return (
          <ActivityCard
            key={activity.activity_id}
            activity={{
              activity_id: activity.activity_id,
              name: activity.name,
              description: activity.description,
              created_by: {
                user_id: "1",
                name: activityPersonaInfo.personaName || role,
                profile_image_url: activityPersonaInfo.avatarUrl || "",
                timestamp: new Date().toISOString(),
              },
            }}
            onEdit={(newText) => onEditActivity(activity.activity_id, newText)}
            onDelete={() => onDeleteActivity(activity.activity_id)}
            avatarUrl={activityPersonaInfo.avatarUrl}
            personaType={personaType}
            activityType={getRoleTypeKey(role) as "customer" | "front_stage" | "back_stage" | "system"}
          />
        );
      })}
      {/* Add Activity Button - only visible on hover */}
      <Box sx={{ position: "absolute", bottom: 8, right: 8 }}>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setIsPersonaModalOpen(true)}
          sx={{
            backgroundColor: "#c8ff00",
            color: "#000000",
            fontWeight: "bold",
            opacity: isHovering ? 1 : 0,
            transition: "opacity 0.2s ease-in-out",
            "&:hover": {
              backgroundColor: "#b3e600",
            },
          }}
        >
          ADD
        </Button>
      </Box>
      
      <SelectPersonaModal
        open={isPersonaModalOpen}
        onClose={() => setIsPersonaModalOpen(false)}
        onConfirm={async (selectedPersona) => {
          // Create activity data with default "Click to edit" values
          const activityData: ActivityCreate = {
            name: "Click to edit",
            description: "Click to edit",
            activity_type: getRoleTypeKey(role) as "customer" | "front_stage" | "back_stage" | "system",
            estimated_duration: "Unknown",
            complexity_level: "medium",
            personas: [selectedPersona.persona_id],
            sequence: activities.length + 1,
          };

          // Call the provider's addActivity function
          await addActivity(momentId.toString(), activityData);
          setIsPersonaModalOpen(false);
        }}
        roleType={role}
        personas={availablePersonas}
      />
    </Card>
  );
};

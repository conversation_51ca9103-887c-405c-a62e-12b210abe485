import { Moment as ApiMoment } from "@/types/api/client";
import { VisibleRow } from "@/types/grid.types";
import { Box } from "@mui/material";
import React from "react";
import { ActivityCell } from "../activity/ActivityCell";
import MomentCard from "../moment/MomentCard";
import { SystemCell } from "./SystemCell";

interface GridCellProps {
  row: VisibleRow;
  moment: ApiMoment;
  isSelected: boolean;
  cellWidth: number;
  cellHeight: number;
  columnIndex?: number;
  personas?: any[];
}

export const GridCell: React.FC<GridCellProps> = ({
  row,
  moment,
  isSelected,
  cellWidth,
  cellHeight,
  columnIndex = 0,
  personas = [],
}) => {
  // Determine background color based on column index
  const getBackgroundColor = () => {
    if (isSelected) return "primary.dark";
    return columnIndex % 2 === 0 ? "map.evenColBackground" : "map.oddColBackground";
  };

  // Helper function to get activities for this moment and persona type
  const getActivitiesForMomentAndPersona = (personaType: string) => {
    if (!moment || !moment.activities) return [];

    const activityType = personaType.toLowerCase().replace(/_/g, "").replace(/\s+/g, "");
    const typeMapping: Record<string, string> = {
      customer: "customer",
      frontstage: "front_stage",
      backstage: "back_stage",
      system: "system",
    };

    const mappedType = typeMapping[activityType];
    return mappedType && moment.activities[mappedType as keyof typeof moment.activities]
      ? moment.activities[mappedType as keyof typeof moment.activities]
      : [];
  };

  // Helper function to get persona information for a given persona type
  const getPersonaInfo = (personaTypeName: string) => {
    const normalizedTypeName = personaTypeName.toLowerCase().replace(/\s+/g, "_");
    const persona = personas.find((p) => p.persona_type === normalizedTypeName);
    return {
      avatarUrl: persona?.image_url,
      personaType: persona?.persona_type || normalizedTypeName,
      personaName: persona?.name,
    };
  };

  const renderCellContent = () => {
    switch (row.type) {
      case "story":
        return (
          <MomentCard
            id={parseInt(moment.moment_id) || 0}
            name={moment.name || "Untitled Moment"}
            title={moment.name || "Untitled Moment"}
            desc={moment.description || ""}
            image={moment.image_url}
            components={[]}
            onEdit={(updates) => console.log("Edit moment:", updates)}
            onImageUpdate={(momentId, imageUrl) =>
              console.log("Update moment image:", momentId, imageUrl)
            }
          />
        );

      case "action":
      case "activity":
        const activities = getActivitiesForMomentAndPersona(row.name);
        const personaInfo = getPersonaInfo(row.name);
        return (
          <ActivityCell
            momentId={moment.moment_id || 0}
            activityType={row.type}
            role={row.name}
            activities={Array.isArray(activities) ? activities : []}
            onEditActivity={(id, text) => console.log("Edit activity:", id, text)}
            onDeleteActivity={(id) => console.log("Delete activity:", id)}
            avatarUrl={personaInfo.avatarUrl}
            personaType={personaInfo.personaType}
            personaName={personaInfo.personaName}
          />
        );

      case "block":
        return <></>;

      case "system":
        return <SystemCell row={row} moment={moment} />;

      default:
        const defaultActivities = getActivitiesForMomentAndPersona(row.name);
        const defaultPersonaInfo = getPersonaInfo(row.name);
        return (
          <ActivityCell
            momentId={moment.moment_id || 0}
            activityType="activity"
            role={row.name}
            activities={Array.isArray(defaultActivities) ? defaultActivities : []}
            onEditActivity={(id, text) => console.log("Edit activity:", id, text)}
            onDeleteActivity={(id) => console.log("Delete activity:", id)}
            avatarUrl={defaultPersonaInfo.avatarUrl}
            personaType={defaultPersonaInfo.personaType}
            personaName={defaultPersonaInfo.personaName}
          />
        );
    }
  };

  return (
    <Box
      data-cell={`${row.id}-${moment.moment_id}`}
      sx={{
        width: cellWidth,
        height: cellHeight,
        border: "none",
        bgcolor: getBackgroundColor(),
        p: 0,
        boxSizing: "border-box",
        cursor: "default",
        borderRadius: 0,
      }}
    >
      {renderCellContent()}
    </Box>
  );
};

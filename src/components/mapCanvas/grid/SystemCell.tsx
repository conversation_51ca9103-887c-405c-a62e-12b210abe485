import { Moment as ApiMoment } from "@/types/api/client";
import { VisibleRow } from "@/types/grid.types";
import { Paper, Typography } from "@mui/material";
import React from "react";

interface SystemCellProps {
  row: VisibleRow;
  moment: ApiMoment;
}

export const SystemCell: React.FC<SystemCellProps> = ({ row, moment }) => {
  return (
    <Paper
      sx={{
        p: 2,
        height: "100%",
        bgcolor: "grey.900",
        border: "1px solid",
        borderColor: "grey.700",
      }}
    >
      <Typography variant="subtitle2" color="grey.300">
        {row?.name || "System"}
      </Typography>
      <Typography variant="caption" color="grey.500" sx={{ mt: 0.5 }}>
        {row?.name || "System"} - {moment?.name || "Moment"}
      </Typography>
    </Paper>
  );
};

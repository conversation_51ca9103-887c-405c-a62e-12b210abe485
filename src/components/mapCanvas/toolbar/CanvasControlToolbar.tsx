import AddIcon from "@mui/icons-material/Add";
import CropFreeIcon from "@mui/icons-material/CropFree";
import PanToolIcon from "@mui/icons-material/PanTool";
import RemoveIcon from "@mui/icons-material/Remove";
import TuneIcon from "@mui/icons-material/Tune";
import { Box, FormControlLabel, IconButton, Menu, MenuItem, Switch } from "@mui/material";
import React, { useState } from "react";

type CanvasControlToolbarProps = {
  onReset: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onTogglePanning: () => void;
  isPanningDisabled: boolean;
  toggleStoryMode?: () => void;
  isStoryModeOn?: boolean;
};

export const CanvasControlToolbar: React.FC<CanvasControlToolbarProps> = ({
  onReset,
  onZoomIn,
  onZoomOut,
  onTogglePanning,
  isPanningDisabled,
  toggleStoryMode,
  isStoryModeOn = false,
}) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [isStoryView, setIsStoryView] = useState(isStoryModeOn);

  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleCloseAllMenus = () => {
    setMenuAnchorEl(null);
  };

  const handleToggleSwitch = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation();
    setIsStoryView(!isStoryView);
    toggleStoryMode?.();
  };

  return (
    <Box
      sx={{
        zIndex: 1,
        position: "fixed",
        left: "calc(var(--sidebar-width, 0px) + 30px)",
        top: "20%",
        display: "flex",
        flexDirection: "column",
        gap: 1,
        backgroundColor: "background.paper",
        border: "1px solid",
        borderColor: "divider",
        padding: 1,
        borderRadius: 2,
        transition: "left 0.2s",
      }}
    >
      <IconButton onClick={onReset} color="primary">
        <CropFreeIcon />
      </IconButton>
      <IconButton onClick={onTogglePanning} color="primary">
        <PanToolIcon color={isPanningDisabled ? "inherit" : "action"} />
      </IconButton>
      <IconButton onClick={onZoomIn} color="primary">
        <AddIcon />
      </IconButton>
      <IconButton onClick={onZoomOut} color="primary">
        <RemoveIcon />
      </IconButton>
      <IconButton onClick={handleOpenMenu} color="primary">
        <TuneIcon />
      </IconButton>

      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleCloseAllMenus}
        anchorOrigin={{
          vertical: "center",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "left",
        }}
      >
        <MenuItem onClick={(e) => e.stopPropagation()}>
          <FormControlLabel
            control={
              <Switch
                checked={isStoryView}
                onChange={handleToggleSwitch}
                size="small"
                color="primary"
              />
            }
            label="Story Mode"
            labelPlacement="start"
            sx={{ margin: 0, justifyContent: "space-between", width: "100%" }}
          />
        </MenuItem>
      </Menu>
    </Box>
  );
};

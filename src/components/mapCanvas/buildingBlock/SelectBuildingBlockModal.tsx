import { BuildingBlock } from '@/types/grid.types';
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  Checkbox,
  InputAdornment,
  Modal,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

interface SelectBuildingBlockModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (selectedBlocks: BuildingBlock[]) => void;
  title?: string;
  typeId: number;
  typeName: string;
  subTypeId?: number;
  existingBuildingBlocks?: any[];
}

const SelectBuildingBlockModal: React.FC<SelectBuildingBlockModalProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  typeId,
  typeName,
  subTypeId,
  existingBuildingBlocks = [],
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedBlocks, setSelectedBlocks] = useState<Set<number>>(new Set());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Mock building blocks data
  const mockBuildingBlocks: BuildingBlock[] = [
    {
      id: 1,
      name: "Sample Block 1",
      desc: "This is a sample building block for testing",
      type: typeId,
      sub_type: subTypeId,
    },
    {
      id: 2,
      name: "Sample Block 2", 
      desc: "Another sample building block",
      type: typeId,
      sub_type: subTypeId,
    },
  ];

  const filteredBlocks = mockBuildingBlocks.filter(block => 
    !searchQuery || 
    block.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    block.desc?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const paginatedBlocks = filteredBlocks.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleSelectBlock = (blockId: number) => {
    const newSelected = new Set(selectedBlocks);
    if (newSelected.has(blockId)) {
      newSelected.delete(blockId);
    } else {
      newSelected.add(blockId);
    }
    setSelectedBlocks(newSelected);
  };

  const handleConfirm = () => {
    const selectedBlockData = mockBuildingBlocks.filter(block => 
      selectedBlocks.has(block.id!)
    );
    onConfirm?.(selectedBlockData);
    onClose();
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 2,
          width: "90%",
          maxWidth: 1200,
          maxHeight: "90vh",
          overflow: "auto",
          outline: "none",
          p: 3,
        }}
      >
        <Typography variant="h5" sx={{ mb: 3 }}>
          {title || `Select ${typeName}`}
        </Typography>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
          {/* Search Filter */}
          <TextField
            placeholder="Search"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              },
            }}
            sx={{ maxWidth: 400 }}
          />

          {/* Table */}
          <TableContainer sx={{ maxHeight: 400 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox disabled />
                  </TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Description</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedBlocks.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={3} align="center">
                      <Typography color="text.secondary">
                        No data available
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedBlocks.map((block) => (
                    <TableRow
                      key={block.id}
                      hover
                      onClick={() => handleSelectBlock(block.id!)}
                      sx={{ cursor: "pointer" }}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selectedBlocks.has(block.id!)}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{block.name || "Untitled"}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" color="text.secondary">
                          {block.desc || "No description"}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={filteredBlocks.length}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
            rowsPerPageOptions={[5, 10, 25]}
          />

          {/* Actions */}
          <Stack direction="row" spacing={2} justifyContent="flex-end">
            <Button variant="outlined" onClick={onClose}>
              CANCEL
            </Button>
            <Button variant="contained" onClick={handleConfirm} disabled={selectedBlocks.size === 0}>
              CONFIRM
            </Button>
          </Stack>
        </Box>
      </Box>
    </Modal>
  );
};

export default SelectBuildingBlockModal;
import { BuildingBlock } from '@/types/grid.types';
import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  Button,
  Checkbox,
  Chip,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Modal,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

interface VoiceOfCustomerData extends BuildingBlock {
  content?: {
    age?: number;
    date?: string;
    brand?: string;
    channel?: string;
    s_score?: number;
    segment?: string;
    raw_score?: number;
    sentiment?: string;
    customerid?: number;
  };
}

interface SelectVoiceOfCustomerModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (selectedData: VoiceOfCustomerData[]) => void;
  title?: string;
  subTypeId: number;
  existingBuildingBlocks?: any[];
}

const SelectVoiceOfCustomerModal: React.FC<SelectVoiceOfCustomerModalProps> = ({
  open,
  onClose,
  onConfirm,
  title = "Select Voice of Customer data",
  subTypeId,
  existingBuildingBlocks = [],
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedBrand, setSelectedBrand] = useState("All");
  const [selectedData, setSelectedData] = useState<Set<number>>(new Set());
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Mock Voice of Customer data
  const mockVoiceOfCustomerData: VoiceOfCustomerData[] = [
    {
      id: 1,
      name: "Customer Feedback 1",
      desc: "Great service, very satisfied with the experience",
      type: 4,
      sub_type: subTypeId,
      content: {
        date: "2024-01-15",
        sentiment: "promoters",
        brand: "Brand A",
        segment: "Premium",
        s_score: 9,
      }
    },
    {
      id: 2,
      name: "Customer Feedback 2", 
      desc: "Service was okay but could be improved",
      type: 4,
      sub_type: subTypeId,
      content: {
        date: "2024-01-16",
        sentiment: "passives",
        brand: "Brand B", 
        segment: "Standard",
        s_score: 7,
      }
    },
  ];

  const filteredData = mockVoiceOfCustomerData.filter(item => {
    if (searchQuery && 
        !item.name?.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !item.desc?.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    if (selectedCategory !== "All" && item.content?.sentiment !== selectedCategory) {
      return false;
    }
    if (selectedBrand !== "All" && item.content?.brand !== selectedBrand) {
      return false;
    }
    return true;
  });

  const paginatedData = filteredData.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  const handleSelectData = (dataId: number) => {
    const newSelected = new Set(selectedData);
    if (newSelected.has(dataId)) {
      newSelected.delete(dataId);
    } else {
      newSelected.add(dataId);
    }
    setSelectedData(newSelected);
  };

  const handleConfirm = () => {
    const selectedVoiceOfCustomerData = mockVoiceOfCustomerData.filter(item =>
      selectedData.has(item.id!)
    );
    onConfirm?.(selectedVoiceOfCustomerData);
    onClose();
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const capitalize = (str?: string) => {
    if (!str) return "";
    return str.charAt(0).toUpperCase() + str.slice(1);
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          bgcolor: "background.paper",
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 2,
          width: "90%",
          maxWidth: 1200,
          maxHeight: "90vh",
          overflow: "auto",
          outline: "none",
          p: 3,
        }}
      >
        <Typography variant="h5" sx={{ mb: 3 }}>
          {title}
        </Typography>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
          {/* Filters */}
          <Stack direction="row" spacing={2} alignItems="center">
            {/* Search */}
            <TextField
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                },
              }}
              sx={{ minWidth: 300 }}
            />

            {/* Category Filter */}
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>Category</InputLabel>
              <Select
                value={selectedCategory}
                label="Category"
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <MenuItem value="All">All</MenuItem>
                <MenuItem value="promoters">Promoters</MenuItem>
                <MenuItem value="passives">Passives</MenuItem>
                <MenuItem value="detractors">Detractors</MenuItem>
              </Select>
            </FormControl>

            {/* Brand Filter */}
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>Brand</InputLabel>
              <Select
                value={selectedBrand}
                label="Brand"
                onChange={(e) => setSelectedBrand(e.target.value)}
              >
                <MenuItem value="All">All</MenuItem>
                <MenuItem value="Brand A">Brand A</MenuItem>
                <MenuItem value="Brand B">Brand B</MenuItem>
              </Select>
            </FormControl>
          </Stack>

          {/* Data Table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox disabled />
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      Timestamp
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      Sentiment
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      Description
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <Typography color="text.secondary">No data available</Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  paginatedData.map((item) => (
                    <TableRow
                      key={item.id}
                      hover
                      onClick={() => handleSelectData(item.id!)}
                      sx={{ cursor: "pointer" }}
                    >
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selectedData.has(item.id!)}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{formatDate(item.content?.date)}</Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={capitalize(item.content?.sentiment)}
                          color={
                            item.content?.sentiment === "promoters"
                              ? "success"
                              : item.content?.sentiment === "detractors"
                              ? "error"
                              : item.content?.sentiment === "passives"
                              ? "warning"
                              : "default"
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{item.desc}</Typography>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={filteredData.length}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
            rowsPerPageOptions={[5, 10, 25]}
          />

          {/* Action Buttons */}
          <Stack direction="row" spacing={2} justifyContent="flex-end">
            <Button variant="outlined" onClick={onClose}>
              CANCEL
            </Button>
            <Button
              variant="contained"
              onClick={handleConfirm}
              disabled={selectedData.size === 0}
            >
              CONFIRM
            </Button>
          </Stack>
        </Box>
      </Box>
    </Modal>
  );
};

export default SelectVoiceOfCustomerModal;
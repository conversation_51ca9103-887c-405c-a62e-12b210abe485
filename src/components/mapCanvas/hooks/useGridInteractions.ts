import { GridPosition } from "@/types/grid.types";
import { useCallback, useEffect, useRef } from "react";

interface UseGridInteractionsProps {
  mode: "pan" | "select";
  onScroll: (pos: GridPosition) => void;
  onCellSelect?: (cellId: string, isMulti: boolean) => void;
}

export const useGridInteractions = ({ mode, onScroll, onCellSelect }: UseGridInteractionsProps) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef(false);
  const dragStartRef = useRef({ x: 0, y: 0 });
  const scrollStartRef = useRef({ x: 0, y: 0 });

  const handleWheel = useCallback((e: WheelEvent) => {
    e.preventDefault();
    if (!scrollAreaRef.current) return;
    scrollAreaRef.current.scrollLeft += e.deltaX;
    scrollAreaRef.current.scrollTop += e.deltaY;
  }, []);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (
        e.target instanceof Element &&
        (e.target.closest(".dropdown-trigger") || e.target.closest(".dropdown-menu"))
      ) {
        return;
      }

      if (mode === "pan") {
        isDraggingRef.current = true;
        dragStartRef.current = { x: e.clientX, y: e.clientY };
        if (scrollAreaRef.current) {
          scrollStartRef.current = {
            x: scrollAreaRef.current.scrollLeft,
            y: scrollAreaRef.current.scrollTop,
          };
        }
      } else if (mode === "select" && onCellSelect) {
        const cell = (e.target as Element).closest("[data-cell]");
        if (cell) {
          const cellId = cell.getAttribute("data-cell") || "";
          onCellSelect(cellId, e.shiftKey);
        }
      }
    },
    [mode, onCellSelect]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDraggingRef.current || !scrollAreaRef.current || mode !== "pan") return;

      const dx = dragStartRef.current.x - e.clientX;
      const dy = dragStartRef.current.y - e.clientY;

      scrollAreaRef.current.scrollLeft = scrollStartRef.current.x + dx;
      scrollAreaRef.current.scrollTop = scrollStartRef.current.y + dy;
    },
    [mode]
  );

  const handleMouseUp = useCallback(() => {
    isDraggingRef.current = false;
  }, []);

  const handleScroll = useCallback(() => {
    if (!scrollAreaRef.current) return;
    onScroll({
      x: scrollAreaRef.current.scrollLeft,
      y: scrollAreaRef.current.scrollTop,
    });
  }, [onScroll]);

  useEffect(() => {
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("mouseleave", handleMouseUp);

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("mouseleave", handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  return {
    scrollAreaRef,
    handleWheel,
    handleMouseDown,
    handleScroll,
  };
};

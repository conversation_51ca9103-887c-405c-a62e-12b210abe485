import { useState, useCallback } from 'react';
import { GridState, GridPosition } from '@/types/grid.types';

export const useGridState = (initialState?: Partial<GridState>) => {
  const [scale, setScale] = useState(initialState?.scale ?? 0.8);
  const [scrollPos, setScrollPos] = useState<GridPosition>(
    initialState?.scrollPos ?? { x: 0, y: 0 }
  );
  const [mode, setMode] = useState<'pan' | 'select'>(initialState?.mode ?? 'pan');
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>(
    initialState?.expandedRows ?? { customer: true, frontStage: true, backStage: true }
  );
  const [hiddenColumns, setHiddenColumns] = useState<string[]>(
    initialState?.hiddenColumns ?? []
  );
  const [hiddenRows, setHiddenRows] = useState<string[]>(
    initialState?.hiddenRows ?? []
  );
  const [selectedCells, setSelectedCells] = useState<Set<string>>(
    initialState?.selectedCells ?? new Set()
  );
  const [isStoryMode, setIsStoryMode] = useState(initialState?.isStoryMode ?? false);

  const toggleRow = useCallback((rowId: string) => {
    setExpandedRows(prev => ({ ...prev, [rowId]: !prev[rowId] }));
  }, []);

  const toggleRowVisibility = useCallback((rowId: string) => {
    setHiddenRows(prev => 
      prev.includes(rowId) 
        ? prev.filter(id => id !== rowId)
        : [...prev, rowId]
    );
  }, []);

  const toggleColumnVisibility = useCallback((columnId: string) => {
    setHiddenColumns(prev => 
      prev.includes(columnId) 
        ? prev.filter(id => id !== columnId)
        : [...prev, columnId]
    );
  }, []);

  return {
    scale,
    setScale,
    scrollPos,
    setScrollPos,
    mode,
    setMode,
    expandedRows,
    toggleRow,
    hiddenColumns,
    hiddenRows,
    toggleRowVisibility,
    toggleColumnVisibility,
    selectedCells,
    setSelectedCells,
    isStoryMode,
    setIsStoryMode,
  };
};

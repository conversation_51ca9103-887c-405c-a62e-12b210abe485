import React, { useState } from 'react';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, TextField, Typography, InputAdornment, TableFooter, TablePagination
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { ComponentModelRow } from '../utils/componentHelper'; // Adjust the import path as necessary

interface DataTableProps {
  columns: string[]; // The table headers
  data: ComponentModelRow[]; // The parsed data
}

const DataTable: React.FC<DataTableProps> = ({ columns, data }) => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle pagination changes
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0); // Reset to the first page
  };

  // Filter data based on the search query
  const filteredData = data.filter((row) => {
    return Object.entries(row).some(([, value]) => {
      if (Array.isArray(value)) {
        return value.some(item => 
          item.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      return String(value)
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
    });
  });

  return (
    <Paper>
      <TextField
        label="Search"
        variant="outlined"
        value={searchQuery}
        onChange={handleSearchChange}
        margin="normal"
        sx={{
          width: '20rem', // Fixed width for the search bar
          height: '5rem'
        }}
        slotProps={{
          input: {
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          },
        }}
      />
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell key={column}>
                  <Typography variant="body2" fontWeight="bold">
                    {column}
                  </Typography>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredData
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columns.map((column, cellIndex) => (
                    <TableCell key={cellIndex}>
                      {row[column as keyof ComponentModelRow]}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                colSpan={columns.length}
                count={filteredData.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage="Rows per page"
              />
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default DataTable;
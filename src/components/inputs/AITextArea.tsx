import { AutoAwesome } from "@mui/icons-material";
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import React, { useState } from "react";

interface AITextareaProps {
  name: string;
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur: () => void;
  inputRef: React.Ref<any>;
  error?: boolean;
  helperText?: string;
  placeholder?: string;
  rows?: number;
  aiHandler?: () => Promise<string>;
}

const AITextarea: React.FC<AITextareaProps> = ({
  label,
  value,
  onChange,
  onBlur,
  inputRef,
  error,
  helperText,
  placeholder,
  rows = 4,
  aiHandler,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [draftState, setDraftState] = useState<{ draft: string; original: string } | null>(null);

  const handleGenerateAI = async () => {
    if (!aiHandler || isGenerating) return;

    try {
      setIsGenerating(true);
      const generatedText = await aiHandler();

      setDraftState({
        draft: generatedText,
        original: value,
      });
    } catch (error) {
      console.error("AI generation failed:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDiscardDraft = () => {
    if (draftState) {
      // Create a synthetic event to match the onChange signature
      const syntheticEvent = {
        target: { value: draftState.original },
      } as React.ChangeEvent<HTMLInputElement>;

      onChange(syntheticEvent);
      setDraftState(null);
    }
  };

  const handleApplyDraft = () => {
    if (draftState) {
      // Create a synthetic event to match the onChange signature
      const syntheticEvent = {
        target: { value: draftState.draft },
      } as React.ChangeEvent<HTMLInputElement>;

      onChange(syntheticEvent);
      setDraftState(null);
    }
  };

  return (
    <Box>
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
        <Typography variant="body2">{label}</Typography>
        {aiHandler &&
          (isGenerating ? (
            <CircularProgress size={20} />
          ) : (
            <IconButton
              size="small"
              onClick={handleGenerateAI}
              disabled={isGenerating}
              color="primary"
              sx={{
                "&:hover": {
                  cursor: "pointer",
                },
              }}
            >
              <AutoAwesome fontSize="small" />
            </IconButton>
          ))}
      </Stack>
      <TextField
        fullWidth
        multiline
        rows={rows}
        value={draftState?.draft || value}
        onChange={onChange}
        onBlur={onBlur}
        inputRef={inputRef}
        error={error}
        helperText={helperText}
        placeholder={placeholder}
        hiddenLabel={true}
      />
      {draftState && (
        <Stack direction="row" spacing={1} mt={1} justifyContent="flex-end">
          <Button size="small" variant="outlined" onClick={handleDiscardDraft}>
            Discard
          </Button>
          <Button
            size="small"
            variant="outlined"
            onClick={handleGenerateAI}
            disabled={isGenerating}
          >
            Retry
          </Button>
          <Button size="small" variant="contained" onClick={handleApplyDraft}>
            Accept
          </Button>
        </Stack>
      )}
    </Box>
  );
};

export default AITextarea;

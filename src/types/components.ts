import { Component } from "./map";

export interface ComponentData {
  id: number;
  numbering: string;
  name: string;
  description: string;
  is_active: boolean;
  parent: number | null;
  framework: number;
  examples: string;
}

export interface HierarchicalNode {
  id: string | number | null;
  numbering?: string;
  name: string;
  description?: string;
  parent: string | number | null;
  components?: Component[];
  children?: HierarchicalNode[];
}

export interface NodeMapData {
  customer: HierarchicalNode;
  business: HierarchicalNode;
  map: HierarchicalNode[];
}

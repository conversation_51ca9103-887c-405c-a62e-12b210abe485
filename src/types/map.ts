/**
 * @deprecated These types are deprecated. Use the new API v2 types instead.
 * Migration: Replace with `import type { Map, MapDetails, CustomerGoal, etc. } from '@/types/api/client'`
 * 
 * The new types provide better structure, type safety, and match the API v2 specification.
 * @see /src/types/api/client.ts for the new type definitions
 */

export interface Map {
  id: string;
  name: string;
  desc: string;
  organization: string;
  user: number;
  line_of_business: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  state: string;
  customer_goal?: CustomerGoal | number;
}

export interface CustomerGoal {
  id: number;
  name: string;
  desc: string;
  created_at: string;
  updated_at: string;
}

export interface Channel {
  id: number;
  name: string;
  type: string;
  created_at: string;
  updated_at: string;
}

export interface Component {
  id: number;
  numbering: string;
  name?: string;
  description?: string;
  is_active?: boolean;
  level?: number;
  count?: number;
  created_at?: string;
  updated_at?: string;
  parent?: number | null;
  framework?: number;
}

export interface Action {
  id?: string;
  name?: string;
  desc?: string;
  phase?: number;
  block?: number | null;
  role?: MapRole;
  is_active?: boolean;
  is_draft?: boolean;
  parent?: string | null;
  knowledge?: string | null;
  channel?: Channel[];
  type?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
  map_role_id?: number;
}

export interface Phase {
  id?: number;
  name?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
  desc?: string;
  image?: string | null;
  components?: Component[];

  component_ids?: number[];
}

export interface BuildingBlockType {
  id: number;
  type: {
    id: number;
    name: string;
    desc: string;
    order: number;
    created_at: string;
    updated_at: string;
    organization: string;
  };
  order: number;
}

export interface BuildingBlock {
  connection_id?: number;
  connection_type?: number;
  phase?: number;
  id?: number;
  type?:
    | number
    | {
        id: number;
        name: string;
        desc: string;
        order: number;
        created_at: string;
        updated_at: string;
        sub_types?: {
          id: number;
          name: string;
          desc: string;
          order: number;
          created_at: string;
          updated_at: string;
        }[];
        image?: string | null;
      };
  sub_type?:
    | number
    | null
    | {
        id: number;
        name: string;
        desc: string;
        order: number;
        created_at: string;
        updated_at: string;
      };
  role?: number;
  brand?: number | null;
  segment?: number | null;
  name?: string;
  desc?: string;
  order?: number;
  vote?: number;
  content?: any | null;
  created_at?: string;
  updated_at?: string;
  // Additional properties for handling nested type objects
  typeObject?: {
    id: number;
    name: string;
    desc: string;
    order: number;
    created_at: string;
    updated_at: string;
    sub_types?: {
      id: number;
      name: string;
      desc: string;
      order: number;
      created_at: string;
      updated_at: string;
    }[];
    image?: string | null;
  };
  subTypeObject?: {
    id: number;
    name: string;
    desc: string;
    order: number;
    created_at: string;
    updated_at: string;
  };
}

export interface MapBuildingBlock {
  id: number;
  type: number;
  sub_type: number | null;
  phase: number;
  block?: BuildingBlock;
  order: number;
  created_at: string;
  updated_at: string;
}

export interface MapRole {
  id: number;
  name: string;
  image: string | null;
  desc: string;
  order: number;
  map_role_id: number;
  type: string;
}

export interface MapDetails {
  id: string;
  name: string;
  code: string;
  desc: string;
  customer_goal: string;
  business_objective: string;
  layout: string;
  user: number;
  line_of_business: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  phases: Phase[];
  actions: Action[];
  building_block_types: BuildingBlockType[];
  building_blocks: BuildingBlock[];
  roles: MapRole[];
}

export interface Role {
  id: number;
  name: string;
  image: string | null;
  ageRange: string;
  gender: string;
  profession: string;
  job: string | null;
  income: string | null;
  status: string;
  about: string;
  goals: string[];
  frustrations: string[];
}

/**
 * @deprecated These types are deprecated. Use the new API v2 types instead.
 * Migration: Replace with `import type { Persona, PersonaCreate, PersonaUpdate, PersonaDetails } from '@/types/api/client'`
 * 
 * The new types support multiple persona types (customer, front_stage, back_stage, system) and match the API v2 specification.
 * @see /src/types/api/client.ts for the new type definitions
 */

export interface PersonaData {
  id?: number;
  image?: string;
  name: string;
  ageRange: string;
  gender: string;
  profession: string;
  job: string;
  age?: string;
  income: string;
  status: string;
  about: string;
  goals: string;
  frustrations: string;
  location?: string;
}

export interface UpdateRoleData {
  id: number;
  name?: string;
  image?: string;
  ageRange?: string;
  gender?: string;
  profession?: string;
  job?: string;
  income?: string;
  status?: string;
  about?: string;
  goals?: string;
  frustrations?: string;
}
export interface PersonaGroup {
  [key: string]: PersonaData[];
}

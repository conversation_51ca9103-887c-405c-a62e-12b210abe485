interface AgeRange {
  id: number;
  name: string;
  desc: string;
  start_age: number;
  end_age: number;
  created_at: string;
  updated_at: string;
}

export interface BuildingBlockType {
  id: number;
  name: string;
  desc: string;
  order: number;
  created_at: string;
  updated_at: string;
}

interface Channel {
  id: number;
  name: string;
  type: string;
  created_at: string;
  updated_at: string;
}

interface Gender {
  value: "M" | "F" | "O";
  label: string;
}

interface IncomeType {
  id: number;
  name: string;
  desc: string;
  order: number;
  created_at: string;
  updated_at: string;
}

interface LineOfBusiness {
  id: number;
  name: string;
  last_modified: string;
  updated_at: string;
}

interface RoleStatus {
  id: number;
  name: string;
  desc: string;
  created_at: string;
  updated_at: string;
}

export interface RoleType {
  id: number;
  name: string;
  desc: string;
  order: number;
  created_at: string;
  updated_at: string;
}

interface Role {
  id: number;
  name: string;
  image: string | null;
  ageRange: string;
  gender: string;
  profession: string;
  created_at: string;
  updated_at: string;
}

interface MapState {
  id: number;
  value: string;
  label: string;
}

interface CustomerGoal {
  id: number;
  name: string;
  desc: string;
}

interface Brand {
  id: number;
  name: string;
  desc: string | null;
  prompt_style: string | null;
}

interface Segment {
  id: number;
  name: string;
  desc: string | null;
}

export interface CommonData {
  age_ranges: AgeRange[];
  building_block_types: BuildingBlockType[];
  channels: Channel[];
  genders: Gender[];
  income_types: IncomeType[];
  line_of_business: LineOfBusiness[];
  role_statuses: RoleStatus[];
  role_types: RoleType[];
  roles: Role[];
  map_states: MapState[];
  customer_goals: CustomerGoal[];
  brands?: Brand[];
  segments?: Segment[];
}

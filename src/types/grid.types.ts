// API compatible types - aligning with existing mapCanvas data structures
export interface Phase {
  id?: number | string;
  name?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
  desc?: string;
  image?: string | null;
  components?: any[];
  component_ids?: string[]; // Updated to match API v2 schema
  moments?: Moment[]; // Array of moments within this phase
}

// Moment type for new API v2 structure
export interface Moment {
  id?: number | string;
  name?: string;
  order?: number;
  created_at?: string;
  updated_at?: string;
  desc?: string;
  image?: string | null;
  components?: any[];
  component_ids?: string[]; // Updated to match API v2 schema
}

export interface Action {
  action_id?: string;
  organization_id?: string;
  activity_id?: string;
  name?: string;
  description?: string;
  sequence?: number;
  category?: string;
  link?: string;
  supported_by?: string;
  reference_screen?: string;
  delivery_epic?: string;
  additional_document_references?: string[];
  priority?: "high" | "medium" | "low";
  estimated_effort?: string;
  dependencies?: string[];
  usage_count?: number;
  last_used?: string;
  impact_score?: number;
  status?: "active" | "inactive" | "archived";
  created_by?: {
    user_id: string;
    name: string;
    profile_image_url: string;
    timestamp: string;
  };
  updated_by?: {
    user_id: string;
    name: string;
    profile_image_url: string;
    timestamp: string;
  };
}

// Activity type for new API v2 structure - matches API client Activity/ActivityDetails
export interface Activity {
  activity_id?: string;
  organization_id?: string;
  moment_id?: string;
  name?: string;
  description?: string;
  activity_type?: "customer" | "front_stage" | "back_stage" | "system";
  estimated_duration?: string;
  complexity_level?: "low" | "medium" | "high";
  personas?: string[];
  sequence?: number;
  status?: "active" | "inactive" | "archived";
  created_by?: {
    user_id: string;
    name: string;
    profile_image_url: string;
    timestamp: string;
  };
  updated_by?: {
    user_id: string;
    name: string;
    profile_image_url: string;
    timestamp: string;
  };
  action_details?: Action[];
}

export interface BuildingBlock {
  connection_id?: number;
  connection_type?: number;
  phase?: number;
  id?: number;
  type?: number | any;
  sub_type?: number | null | any;
  role?: number;
  brand?: number | null;
  segment?: number | null;
  name?: string;
  desc?: string;
  order?: number;
  vote?: number;
  content?: any | null;
  created_at?: string;
  updated_at?: string;
  typeObject?: any;
  subTypeObject?: any;
}

export interface MapRole {
  id: number;
  name: string;
  image: string | null;
  desc: string;
  order: number;
  map_role_id: number;
  type: string;
}

export interface RowGroup {
  id: string;
  name: string;
  type: "story" | "action" | "activity" | "block" | "system";
  expandable: boolean;
  subRows: SubRow[];
  component?: React.ComponentType<CellProps>;
}

export interface SubRow {
  id: string;
  name: string;
  parentId: string;
  type: string;
}

export interface VisibleRow extends Partial<SubRow> {
  id: string;
  name: string;
  type: string;
  parentId?: string;
  isFirstSubRow?: boolean;
  isLastSubRow?: boolean;
  parentSpan?: number;
  subRows?: SubRow[];
}

export interface CellProps {
  row: VisibleRow;
  phase: Phase;
  data?: Activity | BuildingBlock;
  actions?: Action[];
  activities?: Activity[];
  buildingBlocks?: BuildingBlock[];
  roles?: MapRole[];
}

export interface GridPosition {
  x: number;
  y: number;
}

export interface GridState {
  scale: number;
  scrollPos: GridPosition;
  mode: "pan" | "select";
  expandedRows: Record<string, boolean>;
  hiddenColumns: string[];
  hiddenRows: string[];
  selectedCells: Set<string>;
  isStoryMode: boolean;
}

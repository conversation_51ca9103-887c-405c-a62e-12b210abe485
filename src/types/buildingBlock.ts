export interface BuildingBlockSubType {
  id: number;
  name: string;
  desc: string;
  order: number;
  created_at: string;
  updated_at: string;
}

export interface BuildingBlockType {
  id: number;
  name: string;
  desc: string;
  order: number;
  created_at: string;
  updated_at: string;
  sub_types: BuildingBlockSubType[];
}

export interface FieldValidation {
  required: boolean;
}

export interface FieldValues {
  source?: string;
  options?: { name: string; value: string }[];
}

export interface Field {
  name: string;
  type: string;
  label: string;
  is_visible: boolean;
  description: string;
  validation: FieldValidation;
  values?: FieldValues;
  is_basic?: boolean;
  display_in_table?: boolean;
  display_order?: number | null;
}

export interface Fields {
  name: string;
  fields: Field[];
  description: string;
}

export interface BuildingBlockTypeDetail extends BuildingBlockType {
  organization: string;
  fields: Fields;
}

export interface BuildingBlock {
  id: number;
  type: BuildingBlockType;
  sub_type: BuildingBlockSubType;
  role: number | null;
  brand: number | null;
  segment: number | null;
  name: string;
  desc: string;
  order: number;
  vote?: number;
  created_at: string;
  updated_at: string;
}

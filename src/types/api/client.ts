import { make<PERSON><PERSON>, Zodios, type ZodiosOptions } from "@zodios/core";
import { z } from "zod";

const CognitoAuthRequest = z
  .object({
    ClientId: z.string(),
    AuthFlow: z.string(),
    AuthParameters: z
      .object({ USERNAME: z.string().email(), PASSWORD: z.string() })
      .passthrough(),
  })
  .passthrough();
const Organization = z
  .object({
    organization_id: z.string(),
    name: z.string(),
    industry: z.string(),
    subscription_tier: z.enum(["starter", "professional", "enterprise"]),
    settings: z
      .object({
        timezone: z.string(),
        locale: z.string(),
        data_retention_days: z.number().int(),
      })
      .passthrough(),
    status: z.enum(["active", "suspended", "archived"]),
    created_at: z.string().datetime({ offset: true }),
    updated_at: z.string().datetime({ offset: true }),
  })
  .passthrough();
const OrganizationDetails = Organization.and(
  z
    .object({
      business_structure: z
        .object({
          divisions: z.array(z.string()),
          channels: z.array(z.string()),
          products: z.array(z.string()),
          brands: z.array(z.string()),
        })
        .passthrough(),
      statistics: z
        .object({
          total_users: z.number().int(),
          total_maps: z.number().int(),
          total_personas: z.number().int(),
        })
        .passthrough(),
    })
    .passthrough()
);
const Error = z
  .object({
    error: z.string(),
    code: z.string(),
    message: z.string(),
    timestamp: z.string().datetime({ offset: true }),
  })
  .passthrough();
const OrganizationCreate = z
  .object({
    name: z.string(),
    industry: z.string(),
    subscription_tier: z
      .enum(["starter", "professional", "enterprise"])
      .optional()
      .default("starter"),
    settings: z
      .object({ timezone: z.string(), locale: z.string() })
      .passthrough()
      .optional(),
  })
  .passthrough();
const UserReference = z
  .object({
    user_id: z.string(),
    name: z.string(),
    profile_image_url: z.string().url(),
    timestamp: z.string().datetime({ offset: true }),
  })
  .passthrough();
const User = z
  .object({
    user_id: z.string(),
    organization_id: z.string(),
    profile: z
      .object({
        email: z.string().email(),
        first_name: z.string(),
        last_name: z.string(),
        display_name: z.string(),
        avatar_url: z.string().url(),
      })
      .passthrough(),
    access_control: z
      .object({
        role: z.enum(["admin", "writer", "reader"]),
        department: z.string(),
        permissions: z.array(z.string()),
      })
      .passthrough(),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const OrganizationUpdate = z
  .object({
    name: z.string(),
    industry: z.string(),
    settings: z
      .object({ timezone: z.string(), locale: z.string() })
      .passthrough(),
    business_structure: z
      .object({ divisions: z.array(z.string()), channels: z.array(z.string()) })
      .passthrough(),
  })
  .passthrough();
const UserCreate = z
  .object({
    email: z.string().email(),
    first_name: z.string(),
    last_name: z.string(),
    display_name: z.string().optional(),
    role: z.enum(["admin", "writer", "reader"]),
    department: z.string().optional(),
    permissions: z.array(z.string()).optional(),
  })
  .passthrough();
const UserDetails = User;
const UserUpdate = z
  .object({
    profile: z
      .object({
        first_name: z.string(),
        last_name: z.string(),
        display_name: z.string(),
        avatar_url: z.string().url(),
      })
      .passthrough(),
    access_control: z
      .object({
        role: z.enum(["admin", "writer", "reader"]),
        department: z.string(),
        permissions: z.array(z.string()),
      })
      .passthrough(),
  })
  .passthrough();
const CustomerGoal = z
  .object({
    customer_goal_id: z.string(),
    organization_id: z.string(),
    name: z.string(),
    description: z.string(),
    category: z.string(),
    priority: z.enum(["high", "medium", "low"]),
    status: z.enum(["active", "draft", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const CustomerGoalCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    category: z.string().optional(),
    priority: z.enum(["high", "medium", "low"]).optional().default("medium"),
  })
  .passthrough();
const CustomerGoalDetails = CustomerGoal.and(
  z
    .object({
      maps: z.array(
        z
          .object({ map_id: z.string(), name: z.string(), state: z.string() })
          .passthrough()
      ),
    })
    .passthrough()
);
const CustomerGoalUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    category: z.string(),
    priority: z.enum(["high", "medium", "low"]),
    status: z.enum(["active", "draft", "archived"]),
  })
  .passthrough();
const Map = z
  .object({
    map_id: z.string(),
    organization_id: z.string(),
    customer_goal: CustomerGoal,
    name: z.string(),
    description: z.string(),
    division: z.string(),
    state: z.enum(["draft", "review", "published", "archived"]),
    status: z.enum(["active", "inactive"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const MapCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    customer_goal_id: z.string(),
    division: z.string(),
    state: z.enum(["draft", "review", "published"]).optional().default("draft"),
  })
  .passthrough();
const Phase = z
  .object({
    phase_id: z.string(),
    organization_id: z.string(),
    map_id: z.string(),
    name: z.string(),
    description: z.string(),
    sequence: z.number().int(),
    components: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const Moment = z
  .object({
    moment_id: z.string(),
    organization_id: z.string(),
    name: z.string(),
    description: z.string(),
    image_url: z.string().url(),
    components: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const Activity = z
  .object({
    activity_id: z.string(),
    organization_id: z.string(),
    moment_id: z.string(),
    name: z.string(),
    description: z.string(),
    activity_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    estimated_duration: z.string(),
    complexity_level: z.enum(["low", "medium", "high"]),
    personas: z.array(z.string()),
    sequence: z.number().int().gte(1),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const Action = z
  .object({
    action_id: z.string(),
    organization_id: z.string(),
    activity_id: z.string(),
    name: z.string(),
    description: z.string(),
    sequence: z.number().int().gte(1),
    category: z.string(),
    link: z.string().url(),
    supported_by: z.string(),
    reference_screen: z.string(),
    delivery_epic: z.string(),
    additional_document_references: z.string(),
    priority: z.enum(["high", "medium", "low"]),
    estimated_effort: z.string(),
    usage_count: z.number().int(),
    last_used: z.string().datetime({ offset: true }),
    impact_score: z.number(),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const ActivityDetails = Activity.and(
  z.object({ action_details: z.array(Action) }).passthrough()
);
const ActivitiesByType = z
  .object({
    customer: z.array(ActivityDetails),
    front_stage: z.array(ActivityDetails),
    back_stage: z.array(ActivityDetails),
    system: z.array(ActivityDetails),
  })
  .passthrough();
const MomentDetails = Moment.and(
  z.object({ activities: ActivitiesByType }).passthrough()
);
const Persona = z
  .object({
    persona_id: z.string(),
    organization_id: z.string(),
    name: z.string(),
    persona_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    image_url: z.string().url(),
    tagline: z.string(),
    age: z.number().int(),
    role: z.string(),
    income: z.string(),
    location: z.string(),
    status_description: z.string(),
    motivations: z.array(z.string()),
    frustrations: z.array(z.string()),
    goals: z.array(z.string()),
    preferences: z.array(z.string()),
    description: z.string(),
    category: z.string(),
    vendor: z.string(),
    platform: z.string(),
    availability: z.string(),
    notes: z.string(),
    status: z.enum([
      "active",
      "inactive",
      "maintenance",
      "deprecated",
      "archived",
    ]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const PersonaDetails = Persona.and(
  z
    .object({
      moments_used: z.array(
        z
          .object({
            moment_id: z.string(),
            moment_name: z.string(),
            map_name: z.string(),
            activity_count: z.number().int(),
          })
          .passthrough()
      ),
    })
    .passthrough()
);
const Component = z
  .object({
    component_id: z.string(),
    organization_id: z.string(),
    numbering: z.string(),
    name: z.string(),
    description: z.string(),
    best_practices: z.string(),
    level: z.string(),
    framework: z.enum(["customer", "business"]),
    phases_used: z.array(z.string()),
    moments_used: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
    created_by: UserReference,
    updated_by: UserReference,
  })
  .passthrough();
const ComponentDetails = Component.and(
  z
    .object({
      usage_details: z.array(
        z
          .object({
            entity_type: z.enum(["phase", "moment"]),
            entity_id: z.string(),
            entity_name: z.string(),
            map_name: z.string(),
          })
          .passthrough()
      ),
    })
    .passthrough()
);
const MapDetails = Map.and(
  z
    .object({
      phases: z.array(
        Phase.and(z.object({ moment_ids: z.array(z.string()) }).passthrough())
      ),
      moments: z.array(MomentDetails),
      personas: z.array(PersonaDetails),
      components: z.array(ComponentDetails),
      statistics: z
        .object({
          total_phases: z.number().int(),
          total_moments: z.number().int(),
          total_actions: z.number().int(),
          total_personas: z.number().int(),
          total_components: z.number().int(),
        })
        .passthrough(),
    })
    .passthrough()
);
const MapUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    division: z.string(),
    state: z.enum(["draft", "review", "published", "archived"]),
    status: z.enum(["active", "inactive"]),
  })
  .passthrough();
const PhaseCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    sequence: z.number().int().gte(1).optional(),
    components: z.array(z.string()).optional(),
  })
  .passthrough();
const PhaseDetails = Phase.and(
  z.object({ moment_ids: z.array(z.string()) }).passthrough()
);
const PhaseUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    sequence: z.number().int(),
    components: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const postMomentsbatch_Body = z
  .object({ moment_ids: z.array(z.string()) })
  .passthrough();
const MomentCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    image_url: z.string().url().optional(),
    components: z.array(z.string()).optional(),
  })
  .passthrough();
const MomentUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    image_url: z.string().url(),
    components: z.array(z.string()),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const ActivityCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    activity_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    estimated_duration: z.string().optional(),
    complexity_level: z
      .enum(["low", "medium", "high"])
      .optional()
      .default("medium"),
    personas: z.array(z.string()).optional(),
    sequence: z.number().int().gte(1).optional(),
  })
  .passthrough();
const ActivityUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    activity_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    estimated_duration: z.string(),
    complexity_level: z.enum(["low", "medium", "high"]),
    personas: z.array(z.string()),
    sequence: z.number().int().gte(1),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const ActionCreate = z
  .object({
    name: z.string(),
    description: z.string(),
    sequence: z.number().int().gte(1),
    category: z.string(),
    link: z.string().url().optional(),
    supported_by: z.string().optional(),
    reference_screen: z.string().optional(),
    delivery_epic: z.string().optional(),
    additional_document_references: z.string().optional(),
    priority: z.enum(["high", "medium", "low"]).optional().default("medium"),
    estimated_effort: z.string().optional(),
  })
  .passthrough();
const ActionDetails = Action;
const ActionUpdate = z
  .object({
    name: z.string(),
    description: z.string(),
    sequence: z.number().int().gte(1),
    category: z.string(),
    link: z.string().url(),
    supported_by: z.string(),
    reference_screen: z.string(),
    delivery_epic: z.string(),
    additional_document_references: z.string(),
    priority: z.enum(["high", "medium", "low"]),
    estimated_effort: z.string(),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const PersonaCreate = z
  .object({
    name: z.string(),
    persona_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    image_url: z.string().url().optional(),
    tagline: z.string().optional(),
    age: z.number().int().gte(1).lte(120).optional(),
    role: z.string().optional(),
    income: z.string().optional(),
    location: z.string().optional(),
    status_description: z.string().optional(),
    motivations: z.array(z.string()).optional(),
    frustrations: z.array(z.string()).optional(),
    goals: z.array(z.string()).optional(),
    preferences: z.array(z.string()).optional(),
    description: z.string().optional(),
    category: z.string().optional(),
    vendor: z.string().optional(),
    platform: z.string().optional(),
    availability: z.string().optional(),
    notes: z.string().optional(),
  })
  .passthrough();
const PersonaUpdate = z
  .object({
    name: z.string(),
    persona_type: z.enum(["customer", "front_stage", "back_stage", "system"]),
    image_url: z.string().url(),
    tagline: z.string(),
    age: z.number().int(),
    role: z.string(),
    income: z.string(),
    location: z.string(),
    status_description: z.string(),
    motivations: z.array(z.string()),
    frustrations: z.array(z.string()),
    goals: z.array(z.string()),
    preferences: z.array(z.string()),
    description: z.string(),
    category: z.string(),
    vendor: z.string(),
    platform: z.string(),
    availability: z.string(),
    notes: z.string(),
    status: z.enum([
      "active",
      "inactive",
      "maintenance",
      "deprecated",
      "archived",
    ]),
  })
  .passthrough();
const ComponentCreate = z
  .object({
    numbering: z.string(),
    name: z.string(),
    description: z.string(),
    best_practices: z.string().optional(),
    level: z.string().optional(),
    framework: z.enum(["customer", "business"]).optional(),
  })
  .passthrough();
const ComponentUpdate = z
  .object({
    numbering: z.string(),
    name: z.string(),
    description: z.string(),
    best_practices: z.string(),
    level: z.string(),
    framework: z.enum(["customer", "business"]),
    status: z.enum(["active", "inactive", "archived"]),
  })
  .passthrough();
const postComponentsmatch_Body = z
  .object({
    text: z.string(),
    threshold: z.number().gte(0).lte(1).optional().default(0.7),
  })
  .passthrough();
const ComponentMatch = z
  .object({
    component_id: z.string(),
    component_name: z.string(),
    numbering: z.string(),
    similarity_score: z.number().gte(0).lte(1),
    description: z.string(),
  })
  .passthrough();
const postComponentsupload_Body = z
  .object({
    csv_file_location: z.string().url(),
    overwrite_existing: z.boolean().optional().default(false),
    validate_only: z.boolean().optional().default(false),
  })
  .passthrough();
const ComponentValidationError = z
  .object({
    row_number: z.number().int(),
    field: z.string(),
    value: z.string(),
    error_message: z.string(),
    error_code: z.enum([
      "required_field_missing",
      "invalid_format",
      "duplicate_numbering",
      "invalid_enum_value",
      "field_too_long",
    ]),
  })
  .passthrough();
const ComponentUploadResult = z
  .object({
    total_rows: z.number().int(),
    processed_rows: z.number().int(),
    created_components: z.number().int(),
    updated_components: z.number().int(),
    failed_rows: z.number().int(),
    validation_errors: z.array(ComponentValidationError),
    created_component_ids: z.array(z.string()),
    processing_time_ms: z.number().int(),
  })
  .passthrough();
const postAigenerateText_Body = z
  .object({
    content_type: z.enum([
      "customer_goal_description",
      "map_description",
      "phase_description",
      "moment_description",
      "activity_description",
      "action_description",
      "persona_description",
      "persona_goals",
      "persona_frustrations",
    ]),
    context: z.object({}).passthrough(),
    tone: z
      .enum(["professional", "casual", "technical", "friendly"])
      .optional()
      .default("professional"),
    length: z.enum(["short", "medium", "long"]).optional().default("medium"),
  })
  .passthrough();
const postAigenerateImage_Body = z
  .object({
    image_type: z.enum(["persona_avatar", "map_illustration", "phase_icon"]),
    parameters: z.object({}).passthrough(),
    style: z
      .enum(["professional", "modern", "minimalist", "colorful"])
      .optional()
      .default("professional"),
  })
  .passthrough();

// Export TypeScript types from Zod schemas
// Authentication
export type LoginRequest = z.infer<typeof CognitoAuthRequest>;
export type ApiError = z.infer<typeof Error>;

// Organizations
export type Organization = z.infer<typeof Organization>;
export type OrganizationCreate = z.infer<typeof OrganizationCreate>;
export type OrganizationUpdate = z.infer<typeof OrganizationUpdate>;
export type OrganizationDetails = z.infer<typeof OrganizationDetails>;

// Users
export type UserReference = z.infer<typeof UserReference>;
export type UserProfile = z.infer<typeof User>;
export type UserCreate = z.infer<typeof UserCreate>;
export type UserUpdate = z.infer<typeof UserUpdate>;
export type UserDetails = z.infer<typeof UserDetails>;

// Customer Goals
export type CustomerGoal = z.infer<typeof CustomerGoal>;
export type CustomerGoalCreate = z.infer<typeof CustomerGoalCreate>;
export type CustomerGoalUpdate = z.infer<typeof CustomerGoalUpdate>;
export type CustomerGoalDetails = z.infer<typeof CustomerGoalDetails>;

// Maps
export type Map = z.infer<typeof Map>;
export type MapCreate = z.infer<typeof MapCreate>;
export type MapUpdate = z.infer<typeof MapUpdate>;
export type MapDetails = z.infer<typeof MapDetails>;

// Phases
export type Phase = z.infer<typeof Phase>;
export type PhaseCreate = z.infer<typeof PhaseCreate>;
export type PhaseUpdate = z.infer<typeof PhaseUpdate>;
export type PhaseDetails = z.infer<typeof PhaseDetails>;

// Moments
export type Moment = z.infer<typeof Moment>;
export type MomentCreate = z.infer<typeof MomentCreate>;
export type MomentUpdate = z.infer<typeof MomentUpdate>;
export type MomentDetails = z.infer<typeof MomentDetails>;

// Activities
export type Activity = z.infer<typeof Activity>;
export type ActivityCreate = z.infer<typeof ActivityCreate>;
export type ActivityUpdate = z.infer<typeof ActivityUpdate>;
export type ActivityDetails = z.infer<typeof ActivityDetails>;
export type ActivitiesByType = z.infer<typeof ActivitiesByType>;

// Actions
export type Action = z.infer<typeof Action>;
export type ActionCreate = z.infer<typeof ActionCreate>;
export type ActionUpdate = z.infer<typeof ActionUpdate>;
export type ActionDetails = z.infer<typeof ActionDetails>;

// Personas
export type Persona = z.infer<typeof Persona>;
export type PersonaCreate = z.infer<typeof PersonaCreate>;
export type PersonaUpdate = z.infer<typeof PersonaUpdate>;
export type PersonaDetails = z.infer<typeof PersonaDetails>;

// Components
export type Component = z.infer<typeof Component>;
export type ComponentCreate = z.infer<typeof ComponentCreate>;
export type ComponentUpdate = z.infer<typeof ComponentUpdate>;
export type ComponentDetails = z.infer<typeof ComponentDetails>;
export type ComponentMatch = z.infer<typeof ComponentMatch>;
export type ComponentValidationError = z.infer<typeof ComponentValidationError>;
export type ComponentUploadResult = z.infer<typeof ComponentUploadResult>;

// Other
export type MomentsbatchRequest = z.infer<typeof postMomentsbatch_Body>;
export type ComponentsmatchRequest = z.infer<typeof postComponentsmatch_Body>;
export type ComponentsuploadRequest = z.infer<typeof postComponentsupload_Body>;
export type AigenerateTextRequest = z.infer<typeof postAigenerateText_Body>;
export type AigenerateImageRequest = z.infer<typeof postAigenerateImage_Body>;


// Re-export the API client from base
export { apiV2Client, default as default } from "@/api/v2/base";

export const schemas = {
  CognitoAuthRequest,
  Organization,
  OrganizationDetails,
  Error,
  OrganizationCreate,
  UserReference,
  User,
  OrganizationUpdate,
  UserCreate,
  UserDetails,
  UserUpdate,
  CustomerGoal,
  CustomerGoalCreate,
  CustomerGoalDetails,
  CustomerGoalUpdate,
  Map,
  MapCreate,
  Phase,
  Moment,
  Activity,
  Action,
  ActivityDetails,
  ActivitiesByType,
  MomentDetails,
  Persona,
  PersonaDetails,
  Component,
  ComponentDetails,
  MapDetails,
  MapUpdate,
  PhaseCreate,
  PhaseDetails,
  PhaseUpdate,
  postMomentsbatch_Body,
  MomentCreate,
  MomentUpdate,
  ActivityCreate,
  ActivityUpdate,
  ActionCreate,
  ActionDetails,
  ActionUpdate,
  PersonaCreate,
  PersonaUpdate,
  ComponentCreate,
  ComponentUpdate,
  postComponentsmatch_Body,
  ComponentMatch,
  postComponentsupload_Body,
  ComponentValidationError,
  ComponentUploadResult,
  postAigenerateText_Body,
  postAigenerateImage_Body,
};

const endpoints = makeApi([
  {
    method: "get",
    path: "/actions/:actionId",
    requestFormat: "json",
    parameters: [
      {
        name: "actionId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: ActionDetails,
  },
  {
    method: "put",
    path: "/actions/:actionId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ActionUpdate,
      },
      {
        name: "actionId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Action,
  },
  {
    method: "delete",
    path: "/actions/:actionId",
    requestFormat: "json",
    parameters: [
      {
        name: "actionId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/activities/:activityId",
    requestFormat: "json",
    parameters: [
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: ActivityDetails,
  },
  {
    method: "put",
    path: "/activities/:activityId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ActivityUpdate,
      },
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Activity,
  },
  {
    method: "delete",
    path: "/activities/:activityId",
    requestFormat: "json",
    parameters: [
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/activities/:activityId/actions",
    description: `Retrieve all actions within a specific activity`,
    requestFormat: "json",
    parameters: [
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.array(Action),
  },
  {
    method: "post",
    path: "/activities/:activityId/actions",
    description: `Create a new action within a specific activity`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ActionCreate,
      },
      {
        name: "activityId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Action,
  },
  {
    method: "post",
    path: "/ai/generate-image",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postAigenerateImage_Body,
      },
    ],
    response: z
      .object({ image_url: z.string().url(), thumbnail_url: z.string().url() })
      .passthrough(),
  },
  {
    method: "post",
    path: "/ai/generate-text",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postAigenerateText_Body,
      },
    ],
    response: z
      .object({
        generated_text: z.string(),
        metadata: z
          .object({ model_used: z.string(), token_count: z.number().int() })
          .passthrough(),
      })
      .passthrough(),
  },
  {
    method: "post",
    path: "/auth/login",
    description: `**Note: This endpoint uses the AWS Cognito server, not your main API server.**

Authenticate user with username and password using AWS Cognito.
Copy the &#x60;IdToken&#x60; from the response to use as Bearer token for other endpoints.

**Authentication Flow:**
1. Call this endpoint with username/password
2. Extract the &#x60;IdToken&#x60; from the response  
3. Use the &#x60;IdToken&#x60; as Bearer token for all other API calls
`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: CognitoAuthRequest,
      },
      {
        name: "Content-Type",
        type: "Header",
        schema: z.string().default("application/x-amz-json-1.1"),
      },
      {
        name: "X-Amz-Target",
        type: "Header",
        schema: z
          .string()
          .default("AWSCognitoIdentityProviderService.InitiateAuth"),
      },
    ],
    response: z.void(),
    errors: [
      {
        status: 400,
        description: `Invalid request parameters`,
        schema: z.void(),
      },
      {
        status: 401,
        description: `Invalid credentials`,
        schema: z.void(),
      },
      {
        status: 429,
        description: `Too many requests`,
        schema: z.void(),
      },
    ],
  },
  {
    method: "get",
    path: "/components",
    requestFormat: "json",
    response: z.array(Component),
  },
  {
    method: "post",
    path: "/components",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ComponentCreate,
      },
    ],
    response: Component,
  },
  {
    method: "get",
    path: "/components/:componentId",
    requestFormat: "json",
    parameters: [
      {
        name: "componentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: ComponentDetails,
  },
  {
    method: "put",
    path: "/components/:componentId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ComponentUpdate,
      },
      {
        name: "componentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Component,
  },
  {
    method: "delete",
    path: "/components/:componentId",
    requestFormat: "json",
    parameters: [
      {
        name: "componentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "post",
    path: "/components/match",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postComponentsmatch_Body,
      },
    ],
    response: z.array(ComponentMatch),
  },
  {
    method: "post",
    path: "/components/upload",
    description: `Upload components in bulk from a CSV file. The CSV file must contain the following columns:
- numbering (required): Component numbering identifier
- name (required): Component name
- description (required): Component description
- best_practices (optional): Best practices text
- level (optional): Level designation
- status (optional): Component status (defaults to &#x27;active&#x27;)

Example CSV format:
&#x60;&#x60;&#x60;
numbering,name,description,best_practices,level,status
C001,User Login,Customer authentication process,Use secure protocols,Basic,active
C002,Payment Processing,Handle payment transactions,Validate all inputs,Advanced,active
&#x60;&#x60;&#x60;
`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postComponentsupload_Body,
      },
    ],
    response: ComponentUploadResult,
    errors: [
      {
        status: 400,
        description: `Invalid CSV format or data`,
        schema: Error.and(
          z
            .object({ validation_errors: z.array(ComponentValidationError) })
            .passthrough()
        ),
      },
    ],
  },
  {
    method: "get",
    path: "/customer-goals",
    description: `Retrieves all customer goals for the current organization.
Customer goals are high-level objectives that customers want to achieve.
Each goal can have multiple journey maps associated with it.
`,
    requestFormat: "json",
    parameters: [
      {
        name: "priority",
        type: "Query",
        schema: z.enum(["high", "medium", "low"]).optional(),
      },
      {
        name: "status",
        type: "Query",
        schema: z.enum(["active", "draft", "archived"]).optional(),
      },
    ],
    response: z.array(CustomerGoal),
  },
  {
    method: "post",
    path: "/customer-goals",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: CustomerGoalCreate,
      },
    ],
    response: CustomerGoal,
  },
  {
    method: "get",
    path: "/customer-goals/:customer_goal_id",
    requestFormat: "json",
    parameters: [
      {
        name: "customer_goal_id",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: CustomerGoalDetails,
  },
  {
    method: "put",
    path: "/customer-goals/:customer_goal_id",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: CustomerGoalUpdate,
      },
      {
        name: "customer_goal_id",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: CustomerGoal,
  },
  {
    method: "delete",
    path: "/customer-goals/:customer_goal_id",
    requestFormat: "json",
    parameters: [
      {
        name: "customer_goal_id",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/maps",
    description: `Retrieves all journey maps for the current organization.
Maps are the core entities that connect customer goals to detailed journey phases.
`,
    requestFormat: "json",
    parameters: [
      {
        name: "customer_goal_id",
        type: "Query",
        schema: z.string().optional(),
      },
      {
        name: "state",
        type: "Query",
        schema: z.enum(["draft", "review", "published", "archived"]).optional(),
      },
    ],
    response: z.array(Map),
  },
  {
    method: "post",
    path: "/maps",
    description: `Creates a new journey map linked to a customer goal.
Maps organize the customer journey into phases, moments, and activities.
Requires &#x27;writer&#x27; or &#x27;admin&#x27; role.
`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: MapCreate,
      },
    ],
    response: Map,
    errors: [
      {
        status: 400,
        description: `Invalid request data`,
        schema: Error,
      },
      {
        status: 409,
        description: `Map name already exists`,
        schema: Error,
      },
    ],
  },
  {
    method: "get",
    path: "/maps/:mapId",
    description: `Retrieves comprehensive details of a specific journey map including:
- All phases with their sequences and components
- All moments across phases (deduplicated if shared)
- All personas referenced in the map
- All components used throughout the journey
- Nested activities grouped by type (customer, front_stage, back_stage, system)
- Detailed actions within each activity
- Summary statistics

This endpoint provides the complete journey structure for visualization and analysis.
`,
    requestFormat: "json",
    parameters: [
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: MapDetails,
  },
  {
    method: "put",
    path: "/maps/:mapId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: MapUpdate,
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Map,
  },
  {
    method: "delete",
    path: "/maps/:mapId",
    requestFormat: "json",
    parameters: [
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/maps/:mapId/phases",
    requestFormat: "json",
    parameters: [
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.array(Phase),
  },
  {
    method: "post",
    path: "/maps/:mapId/phases",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: PhaseCreate,
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Phase,
  },
  {
    method: "get",
    path: "/maps/:mapId/phases/:phaseId/moments",
    requestFormat: "json",
    parameters: [
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.array(Moment),
  },
  {
    method: "post",
    path: "/maps/:mapId/phases/:phaseId/moments",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: MomentCreate,
      },
      {
        name: "mapId",
        type: "Path",
        schema: z.string(),
      },
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Moment,
  },
  {
    method: "get",
    path: "/moments",
    requestFormat: "json",
    response: z.array(Moment),
  },
  {
    method: "get",
    path: "/moments/:momentId",
    requestFormat: "json",
    parameters: [
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: MomentDetails,
  },
  {
    method: "put",
    path: "/moments/:momentId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: MomentUpdate,
      },
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Moment,
  },
  {
    method: "delete",
    path: "/moments/:momentId",
    requestFormat: "json",
    parameters: [
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/moments/:momentId/activities",
    description: `Retrieve all activities for a moment, grouped by activity type`,
    requestFormat: "json",
    parameters: [
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: ActivitiesByType,
  },
  {
    method: "post",
    path: "/moments/:momentId/activities",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: ActivityCreate,
      },
      {
        name: "momentId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Activity,
  },
  {
    method: "post",
    path: "/moments/batch",
    description: `Retrieve multiple moments by providing an array of moment IDs in the request body`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: postMomentsbatch_Body,
      },
    ],
    response: z.array(MomentDetails),
  },
  {
    method: "get",
    path: "/organization",
    description: `Retrieves the current user&#x27;s organization details including business structure and statistics.
Returns 404 if the user has no organization (needs onboarding).
`,
    requestFormat: "json",
    response: OrganizationDetails,
    errors: [
      {
        status: 404,
        description: `User has no organization (needs onboarding)`,
        schema: Error,
      },
    ],
  },
  {
    method: "post",
    path: "/organization",
    description: `Creates a new organization for first-time users. This endpoint handles the onboarding flow where:
1. User has valid Cognito JWT token but no organization in our database
2. Creates organization + user record atomically
3. User becomes admin of the new organization
4. User ID in database matches Cognito sub claim
`,
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: OrganizationCreate,
      },
    ],
    response: z
      .object({
        organization: OrganizationDetails,
        user: User,
        message: z.string(),
      })
      .passthrough(),
    errors: [
      {
        status: 409,
        description: `User already has an organization`,
        schema: z
          .object({ error: z.string(), existing_organization_id: z.string() })
          .passthrough(),
      },
    ],
  },
  {
    method: "put",
    path: "/organization",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: OrganizationUpdate,
      },
    ],
    response: Organization,
  },
  {
    method: "get",
    path: "/personas",
    requestFormat: "json",
    response: z.array(Persona),
  },
  {
    method: "post",
    path: "/personas",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: PersonaCreate,
      },
    ],
    response: Persona,
  },
  {
    method: "get",
    path: "/personas/:personaId",
    requestFormat: "json",
    parameters: [
      {
        name: "personaId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: PersonaDetails,
  },
  {
    method: "put",
    path: "/personas/:personaId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: PersonaUpdate,
      },
      {
        name: "personaId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Persona,
  },
  {
    method: "delete",
    path: "/personas/:personaId",
    requestFormat: "json",
    parameters: [
      {
        name: "personaId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/personas/type/:personaType",
    requestFormat: "json",
    parameters: [
      {
        name: "personaType",
        type: "Path",
        schema: z.enum(["customer", "front_stage", "back_stage", "system"]),
      },
    ],
    response: z.array(Persona),
  },
  {
    method: "get",
    path: "/phases/:phaseId",
    requestFormat: "json",
    parameters: [
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: PhaseDetails,
  },
  {
    method: "put",
    path: "/phases/:phaseId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: PhaseUpdate,
      },
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: Phase,
  },
  {
    method: "delete",
    path: "/phases/:phaseId",
    requestFormat: "json",
    parameters: [
      {
        name: "phaseId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
  {
    method: "get",
    path: "/users",
    description: `Retrieves all users in the current organization.
Requires &#x27;admin&#x27; role to view all users.
Results include user profiles and access control information.
`,
    requestFormat: "json",
    parameters: [
      {
        name: "role",
        type: "Query",
        schema: z.enum(["admin", "writer", "reader"]).optional(),
      },
      {
        name: "department",
        type: "Query",
        schema: z.string().optional(),
      },
    ],
    response: z.array(User),
  },
  {
    method: "post",
    path: "/users",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: UserCreate,
      },
    ],
    response: User,
  },
  {
    method: "get",
    path: "/users/:userId",
    requestFormat: "json",
    parameters: [
      {
        name: "userId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: UserDetails,
  },
  {
    method: "put",
    path: "/users/:userId",
    requestFormat: "json",
    parameters: [
      {
        name: "body",
        type: "Body",
        schema: UserUpdate,
      },
      {
        name: "userId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: User,
  },
  {
    method: "delete",
    path: "/users/:userId",
    requestFormat: "json",
    parameters: [
      {
        name: "userId",
        type: "Path",
        schema: z.string(),
      },
    ],
    response: z.void(),
  },
]);

export const api = new Zodios("https://api.mulapin.com/v2", endpoints);

export function createApiClient(baseUrl: string, options?: ZodiosOptions) {
  return new Zodios(baseUrl, endpoints, options);
}

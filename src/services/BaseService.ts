// src/services/BaseService.ts
/**
 * @deprecated This service is deprecated. Use the new API v2 client instead.
 * Migration: Replace imports with `import { apiV2Client } from '@/api/v2/hooks'`
 * New API provides better type safety, error handling, and uses TanStack Query for caching.
 * 
 * Migration Guide:
 * - BaseService.get() -> apiV2Client.get()
 * - BaseService.post() -> apiV2Client.post()
 * - Use the new useQuery/useMutation hooks for better React integration
 * 
 * @see /src/api/v2/ for the new implementation
 */
import axios, { AxiosInstance, AxiosResponse } from "axios";

class BaseService {
  private api: AxiosInstance;

  constructor() {
    // Create an instance of axios with the base URL from environment variables
    this.api = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_URL, // API URL from .env file
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });

    // Only initialize on client-side
    if (typeof window !== "undefined") {
      this.initializeAuthToken();

      // Add request interceptor
      this.api.interceptors.request.use(
        (config) => {
          const token = localStorage.getItem("authToken");
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          return config;
        },
        (error) => {
          return Promise.reject(error);
        }
      );
    }
  }

  private initializeAuthToken() {
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("authToken");
      if (token) {
        this.setAuthToken(token);
      }
    }
  }

  // Helper to set the Bearer token
  setAuthToken(token: string) {
    this.api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  }

  // Clear the Bearer token
  clearAuthToken() {
    delete this.api.defaults.headers.common["Authorization"];
  }

  // Basic GET request
  async get<T>(url: string): Promise<AxiosResponse<T>> {
    return this.api.get<T>(url);
  }

  // Basic POST request
  async post<T>(url: string, data: any): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, data);
  }

  // POST request for file uploads (FormData)
  async postFormData<T>(url: string, formData: FormData): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, formData, {
      headers: {
        // Remove the default Content-Type to let browser set it with boundary
        "Content-Type": undefined,
      },
    });
  }

  // Basic PUT request
  async put<T>(url: string, data: any): Promise<AxiosResponse<T>> {
    return this.api.put<T>(url, data);
  }

  // Basic PATCH request
  async patch<T>(url: string, data: any): Promise<AxiosResponse<T>> {
    return this.api.patch<T>(url, data);
  }

  // Basic DELETE request
  async delete<T>(url: string): Promise<AxiosResponse<T>> {
    return this.api.delete<T>(url);
  }
}

const baseService = new BaseService();
export default baseService;

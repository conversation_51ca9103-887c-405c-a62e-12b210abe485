// src/services/AuthService.ts
/**
 * @deprecated This service is deprecated. Use the new auth hooks instead.
 * Migration: Replace with `import { useLogin, useLogout, useCurrentUser } from '@/api/v2/hooks'`
 * 
 * Migration Examples:
 * - authService.login(username, password) -> const loginMutation = useLogin(); loginMutation.mutate({email, password})
 * - authService.logout() -> const logoutMutation = useLogout(); logoutMutation.mutate()
 * - authService.isAuthenticated() -> const isAuth = useIsAuthenticated()
 * 
 * @see /src/api/v2/hooks/useAuth.ts for the new implementation
 */
import BaseService from './BaseService';

class AuthService {
  async login(username: string, password: string) {
    // Make API request to login endpoint
    const response = await BaseService.post<{ access: string }>('/token/', {
      username,
      password,
    });

    // Save token to localStorage
    const token = response.data.access;
    localStorage.setItem('authToken', token);
    localStorage.setItem('username', username);

    // Set the token in BaseService for future requests
    BaseService.setAuthToken(token);

    return response.data;
  }

  logout() {
    // Clear the auth token from localStorage and BaseService
    localStorage.removeItem('authToken');
    localStorage.removeItem('username');
    BaseService.clearAuthToken();
  }

  // Check if the user is authenticated
  isAuthenticated(): boolean {
    return !!localStorage.getItem('authToken');
  }
}

const authService = new AuthService();
export default authService;
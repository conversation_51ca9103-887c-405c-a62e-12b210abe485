/**
 * @deprecated This service is deprecated. Use the new customer goal hooks instead.
 * Migration: Replace with `import { useCustomerGoals, useCustomerGoal, useCreateCustomerGoal, useUpdateCustomerGoal } from '@/api/v2/hooks'`
 * 
 * Migration Examples:
 * - customerGoalService.getCustomerGoals() -> const { data: goals } = useCustomerGoals()
 * - customerGoalService.getCustomerGoal(id) -> const { data: goal } = useCustomerGoal(id)
 * - customerGoalService.createCustomerGoal(data) -> const createGoalMutation = useCreateCustomerGoal(); createGoalMutation.mutate(data)
 * 
 * @see /src/api/v2/hooks/useCustomerGoals.ts for the new implementation
 */
import BaseService from "./BaseService";

export interface CustomerGoal {
  id?: number;
  name: string;
  desc?: string;
  created_at?: string;
  updated_at?: string;
}

class CustomerGoalService {
  /**
   * Get all customer goals
   */
  async getCustomerGoals(): Promise<CustomerGoal[]> {
    const response = await BaseService.get("/customer-goals/");
    return response.data as CustomerGoal[];
  }

  /**
   * Get a specific customer goal by ID
   */
  async getCustomerGoal(id: number): Promise<CustomerGoal> {
    const response = await BaseService.get(`/customer-goals/${id}/`);
    return response.data as CustomerGoal;
  }

  /**
   * Create a new customer goal
   */
  async createCustomerGoal(goal: Omit<CustomerGoal, "id">): Promise<CustomerGoal> {
    const response = await BaseService.post("/customer-goals/", goal);
    return response.data as CustomerGoal;
  }

  /**
   * Update an existing customer goal
   */
  async updateCustomerGoal(id: number, data: Partial<CustomerGoal>): Promise<CustomerGoal> {
    const response = await BaseService.patch(`/customer-goals/${id}/`, data);
    return response.data as CustomerGoal;
  }

  /**
   * Delete a customer goal
   */
  async deleteCustomerGoal(id: number): Promise<void> {
    await BaseService.delete(`/customer-goals/${id}/`);
  }
}

const customerGoalService = new CustomerGoalService();
export default customerGoalService;

/**
 * @deprecated This service is deprecated. Use the new component hooks instead.
 * Migration: Replace with `import { useComponents, useComponent, useCreateComponent, useUpdateComponent, useComponentMatch } from '@/api/v2/hooks'`
 * 
 * Migration Examples:
 * - componentService.getComponents() -> const { data: components } = useComponents()
 * - componentService.getComponent(id) -> const { data: component } = useComponent(id)
 * - componentService.createComponent(data) -> const createComponentMutation = useCreateComponent(); createComponentMutation.mutate(data)
 * - componentService.searchComponents(query) -> const { data: matches } = useComponentMatch(query)
 * 
 * @see /src/api/v2/hooks/useComponents.ts for the new implementation
 */
import BaseService from './BaseService';

interface GetComponentsParams {
  framework?: 'business' | 'customer';
  query?: string;
}

class ComponentService {
  async uploadComponentFile(file: File, frameworkId: string) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('framework_id', frameworkId);

    const response = await BaseService.post('/component/upload/', formData);

    return response.data;
  }

  async getComponents(params?: GetComponentsParams) {
    const searchParams = new URLSearchParams();
    
    // Always include base query param
    searchParams.append('query', params?.query || '');
    
    if (params?.framework) {
      searchParams.append('framework', params.framework);
    }

    const url = `/component/?${searchParams.toString()}`;
    const response = await BaseService.get(url);
    return response.data;
  }
}

const componentService = new ComponentService();
export default componentService;
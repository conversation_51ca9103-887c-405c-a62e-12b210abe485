/**
 * @deprecated This service is deprecated. Building blocks have been replaced with Activities and Actions in API v2.
 * Migration: Replace with activity/action hooks from the new API
 * 
 * Migration Path:
 * - Building blocks are now organized as Activities (customer, front_stage, back_stage, system)
 * - Each activity contains Actions
 * - Use moment-specific activity hooks to manage the new structure
 * 
 * @see /src/api/v2/hooks/useMaps.ts for moment and activity management
 * @see OpenAPI schema for the new Activities and Actions structure
 */
import { BuildingBlock, BuildingBlockType, BuildingBlockTypeDetail } from "../types/buildingBlock";
import BaseService from "./BaseService";

interface CreateBuildingBlockParams {
  sub_type: number;
  brand?: number;
  segment?: number;
  role?: number;
  name: string;
  desc: string;
  order?: number;
}

interface UploadBuildingBlockResponse {
  task_id: string;
  message: string;
  file_name: string;
}

interface TaskStatusResponse {
  task_id: string;
  status: string;
  ready: boolean;
  message: string;
}

class BuildingBlockService {
  /**
   * Get all building block types
   * @returns List of building block types with their sub-types
   */
  async getBuildingBlockTypes(): Promise<BuildingBlockType[]> {
    const response = await BaseService.get("/building-block-types/");
    return response.data as BuildingBlockType[];
  }

  /**
   * Get detailed information about a specific building block type
   * @param typeId The ID of the building block type
   * @param subTypeId Optional sub-type ID to filter results
   * @returns Detailed information about the building block type
   */
  async getBuildingBlockTypeDetail(
    typeId: number,
    subTypeId?: number
  ): Promise<BuildingBlockTypeDetail> {
    let url = `/building-block-types/${typeId}/`;
    if (subTypeId) {
      url += `?subtype=${subTypeId}`;
    }
    const response = await BaseService.get(url);
    return response.data as BuildingBlockTypeDetail;
  }

  /**
   * Get building blocks for a specific type and optional sub-type
   * @param typeId The ID of the building block type
   * @param subTypeId Optional sub-type ID to filter results
   * @returns List of building blocks
   */
  async getBuildingBlocks(typeId: number, subTypeId?: number): Promise<BuildingBlock[]> {
    let url = `/building-block-types/${typeId}/blocks/`;
    if (subTypeId) {
      url += `?sub_type=${subTypeId}`;
    }
    const response = await BaseService.get(url);
    return response.data as BuildingBlock[];
  }

  /**
   * Create a new building block
   * @param typeId The ID of the building block type
   * @param data The building block data
   * @returns The created building block
   */
  async createBuildingBlock(
    typeId: number,
    data: CreateBuildingBlockParams
  ): Promise<BuildingBlock> {
    const response = await BaseService.post(`/building-block-types/${typeId}/blocks/`, data);
    return response.data as BuildingBlock;
  }

  /**
   * Update an existing building block
   * @param typeId The ID of the building block type
   * @param blockId The ID of the building block to update
   * @param data The updated building block data
   * @returns The updated building block
   */
  async updateBuildingBlock(
    typeId: number,
    blockId: number,
    data: Partial<CreateBuildingBlockParams>
  ): Promise<BuildingBlock> {
    const response = await BaseService.patch(
      `/building-block-types/${typeId}/blocks/${blockId}/`,
      data
    );
    return response.data as BuildingBlock;
  }

  /**
   * Delete a building block
   * @param typeId The ID of the building block type
   * @param blockId The ID of the building block to delete
   */
  async deleteBuildingBlock(typeId: number, blockId: number): Promise<void> {
    await BaseService.delete(`/building-block-types/${typeId}/blocks/${blockId}/`);
  }

  /**
   * Upload a CSV file for building blocks
   * @param subTypeId The ID of the building block sub-type
   * @param file The CSV file to upload (actual File object, not path)
   * @returns Upload response with task ID and message
   */
  async uploadBuildingBlocks(subTypeId: number, file: File): Promise<UploadBuildingBlockResponse> {
    // Validate that we have an actual File object
    if (!(file instanceof File)) {
      throw new Error("Invalid file: Expected a File object, not a file path or string");
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith(".csv")) {
      throw new Error("Invalid file type: Only CSV files are supported");
    }

    // Validate file size (e.g., max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error("File too large: Maximum file size is 10MB");
    }

    // Create FormData with the actual file content and sub_type_id
    const formData = new FormData();
    formData.append("file", file, file.name); // Explicitly include filename
    formData.append("sub_type_id", subTypeId.toString()); // Add sub_type_id as string

    // Debug: Log FormData contents
    console.log("FormData created with file:", {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      subTypeId: subTypeId,
      formDataEntries: Array.from(formData.entries()).map(([key, value]) => ({
        key,
        value: value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value,
      })),
    });

    const response = await BaseService.postFormData(`/building-blocks/upload/`, formData);
    return response.data as UploadBuildingBlockResponse;
  }

  /**
   * Get the status of a building block upload task
   * @param taskId The task ID from the upload response
   * @returns Task status information
   */
  async getBuildingBlockTaskStatus(taskId: string): Promise<TaskStatusResponse> {
    const response = await BaseService.get(`/building-blocks/task-status/${taskId}/`);
    return response.data as TaskStatusResponse;
  }
}

const buildingBlockService = new BuildingBlockService();
export default buildingBlockService;

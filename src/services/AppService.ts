// src/services/AppService.ts
/**
 * @deprecated This service is deprecated. Use the new organization/user hooks instead.
 * Migration: Replace with `import { useCurrentUser } from '@/api/v2/hooks'`
 * 
 * Migration Guide:
 * - Common data is now available through user profile and organization endpoints
 * - Use organization-specific hooks for business structure data
 * 
 * @see /src/api/v2/hooks/useAuth.ts for user data
 */
import { CommonData } from '@/types/common';
import BaseService from './BaseService';

class AppService {
  async getCommonData(): Promise<CommonData> {
    const response = await BaseService.get("/common/");
    return response.data as CommonData;
  }
}

const appService = new AppService();
export default appService;
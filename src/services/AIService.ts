/**
 * @deprecated This service is deprecated. Use the new AI generation endpoints instead.
 * Migration: Use the new /ai/generate-text and /ai/generate-image endpoints through the API v2 client.
 * 
 * Migration Path:
 * - AI text generation is now available at POST /ai/generate-text
 * - AI image generation is now available at POST /ai/generate-image
 * - Better structured parameters and responses in the new API
 * 
 * @see OpenAPI schema for /ai/generate-text and /ai/generate-image endpoints
 * @see /src/api/v2/client.ts for making direct API calls
 */
import BaseService from "./BaseService";

interface AITextParams {
  code: "action" | "phase" | "persona goal" | "persona frustration" | "persona description";
  params: Record<string, any>;
}

interface AITextResponse {
  result: string;
  prompt?: string;
}

class AIService {
  /**
   * Generate AI text content
   * @param code The code identifying the type of text to generate
   * @param params The parameters for text generation
   * @returns The generated text response
   */
  async generateText(code: AITextParams["code"], params: AITextParams["params"]): Promise<string> {
    const payload: AITextParams = {
      code,
      params: { ...params, organization: { industry: "banking" } },
    };
    const response = await BaseService.post<AITextResponse>("/ai/text/", payload);
    return response.data.result;
  }
}

const aiService = new AIService();
export default aiService;

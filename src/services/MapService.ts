/**
 * @deprecated This service is deprecated. Use the new map hooks instead.
 * Migration: Replace with `import { useMaps, useMap, useCreateMap, useUpdateMap } from '@/api/v2/hooks'`
 * 
 * Migration Examples:
 * - mapService.getMaps() -> const { data: maps } = useMaps()
 * - mapService.getMapDetails(id) -> const { data: mapDetails } = useMap(id)
 * - mapService.postMap(map) -> const createMapMutation = useCreateMap(); createMapMutation.mutate(map)
 * - mapService.patchMap(map) -> const updateMapMutation = useUpdateMap(); updateMapMutation.mutate({mapId, updates})
 * 
 * @see /src/api/v2/hooks/useMaps.ts for the new implementation
 */
import { Action, BuildingBlock, Map, MapBuildingBlock, MapDetails, Phase } from "@/types/map";
import BaseService from "./BaseService";

export type ComponentMatch = {
  id: number;
  component?: string;
  numbering: string;
  similarity?: number;
  name?: string;
  description?: string;
};

export type FrameworkMatch = {
  framework_id: number;
  components: ComponentMatch[];
};

export type ImageGenerationResponse = {
  image_url: string;
};

class MapService {
  async getMaps(): Promise<Map[]> {
    const response = await BaseService.get("/maps/");
    return response.data as Map[];
  }

  async postMap(map: Partial<Map>): Promise<Map> {
    const response = await BaseService.post("/maps/", map);
    return response.data as Map;
  }

  async deleteMap(id: string): Promise<void> {
    await BaseService.delete(`/maps/${id}/`);
  }

  async getMapDetails(id: string): Promise<MapDetails> {
    const response = await BaseService.get(`/maps/${id}/`);
    return response.data as MapDetails;
  }

  async patchMap(map: Partial<Map>): Promise<Map> {
    const response = await BaseService.patch(`/maps/${map.id}/`, map);
    return response.data as Map;
  }

  async postComponentMatch(content: string): Promise<FrameworkMatch[]> {
    const response = await BaseService.post("/component/match/", {
      text: content,
    });
    return response.data as FrameworkMatch[];
  }
  async postGenerateImage(content: string): Promise<string> {
    const response = await BaseService.post("/component/image/", {
      text: content,
    });
    return (response.data as ImageGenerationResponse).image_url;
  }

  async postPhase(phase: Phase, mapId: string): Promise<Phase> {
    const response = await BaseService.post(`/maps/${mapId}/phases/`, phase);
    return response.data as Phase;
  }

  async patchPhase(phase: Phase, mapId: string): Promise<Phase> {
    const response = await BaseService.patch(`/maps/${mapId}/phases/${phase.id}/`, {
      components: phase.component_ids,
      name: phase.name,
      desc: phase.desc,
      image: phase.image,
      order: phase.order,
    });
    return response.data as Phase;
  }

  async deletePhase(phaseId: number, mapId: string): Promise<void> {
    await BaseService.delete(`/maps/${mapId}/phases/${phaseId}/`);
  }

  async postAction(action: Action, mapId: string): Promise<Action> {
    const response = await BaseService.post(`/maps/${mapId}/actions/`, action);
    return response.data as Action;
  }

  async patchAction(action: Action, mapId: string): Promise<Action> {
    const response = await BaseService.patch(`/maps/${mapId}/actions/${action.id}/`, action);
    return response.data as Action;
  }

  async deleteAction(actionId: string, mapId: string): Promise<void> {
    await BaseService.delete(`/maps/${mapId}/actions/${actionId}/`);
  }

  async getBuildingBlocks(mapId: string): Promise<BuildingBlock[]> {
    const response = await BaseService.get(`/maps/${mapId}/blocks/`);
    // Extract the block from each item in the response
    const mapBuildingBlocks = response.data as MapBuildingBlock[];
    // Return only the blocks, filtering out any undefined blocks
    return mapBuildingBlocks
      .map((item) => item.block)
      .filter((block): block is BuildingBlock => block !== undefined);
  }

  async postBuildingBlock(buildingBlock: BuildingBlock, mapId: string): Promise<BuildingBlock> {
    const response = await BaseService.post(`/maps/${mapId}/blocks/`, buildingBlock);
    return response.data as BuildingBlock;
  }

  async deleteBuildingBlock(connectionId: number, mapId: string): Promise<void> {
    await BaseService.delete(`/maps/${mapId}/blocks/${connectionId}/`);
  }
}

const mapService = new MapService();
export default mapService;

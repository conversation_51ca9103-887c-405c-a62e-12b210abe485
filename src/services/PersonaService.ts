/**
 * @deprecated This service is deprecated. Use the new persona hooks instead.
 * Migration: Replace with `import { usePersonas, usePersona, useCreatePersona, useUpdatePersona } from '@/api/v2/hooks'`
 * 
 * Migration Examples:
 * - personaService.getPersonas() -> const { data: personas } = usePersonas()
 * - personaService.getPersona(id) -> const { data: persona } = usePersona(id)
 * - personaService.createPersona(data) -> const createPersonaMutation = useCreatePersona(); createPersonaMutation.mutate(data)
 * 
 * @see /src/api/v2/hooks/usePersonas.ts for the new implementation
 */
import { PersonaData, PersonaGroup, UpdateRoleData } from "@/types/persona";
import BaseService from "./BaseService";

interface GenerateImageParams {
  title: string;
  age: number;
  gender: string;
  role: {
    id: number;
    age: number;
  };
}

class PersonaService {
  async getRoles(): Promise<PersonaData[]> {
    const response = await BaseService.get("/roles/");
    return response.data as PersonaData[];
  }

  async getGroupedRoles(): Promise<PersonaGroup> {
    const response = await BaseService.get("/roles/grouped/");
    return response.data as PersonaGroup;
  }

  async createRole(role: any): Promise<PersonaData> {
    const response = await BaseService.post("/roles/", role);
    return response.data as PersonaData;
  }

  async getRole(id: number): Promise<PersonaData> {
    const response = await BaseService.get(`/roles/${id}/`);
    return response.data as PersonaData;
  }

  async updateRole(id: number, data: Partial<UpdateRoleData>): Promise<PersonaData> {
    const response = await BaseService.patch(`/roles/${id}/`, data);
    return response.data as PersonaData;
  }

  async deleteRole(id: number): Promise<void> {
    await BaseService.delete(`/roles/${id}/`);
  }

  async generateImage(params: GenerateImageParams): Promise<any> {
    const payload = {
      code: "role",
      params: params,
    };
    const response = await BaseService.post("/ai/image/", payload);
    return response.data;
  }
}

const personaService = new PersonaService();
export default personaService;

import { createTheme, responsiveFontSizes, Theme, ThemeOptions } from "@mui/material/styles";
import {
  colors,
  components,
  shadows,
  shape,
  spacing,
  transitions,
  typography,
  zIndex,
} from "./design-tokens";

// Extend the Palette interface to include 'map' and custom text properties
declare module "@mui/material/styles" {
  interface Palette {
    map: {
      header: string;
      cell: string;
      note: string;
      phase: string;
      oddColBackground: string;
      evenColBackground: string;
    };
    nodeMap: {
      root: string;
      level1: string;
      level2: string;
      level3: string;
      map: string;
      phase: string;
      buildingBlock: string;
      mapComponentLink: string;
      phaseComponentLink: string;
      componentNode: string;
    };
  }
  interface TypeText {
    contrast: string;
  }
  interface TypeTextOptions {
    contrast?: string;
  }
  interface PaletteOptions {
    map?: {
      header?: string;
      cell?: string;
      note?: string;
      phase?: string;
      oddColBackground?: string;
      evenColBackground?: string;
    };
    nodeMap?: {
      root?: string;
      level1?: string;
      level2?: string;
      level3?: string;
      map?: string;
      phase?: string;
      buildingBlock?: string;
      mapComponentLink?: string;
      phaseComponentLink?: string;
      componentNode?: string;
    };
  }
}

/**
 * Creates a theme instance based on the provided mode (light or dark)
 * using the design tokens defined in design-tokens.ts
 */
export const getCustomTheme = (mode: "light" | "dark"): Theme => {
  // Create the base theme options
  const themeOptions: ThemeOptions = {
    palette: {
      mode,
      primary: colors.primary,
      secondary: colors.secondary,
      error: colors.error,
      warning: colors.warning,
      info: colors.info,
      success: colors.success,
      text: {
        primary: mode === "light" ? colors.text.primary : colors.darkText.primary,
        secondary: mode === "light" ? colors.text.secondary : colors.darkText.secondary,
        disabled: mode === "light" ? colors.text.disabled : colors.darkText.disabled,
        contrast: mode === "light" ? colors.text.contrast : colors.darkText.contrast,
      },
      background: {
        paper: mode === "light" ? colors.background.paper : colors.darkBackground.paper,
        default: mode === "light" ? colors.background.default : colors.darkBackground.default,
      },
      action: {
        active: mode === "light" ? colors.action.active : colors.darkAction.active,
        hover: mode === "light" ? colors.action.hover : colors.darkAction.hover,
        selected: mode === "light" ? colors.action.selected : colors.darkAction.selected,
        disabled: mode === "light" ? colors.action.disabled : colors.darkAction.disabled,
        disabledBackground:
          mode === "light"
            ? colors.action.disabledBackground
            : colors.darkAction.disabledBackground,
        focus: mode === "light" ? colors.action.focus : colors.darkAction.focus,
      },
      divider: mode === "light" ? colors.divider : colors.darkDivider,
      // Custom map colors
      map: colors.map,
      nodeMap: colors.nodeMap,
    },
    typography: typography as any, // Type assertion to fix typography type issues
    spacing,
    shape,
    transitions,
    zIndex,
    shadows: shadows as any, // Type assertion to fix shadows type issues
    components: components as any, // Type assertion to fix components type issues
  };

  // Create the theme
  let theme = createTheme(themeOptions);

  // Make typography responsive
  theme = responsiveFontSizes(theme);

  return theme;
};

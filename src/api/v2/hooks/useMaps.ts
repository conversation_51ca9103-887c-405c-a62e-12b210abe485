import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type {
  Map,
  MapCreate,
  MapUpdate,
  MapDetails,
} from "../../../types/api/client";
import { apiV2Client } from "../../../types/api/client";
import { mockMaps, mockMapDetails } from "../mocks/mockData";

// Query keys
export const mapKeys = {
  all: ["maps"] as const,
  lists: () => [...mapKeys.all, "list"] as const,
  list: (filters: string) => [...mapKeys.lists(), { filters }] as const,
  details: () => [...mapKeys.all, "detail"] as const,
  detail: (id: string) => [...mapKeys.details(), id] as const,
};

// Mock data toggle
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';

// Get all maps
export function useMaps() {
  return useQuery({
    queryKey: mapKeys.lists(),
    queryFn: async (): Promise<Map[]> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 500));
        return mockMaps;
      }
      const response = await apiV2Client.get<{ data: Map[] }>("/maps");
      return response.data.data;
    },
  });
}

// Get map details
export function useMap(mapId: string) {
  return useQuery({
    queryKey: mapKeys.detail(mapId),
    queryFn: async (): Promise<MapDetails> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 300));
        return mockMapDetails;
      }
      const response = await apiV2Client.get<{ data: MapDetails }>(`/maps/${mapId}`);
      return response.data.data;
    },
    enabled: !!mapId,
  });
}

// Create map mutation
export function useCreateMap() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (mapData: MapCreate): Promise<Map> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 800));
        const newMap: Map = {
          ...mockMaps[0],
          map_id: 'map_new_' + Date.now(),
          name: mapData.name,
          description: mapData.description,
          division: mapData.division,
          state: mapData.state || 'draft',
        };
        return newMap;
      }
      const response = await apiV2Client.post<{ data: Map }>("/maps", mapData);
      return response.data.data;
    },
    onSuccess: () => {
      // Invalidate maps list
      queryClient.invalidateQueries({ queryKey: mapKeys.lists() });
    },
  });
}

// Update map mutation
export function useUpdateMap() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ mapId, updates }: { mapId: string; updates: MapUpdate }): Promise<Map> => {
      const response = await apiV2Client.put<{ data: Map }>(`/maps/${mapId}`, updates);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: mapKeys.lists() });
      queryClient.invalidateQueries({ queryKey: mapKeys.detail(data.map_id) });
    },
  });
}

// Delete map mutation
export function useDeleteMap() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (mapId: string): Promise<void> => {
      await apiV2Client.delete(`/maps/${mapId}`);
    },
    onSuccess: (_, mapId) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: mapKeys.lists() });
      queryClient.removeQueries({ queryKey: mapKeys.detail(mapId) });
    },
  });
}


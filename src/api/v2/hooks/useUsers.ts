import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiV2Client } from '../../../types/api/client';
import type { 
  UserProfile,
  UserCreate,
  UserUpdate,
  UserDetails
} from '../../../types/api/client';

// Mock data toggle
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';

// Mock users data
const mockUsers: UserProfile[] = [
  {
    user_id: "usr_emily_rodriguez_001",
    organization_id: "acme-retail-group",
    profile: {
      email: "<EMAIL>",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      display_name: "<PERSON><PERSON>",
      avatar_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg"
    },
    access_control: {
      role: "admin",
      department: "Operations",
      permissions: ["maps.create", "maps.edit", "users.manage"]
    },
    created_by: {
      user_id: "system",
      name: "System",
      profile_image_url: "",
      timestamp: "2024-01-01T00:00:00Z"
    },
    updated_by: {
      user_id: "system",
      name: "System", 
      profile_image_url: "",
      timestamp: "2024-01-10T14:30:00Z"
    }
  },
  {
    user_id: "usr_alex_chen_002",
    organization_id: "acme-retail-group",
    profile: {
      email: "<EMAIL>",
      first_name: "Alex",
      last_name: "Chen",
      display_name: "Alex C.",
      avatar_url: "https://cdn.acmeretail.com/avatars/alex_chen.jpg"
    },
    access_control: {
      role: "writer",
      department: "Digital Marketing",
      permissions: ["maps.create", "maps.edit"]
    },
    created_by: {
      user_id: "usr_emily_rodriguez_001",
      name: "Emily Rodriguez",
      profile_image_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg",
      timestamp: "2024-01-05T10:00:00Z"
    },
    updated_by: {
      user_id: "usr_emily_rodriguez_001",
      name: "Emily Rodriguez",
      profile_image_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg", 
      timestamp: "2024-01-12T09:15:00Z"
    }
  }
];

// Query keys
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (filters: string) => [...userKeys.lists(), { filters }] as const,
  details: () => [...userKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeys.details(), id] as const,
};

// Get all users
export function useUsers(role?: string, department?: string) {
  return useQuery({
    queryKey: userKeys.list(`${role || 'all'}-${department || 'all'}`),
    queryFn: async (): Promise<UserProfile[]> => {
      if (USE_MOCK_DATA) {
        // Return filtered mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 400));
        let filteredUsers = mockUsers;
        if (role) {
          filteredUsers = filteredUsers.filter(user => user.access_control.role === role);
        }
        if (department) {
          filteredUsers = filteredUsers.filter(user => user.access_control.department === department);
        }
        return filteredUsers;
      }
      
      const params: Record<string, string> = {};
      if (role) params.role = role;
      if (department) params.department = department;
      
      const response = await apiV2Client.get<{ data: UserProfile[] }>('/users', params);
      return response.data.data;
    },
  });
}

// Get user details
export function useUser(userId: string) {
  return useQuery({
    queryKey: userKeys.detail(userId),
    queryFn: async (): Promise<UserDetails> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 300));
        const user = mockUsers.find(u => u.user_id === userId);
        if (!user) {
          throw new Error(`User with ID ${userId} not found`);
        }
        return user as UserDetails;
      }
      const response = await apiV2Client.get<{ data: UserDetails }>(`/users/${userId}`);
      return response.data.data;
    },
    enabled: !!userId,
  });
}

// Create user
export function useCreateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userData: UserCreate): Promise<UserProfile> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 800));
        const newUser: UserProfile = {
          user_id: 'usr_new_' + Date.now(),
          organization_id: "acme-retail-group",
          profile: {
            email: userData.email,
            first_name: userData.first_name,
            last_name: userData.last_name,
            display_name: userData.display_name || `${userData.first_name} ${userData.last_name.charAt(0)}.`,
            avatar_url: "https://cdn.acmeretail.com/avatars/default.jpg"
          },
          access_control: {
            role: userData.role,
            department: userData.department || "General",
            permissions: userData.permissions || []
          },
          created_by: {
            user_id: "usr_emily_rodriguez_001",
            name: "Emily Rodriguez",
            profile_image_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg",
            timestamp: new Date().toISOString()
          },
          updated_by: {
            user_id: "usr_emily_rodriguez_001",
            name: "Emily Rodriguez",
            profile_image_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg",
            timestamp: new Date().toISOString()
          }
        };
        return newUser;
      }
      const response = await apiV2Client.post<{ data: UserProfile }>('/users', userData);
      return response.data.data;
    },
    onSuccess: () => {
      // Invalidate users list
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
    },
  });
}

// Update user
export function useUpdateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, updates }: { userId: string; updates: UserUpdate }): Promise<UserProfile> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 600));
        const existingUser = mockUsers.find(u => u.user_id === userId);
        if (!existingUser) {
          throw new Error(`User with ID ${userId} not found`);
        }
        
        return {
          ...existingUser,
          profile: { ...existingUser.profile, ...updates.profile },
          access_control: { ...existingUser.access_control, ...updates.access_control },
          updated_by: {
            user_id: "usr_emily_rodriguez_001",
            name: "Emily Rodriguez",
            profile_image_url: "https://cdn.acmeretail.com/avatars/emily_rodriguez.jpg",
            timestamp: new Date().toISOString()
          }
        };
      }
      const response = await apiV2Client.put<{ data: UserProfile }>(`/users/${userId}`, updates);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeys.detail(data.user_id) });
    },
  });
}

// Delete user
export function useDeleteUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userId: string): Promise<void> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        return;
      }
      await apiV2Client.delete(`/users/${userId}`);
    },
    onSuccess: (_, userId) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      queryClient.removeQueries({ queryKey: userKeys.detail(userId) });
    },
  });
}
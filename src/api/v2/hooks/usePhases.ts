import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiV2Client } from '../../../types/api/client';
import type { 
  Phase,
  PhaseCreate,
  PhaseUpdate,
  PhaseDetails
} from '../../../types/api/client';
import { mockPhases } from '../mocks/mockData';

// Mock data toggle
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';

// Query keys
export const phaseKeys = {
  all: ['phases'] as const,
  lists: () => [...phaseKeys.all, 'list'] as const,
  list: (mapId: string) => [...phaseKeys.lists(), { mapId }] as const,
  details: () => [...phaseKeys.all, 'detail'] as const,
  detail: (id: string) => [...phaseKeys.details(), id] as const,
};

// Get phases for a map
export function useMapPhases(mapId: string) {
  return useQuery({
    queryKey: phaseKeys.list(mapId),
    queryFn: async (): Promise<Phase[]> => {
      if (USE_MOCK_DATA) {
        // Return filtered mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 350));
        return mockPhases.filter(phase => phase.map_id === mapId);
      }
      const response = await apiV2Client.get<{ data: Phase[] }>(`/maps/${mapId}/phases`);
      return response.data.data;
    },
    enabled: !!mapId,
  });
}

// Get phase details
export function usePhase(phaseId: string) {
  return useQuery({
    queryKey: phaseKeys.detail(phaseId),
    queryFn: async (): Promise<PhaseDetails> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 300));
        const phase = mockPhases.find(p => p.phase_id === phaseId);
        if (!phase) {
          throw new Error(`Phase with ID ${phaseId} not found`);
        }
        // Add moment_ids for PhaseDetails
        return {
          ...phase,
          moment_ids: ["moment_compare_prices_online", "moment_check_product_reviews"]
        };
      }
      const response = await apiV2Client.get<{ data: PhaseDetails }>(`/phases/${phaseId}`);
      return response.data.data;
    },
    enabled: !!phaseId,
  });
}

// Create phase
export function useCreatePhase() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ mapId, phaseData }: { mapId: string; phaseData: PhaseCreate }): Promise<Phase> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 800));
        const newPhase: Phase = {
          ...mockPhases[0],
          phase_id: 'phase_new_' + Date.now(),
          map_id: mapId,
          name: phaseData.name,
          description: phaseData.description,
          sequence: phaseData.sequence || 1,
          components: phaseData.components || [],
          created_by: mockPhases[0].created_by,
          updated_by: {
            ...mockPhases[0].updated_by,
            timestamp: new Date().toISOString()
          }
        };
        return newPhase;
      }
      const response = await apiV2Client.post<{ data: Phase }>(`/maps/${mapId}/phases`, phaseData);
      return response.data.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate phases list for the map
      queryClient.invalidateQueries({ queryKey: phaseKeys.list(variables.mapId) });
    },
  });
}

// Update phase
export function useUpdatePhase() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ phaseId, updates }: { phaseId: string; updates: PhaseUpdate }): Promise<Phase> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 600));
        const existingPhase = mockPhases.find(p => p.phase_id === phaseId);
        if (!existingPhase) {
          throw new Error(`Phase with ID ${phaseId} not found`);
        }
        
        return {
          ...existingPhase,
          ...updates,
          updated_by: {
            ...existingPhase.updated_by,
            timestamp: new Date().toISOString()
          }
        };
      }
      const response = await apiV2Client.put<{ data: Phase }>(`/phases/${phaseId}`, updates);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: phaseKeys.list(data.map_id) });
      queryClient.invalidateQueries({ queryKey: phaseKeys.detail(data.phase_id) });
    },
  });
}

// Delete phase
export function useDeletePhase() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ phaseId, mapId }: { phaseId: string; mapId: string }): Promise<void> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        return;
      }
      await apiV2Client.delete(`/phases/${phaseId}`);
    },
    onSuccess: (_, variables) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: phaseKeys.list(variables.mapId) });
      queryClient.removeQueries({ queryKey: phaseKeys.detail(variables.phaseId) });
    },
  });
}
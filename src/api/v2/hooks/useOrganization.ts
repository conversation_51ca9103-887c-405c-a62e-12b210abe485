import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiV2Client } from '../../../types/api/client';
import type { 
  Organization,
  OrganizationCreate,
  OrganizationUpdate,
  OrganizationDetails,
  UserProfile
} from '../../../types/api/client';

// Mock data toggle
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';

// Mock organization data
const mockOrganization: OrganizationDetails = {
  organization_id: "acme-retail-group",
  name: "Acme Retail Group",
  industry: "Retail & E-commerce",
  subscription_tier: "professional",
  settings: {
    timezone: "Australia/Sydney",
    locale: "en-US",
    data_retention_days: 2555,
  },
  business_structure: {
    divisions: ["Online", "Retail Stores", "Wholesale"],
    channels: ["Website", "Mobile App", "Physical Stores", "B2B Portal"],
    products: ["Electronics", "Home & Garden", "Fashion", "Sports"],
    brands: ["Acme Electronics", "Acme Home", "Acme Active"],
  },
  status: "active",
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-15T10:30:00Z",
  statistics: {
    total_users: 12,
    total_maps: 8,
    total_personas: 15,
  },
};

// Query keys
export const organizationKeys = {
  all: ['organization'] as const,
  details: () => [...organizationKeys.all, 'details'] as const,
};

// Get current organization
export function useOrganization() {
  return useQuery({
    queryKey: organizationKeys.details(),
    queryFn: async (): Promise<OrganizationDetails> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 600));
        return mockOrganization;
      }
      const response = await apiV2Client.get<{ data: OrganizationDetails }>('/organization');
      return response.data.data;
    },
  });
}

// Create organization (onboarding)
export function useCreateOrganization() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (orgData: OrganizationCreate): Promise<{ organization: OrganizationDetails; user: UserProfile }> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock response
        await new Promise(resolve => setTimeout(resolve, 1500));
        const mockUser: UserProfile = {
          user_id: "usr_john_doe_001",
          organization_id: mockOrganization.organization_id,
          profile: {
            email: "<EMAIL>",
            first_name: "John",
            last_name: "Doe",
            display_name: "John D.",
            avatar_url: "https://cdn.acmeretail.com/avatars/john_doe.jpg"
          },
          access_control: {
            role: "admin",
            department: "Executive",
            permissions: ["all"]
          },
          created_by: {
            user_id: "system",
            name: "System",
            profile_image_url: "",
            timestamp: new Date().toISOString()
          },
          updated_by: {
            user_id: "system", 
            name: "System",
            profile_image_url: "",
            timestamp: new Date().toISOString()
          }
        };
        
        return {
          organization: {
            ...mockOrganization,
            name: orgData.name,
            industry: orgData.industry,
            subscription_tier: orgData.subscription_tier || 'starter',
          },
          user: mockUser
        };
      }
      const response = await apiV2Client.post<{ data: { organization: OrganizationDetails; user: UserProfile } }>('/organization', orgData);
      return response.data.data;
    },
    onSuccess: () => {
      // Invalidate organization cache
      queryClient.invalidateQueries({ queryKey: organizationKeys.all });
    },
  });
}

// Update organization
export function useUpdateOrganization() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (updates: OrganizationUpdate): Promise<Organization> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock response
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          ...mockOrganization,
          ...updates,
          updated_at: new Date().toISOString(),
        };
      }
      const response = await apiV2Client.put<{ data: Organization }>('/organization', updates);
      return response.data.data;
    },
    onSuccess: () => {
      // Invalidate organization cache
      queryClient.invalidateQueries({ queryKey: organizationKeys.details() });
    },
  });
}
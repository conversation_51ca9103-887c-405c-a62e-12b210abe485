import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiV2Client } from '../../../types/api/client';
import type { 
  CustomerGoal,
  CustomerGoalCreate,
  CustomerGoalUpdate
} from '../../../types/api/client';
import { mockCustomerGoals } from '../mocks/mockData';

// Extended type for detailed customer goal response
export interface CustomerGoalDetails extends CustomerGoal {
  maps: Array<{
    map_id: string;
    name: string;
    state: string;
  }>;
}

// Query keys
export const customerGoalKeys = {
  all: ['customer-goals'] as const,
  lists: () => [...customerGoalKeys.all, 'list'] as const,
  list: (filters: string) => [...customerGoalKeys.lists(), { filters }] as const,
  details: () => [...customerGoalKeys.all, 'detail'] as const,
  detail: (id: string) => [...customerGoalKeys.details(), id] as const,
};

// Mock data toggle
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';

// Get all customer goals
export function useCustomerGoals() {
  return useQuery({
    queryKey: customerGoalKeys.lists(),
    queryFn: async (): Promise<CustomerGoal[]> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 400));
        return mockCustomerGoals;
      }
      const response = await apiV2Client.get<{ data: CustomerGoal[] }>('/customer-goals');
      return response.data.data;
    },
  });
}

// Get customer goal details
export function useCustomerGoal(goalId: string) {
  return useQuery({
    queryKey: customerGoalKeys.detail(goalId),
    queryFn: async (): Promise<CustomerGoalDetails> => {
      const response = await apiV2Client.get<{ data: CustomerGoalDetails }>(`/customer-goals/${goalId}`);
      return response.data.data;
    },
    enabled: !!goalId,
  });
}

// Create customer goal mutation
export function useCreateCustomerGoal() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (goalData: CustomerGoalCreate): Promise<CustomerGoal> => {
      const response = await apiV2Client.post<{ data: CustomerGoal }>('/customer-goals', goalData);
      return response.data.data;
    },
    onSuccess: () => {
      // Invalidate customer goals list
      queryClient.invalidateQueries({ queryKey: customerGoalKeys.lists() });
    },
  });
}

// Update customer goal mutation
export function useUpdateCustomerGoal() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ goalId, updates }: { goalId: string; updates: CustomerGoalUpdate }): Promise<CustomerGoal> => {
      const response = await apiV2Client.put<{ data: CustomerGoal }>(`/customer-goals/${goalId}`, updates);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: customerGoalKeys.lists() });
      queryClient.invalidateQueries({ queryKey: customerGoalKeys.detail(data.customer_goal_id) });
    },
  });
}

// Delete customer goal mutation
export function useDeleteCustomerGoal() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (goalId: string): Promise<void> => {
      await apiV2Client.delete(`/customer-goals/${goalId}`);
    },
    onSuccess: (_, goalId) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: customerGoalKeys.lists() });
      queryClient.removeQueries({ queryKey: customerGoalKeys.detail(goalId) });
    },
  });
}
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiV2Client } from '../../../types/api/client';
import type { 
  Component,
  ComponentCreate,
  ComponentUpdate,
  ComponentMatch,
  ComponentsmatchRequest
} from '../../../types/api/client';
import { mockComponents } from '../mocks/mockData';

// Extended type for detailed component response
export interface ComponentDetails extends Component {
  usage_details: Array<{
    entity_type: 'phase' | 'moment';
    entity_id: string;
    entity_name: string;
    map_name: string;
  }>;
}

// Query keys
export const componentKeys = {
  all: ['components'] as const,
  lists: () => [...componentKeys.all, 'list'] as const,
  list: (filters: string) => [...componentKeys.lists(), { filters }] as const,
  details: () => [...componentKeys.all, 'detail'] as const,
  detail: (id: string) => [...componentKeys.details(), id] as const,
  matches: (text: string) => [...componentKeys.all, 'match', text] as const,
};

// Mock data toggle
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';

// Get all components
export function useComponents() {
  return useQuery({
    queryKey: componentKeys.lists(),
    queryFn: async (): Promise<Component[]> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 400));
        return mockComponents;
      }
      const response = await apiV2Client.get<{ data: Component[] }>('/components');
      return response.data.data;
    },
  });
}

// Get component details
export function useComponent(componentId: string) {
  return useQuery({
    queryKey: componentKeys.detail(componentId),
    queryFn: async (): Promise<ComponentDetails> => {
      const response = await apiV2Client.get<{ data: ComponentDetails }>(`/components/${componentId}`);
      return response.data.data;
    },
    enabled: !!componentId,
  });
}

// Find matching components
export function useComponentMatch(text: string, threshold: number = 0.7, enabled: boolean = true) {
  return useQuery({
    queryKey: componentKeys.matches(text),
    queryFn: async (): Promise<ComponentMatch[]> => {
      const response = await apiV2Client.post<{ data: ComponentMatch[] }>('/components/match', {
        text,
        threshold,
      });
      return response.data.data;
    },
    enabled: enabled && !!text && text.length > 3, // Only search if text is meaningful
    staleTime: 5 * 60 * 1000, // 5 minutes - matches don't change often
  });
}

// Create component mutation
export function useCreateComponent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (componentData: ComponentCreate): Promise<Component> => {
      const response = await apiV2Client.post<{ data: Component }>('/components', componentData);
      return response.data.data;
    },
    onSuccess: () => {
      // Invalidate components list
      queryClient.invalidateQueries({ queryKey: componentKeys.lists() });
    },
  });
}

// Update component mutation
export function useUpdateComponent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ componentId, updates }: { componentId: string; updates: ComponentUpdate }): Promise<Component> => {
      const response = await apiV2Client.put<{ data: Component }>(`/components/${componentId}`, updates);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: componentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: componentKeys.detail(data.component_id) });
    },
  });
}

// Delete component mutation
export function useDeleteComponent() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (componentId: string): Promise<void> => {
      await apiV2Client.delete(`/components/${componentId}`);
    },
    onSuccess: (_, componentId) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: componentKeys.lists() });
      queryClient.removeQueries({ queryKey: componentKeys.detail(componentId) });
    },
  });
}

// Mutation for component matching (for one-time searches)
export function useComponentMatchMutation() {
  return useMutation({
    mutationFn: async (request: ComponentsmatchRequest): Promise<ComponentMatch[]> => {
      const response = await apiV2Client.post<{ data: ComponentMatch[] }>('/components/match', request);
      return response.data.data;
    },
  });
}
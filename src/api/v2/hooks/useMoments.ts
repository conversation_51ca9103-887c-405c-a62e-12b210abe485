import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiV2Client } from '../../../types/api/client';
import type { 
  Moment,
  MomentCreate,
  MomentUpdate,
  MomentDetails
} from '../../../types/api/client';
import { mockMomentDetails } from '../mocks/mockData';

// Mock data toggle
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCK_API === 'true';

// Query keys
export const momentKeys = {
  all: ['moments'] as const,
  lists: () => [...momentKeys.all, 'list'] as const,
  list: (filters: string) => [...momentKeys.lists(), { filters }] as const,
  details: () => [...momentKeys.all, 'detail'] as const,
  detail: (id: string) => [...momentKeys.details(), id] as const,
  byPhase: (mapId: string, phaseId: string) => [...momentKeys.all, 'phase', mapId, phaseId] as const,
  batch: (momentIds: string[]) => [...momentKeys.all, 'batch', momentIds.join(',')] as const,
};

// Get all organization moments
export function useMoments() {
  return useQuery({
    queryKey: momentKeys.lists(),
    queryFn: async (): Promise<Moment[]> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 400));
        return mockMomentDetails.map(({ activities, ...moment }) => moment);
      }
      const response = await apiV2Client.get<{ data: Moment[] }>('/moments');
      return response.data.data;
    },
  });
}

// Get moments for a specific phase
export function usePhaseMoments(mapId: string, phaseId: string) {
  return useQuery({
    queryKey: momentKeys.byPhase(mapId, phaseId),
    queryFn: async (): Promise<Moment[]> => {
      if (USE_MOCK_DATA) {
        // Return filtered mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 350));
        return mockMomentDetails
          .filter(moment => moment.moment_id.includes('compare_prices') || moment.moment_id.includes('check_product'))
          .map(({ activities, ...moment }) => moment);
      }
      const response = await apiV2Client.get<{ data: Moment[] }>(`/maps/${mapId}/phases/${phaseId}/moments`);
      return response.data.data;
    },
    enabled: !!(mapId && phaseId),
  });
}

// Get moment details
export function useMoment(momentId: string) {
  return useQuery({
    queryKey: momentKeys.detail(momentId),
    queryFn: async (): Promise<MomentDetails> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 300));
        const moment = mockMomentDetails.find(m => m.moment_id === momentId);
        if (!moment) {
          throw new Error(`Moment with ID ${momentId} not found`);
        }
        return moment;
      }
      const response = await apiV2Client.get<{ data: MomentDetails }>(`/moments/${momentId}`);
      return response.data.data;
    },
    enabled: !!momentId,
  });
}

// Get multiple moments by IDs (batch)
export function useMomentsBatch(momentIds: string[]) {
  return useQuery({
    queryKey: momentKeys.batch(momentIds),
    queryFn: async (): Promise<MomentDetails[]> => {
      if (USE_MOCK_DATA) {
        // Return filtered mock data with artificial delay
        await new Promise(resolve => setTimeout(resolve, 400));
        return mockMomentDetails.filter(moment => momentIds.includes(moment.moment_id));
      }
      const response = await apiV2Client.post<{ data: MomentDetails[] }>('/moments/batch', { moment_ids: momentIds });
      return response.data.data;
    },
    enabled: momentIds.length > 0,
  });
}

// Create moment
export function useCreateMoment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      mapId, 
      phaseId, 
      momentData 
    }: { 
      mapId: string; 
      phaseId: string; 
      momentData: MomentCreate 
    }): Promise<Moment> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 800));
        const newMoment: Moment = {
          moment_id: 'moment_new_' + Date.now(),
          organization_id: "acme-retail-2024",
          name: momentData.name,
          description: momentData.description,
          image_url: momentData.image_url || "https://cdn.acmeretail.com/moments/default.jpg",
          components: momentData.components || [],
          status: "active",
          created_by: mockMomentDetails[0].created_by,
          updated_by: {
            ...mockMomentDetails[0].updated_by,
            timestamp: new Date().toISOString()
          }
        };
        return newMoment;
      }
      const response = await apiV2Client.post<{ data: Moment }>(`/maps/${mapId}/phases/${phaseId}/moments`, momentData);
      return response.data.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate moments lists
      queryClient.invalidateQueries({ queryKey: momentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: momentKeys.byPhase(variables.mapId, variables.phaseId) });
    },
  });
}

// Update moment
export function useUpdateMoment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ momentId, updates }: { momentId: string; updates: MomentUpdate }): Promise<Moment> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 600));
        const existingMoment = mockMomentDetails.find(m => m.moment_id === momentId);
        if (!existingMoment) {
          throw new Error(`Moment with ID ${momentId} not found`);
        }
        
        const { activities, ...moment } = existingMoment;
        return {
          ...moment,
          ...updates,
          updated_by: {
            ...moment.updated_by,
            timestamp: new Date().toISOString()
          }
        };
      }
      const response = await apiV2Client.put<{ data: Moment }>(`/moments/${momentId}`, updates);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: momentKeys.lists() });
      queryClient.invalidateQueries({ queryKey: momentKeys.detail(data.moment_id) });
      // Also invalidate batch queries that might include this moment
      queryClient.invalidateQueries({ queryKey: momentKeys.all });
    },
  });
}

// Delete moment
export function useDeleteMoment() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (momentId: string): Promise<void> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        return;
      }
      await apiV2Client.delete(`/moments/${momentId}`);
    },
    onSuccess: (_, momentId) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: momentKeys.lists() });
      queryClient.removeQueries({ queryKey: momentKeys.detail(momentId) });
      // Also invalidate batch and phase queries
      queryClient.invalidateQueries({ queryKey: momentKeys.all });
    },
  });
}
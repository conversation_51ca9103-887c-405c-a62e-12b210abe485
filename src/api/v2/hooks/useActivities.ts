import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type {
  Action,
  ActionCreate,
  ActionUpdate,
  ActivitiesByType,
  Activity,
  ActivityCreate,
  ActivityDetails,
  ActivityUpdate,
} from "../../../types/api/client";
import { apiV2Client } from "../../../types/api/client";
import { mockActivitiesByType } from "../mocks/mockData";

// Mock data toggle
const USE_MOCK_DATA =
  process.env.NODE_ENV === "development" && process.env.NEXT_PUBLIC_USE_MOCK_API === "true";

// Query keys
export const activityKeys = {
  all: ["activities"] as const,
  lists: () => [...activityKeys.all, "list"] as const,
  list: (momentId: string) => [...activityKeys.lists(), { momentId }] as const,
  details: () => [...activityKeys.all, "detail"] as const,
  detail: (id: string) => [...activityKeys.details(), id] as const,
  byMoment: (momentId: string) => [...activityKeys.all, "moment", momentId] as const,
};

export const actionKeys = {
  all: ["actions"] as const,
  lists: () => [...actionKeys.all, "list"] as const,
  list: (activityId: string) => [...actionKeys.lists(), { activityId }] as const,
  details: () => [...actionKeys.all, "detail"] as const,
  detail: (id: string) => [...actionKeys.details(), id] as const,
};

// Get activities for a moment (grouped by type)
export function useMomentActivities(momentId: string) {
  return useQuery({
    queryKey: activityKeys.byMoment(momentId),
    queryFn: async (): Promise<ActivitiesByType> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise((resolve) => setTimeout(resolve, 400));
        return mockActivitiesByType;
      }
      const response = await apiV2Client.get<{ data: ActivitiesByType }>(
        `/moments/${momentId}/activities`
      );
      return response.data.data;
    },
    enabled: !!momentId,
  });
}

// Get activity details
export function useActivity(activityId: string) {
  return useQuery({
    queryKey: activityKeys.detail(activityId),
    queryFn: async (): Promise<ActivityDetails> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise((resolve) => setTimeout(resolve, 300));
        const activity = mockActivitiesByType.customer[0];
        if (!activity || activity.activity_id !== activityId) {
          throw new Error(`Activity with ID ${activityId} not found`);
        }
        return activity;
      }
      const response = await apiV2Client.get<{ data: ActivityDetails }>(
        `/activities/${activityId}`
      );
      return response.data.data;
    },
    enabled: !!activityId,
  });
}

// Create activity in moment
export function useCreateActivity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      momentId,
      activityData,
    }: {
      momentId: string;
      activityData: ActivityCreate;
    }): Promise<Activity> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise((resolve) => setTimeout(resolve, 800));
        const newActivity: Activity = {
          activity_id: "act_new_" + Date.now(),
          organization_id: "acme-retail-2024",
          moment_id: momentId,
          name: activityData.name || "Click to edit",
          description: activityData.description || "Click to edit",
          activity_type: activityData.activity_type,
          estimated_duration: activityData.estimated_duration || "Unknown",
          complexity_level: activityData.complexity_level || "medium",
          personas: activityData.personas || [],
          sequence: activityData.sequence || 1,
          status: "active",
          created_by: mockActivitiesByType.customer[0].created_by,
          updated_by: {
            ...mockActivitiesByType.customer[0].updated_by,
            timestamp: new Date().toISOString(),
          },
        };
        return newActivity;
      }
      const response = await apiV2Client.post<{ data: Activity }>(
        `/moments/${momentId}/activities`,
        activityData
      );
      return response.data.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate activities for the moment
      queryClient.invalidateQueries({ queryKey: activityKeys.byMoment(variables.momentId) });
    },
  });
}

// Update activity
export function useUpdateActivity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      activityId,
      updates,
    }: {
      activityId: string;
      updates: ActivityUpdate;
    }): Promise<Activity> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise((resolve) => setTimeout(resolve, 600));
        const existingActivity = mockActivitiesByType.customer[0];
        if (!existingActivity || existingActivity.activity_id !== activityId) {
          throw new Error(`Activity with ID ${activityId} not found`);
        }

        const { ...activity } = existingActivity;
        return {
          ...activity,
          ...updates,
          updated_by: {
            ...activity.updated_by,
            timestamp: new Date().toISOString(),
          },
        };
      }
      const response = await apiV2Client.put<{ data: Activity }>(
        `/activities/${activityId}`,
        updates
      );
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: activityKeys.byMoment(data.moment_id) });
      queryClient.invalidateQueries({ queryKey: activityKeys.detail(data.activity_id) });
    },
  });
}

// Delete activity
export function useDeleteActivity() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      activityId,
      momentId,
    }: {
      activityId: string;
      momentId: string;
    }): Promise<void> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500));
        return;
      }
      await apiV2Client.delete(`/activities/${activityId}`);
    },
    onSuccess: (_, variables) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: activityKeys.byMoment(variables.momentId) });
      queryClient.removeQueries({ queryKey: activityKeys.detail(variables.activityId) });
    },
  });
}

// Get actions for an activity
export function useActivityActions(activityId: string) {
  return useQuery({
    queryKey: actionKeys.list(activityId),
    queryFn: async (): Promise<Action[]> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise((resolve) => setTimeout(resolve, 350));
        return mockActivitiesByType.customer[0]?.action_details || [];
      }
      const response = await apiV2Client.get<{ data: Action[] }>(
        `/activities/${activityId}/actions`
      );
      return response.data.data;
    },
    enabled: !!activityId,
  });
}

// Get action details
export function useAction(actionId: string) {
  return useQuery({
    queryKey: actionKeys.detail(actionId),
    queryFn: async (): Promise<Action> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise((resolve) => setTimeout(resolve, 300));
        const action = mockActivitiesByType.customer[0]?.action_details?.[0];
        if (!action || action.action_id !== actionId) {
          throw new Error(`Action with ID ${actionId} not found`);
        }
        return action;
      }
      const response = await apiV2Client.get<{ data: Action }>(`/actions/${actionId}`);
      return response.data.data;
    },
    enabled: !!actionId,
  });
}

// Create action in activity
export function useCreateAction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      activityId,
      actionData,
    }: {
      activityId: string;
      actionData: ActionCreate;
    }): Promise<Action> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise((resolve) => setTimeout(resolve, 800));
        const mockAction = mockActivitiesByType.customer[0]?.action_details?.[0];
        const newAction: Action = {
          action_id: "action_new_" + Date.now(),
          organization_id: "acme-retail-2024",
          activity_id: activityId,
          name: actionData.name,
          description: actionData.description,
          sequence: actionData.sequence,
          category: actionData.category,
          link: actionData.link || "",
          supported_by: actionData.supported_by || "",
          reference_screen: actionData.reference_screen || "",
          delivery_epic: actionData.delivery_epic || "",
          additional_document_references: actionData.additional_document_references || "",
          priority: actionData.priority || "medium",
          estimated_effort: actionData.estimated_effort || "",
          usage_count: 0,
          last_used: new Date().toISOString(),
          impact_score: 0,
          status: "active",
          created_by: mockAction?.created_by || {
            user_id: "system",
            name: "System",
            profile_image_url: "",
            timestamp: new Date().toISOString(),
          },
          updated_by: mockAction?.updated_by || {
            user_id: "system",
            name: "System",
            profile_image_url: "",
            timestamp: new Date().toISOString(),
          },
        };
        return newAction;
      }
      const response = await apiV2Client.post<{ data: Action }>(
        `/activities/${activityId}/actions`,
        actionData
      );
      return response.data.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate actions for the activity
      queryClient.invalidateQueries({ queryKey: actionKeys.list(variables.activityId) });
    },
  });
}

// Update action
export function useUpdateAction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      actionId,
      updates,
    }: {
      actionId: string;
      updates: ActionUpdate;
    }): Promise<Action> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay and return mock data
        await new Promise((resolve) => setTimeout(resolve, 600));
        const existingAction = mockActivitiesByType.customer[0]?.action_details?.[0];
        if (!existingAction || existingAction.action_id !== actionId) {
          throw new Error(`Action with ID ${actionId} not found`);
        }

        return {
          ...existingAction,
          ...updates,
          updated_by: {
            ...existingAction.updated_by,
            timestamp: new Date().toISOString(),
          },
        };
      }
      const response = await apiV2Client.put<{ data: Action }>(`/actions/${actionId}`, updates);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: actionKeys.list(data.activity_id) });
      queryClient.invalidateQueries({ queryKey: actionKeys.detail(data.action_id) });
    },
  });
}

// Delete action
export function useDeleteAction() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      actionId,
      activityId,
    }: {
      actionId: string;
      activityId: string;
    }): Promise<void> => {
      if (USE_MOCK_DATA) {
        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500));
        return;
      }
      await apiV2Client.delete(`/actions/${actionId}`);
    },
    onSuccess: (_, variables) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: actionKeys.list(variables.activityId) });
      queryClient.removeQueries({ queryKey: actionKeys.detail(variables.actionId) });
    },
  });
}

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import type { Persona, PersonaCreate, PersonaUpdate } from "../../../types/api/client";
import { apiV2Client } from "../../../types/api/client";
import { mockPersonas } from "../mocks/mockData";

// Extended type for detailed persona response
export interface PersonaDetails extends Persona {
  moments_used: Array<{
    moment_id: string;
    moment_name: string;
    map_name: string;
    activity_count: number;
  }>;
}

// Query keys
export const personaKeys = {
  all: ["personas"] as const,
  lists: () => [...personaKeys.all, "list"] as const,
  list: (filters: string) => [...personaKeys.lists(), { filters }] as const,
  details: () => [...personaKeys.all, "detail"] as const,
  detail: (id: string) => [...personaKeys.details(), id] as const,
  byType: (type: string) => [...personaKeys.all, "type", type] as const,
};

// Mock data toggle
const USE_MOCK_DATA =
  process.env.NODE_ENV === "development" && process.env.NEXT_PUBLIC_USE_MOCK_API === "true";

// Get all personas
export function usePersonas() {
  return useQuery({
    queryKey: personaKeys.lists(),
    queryFn: async (): Promise<Persona[]> => {
      if (USE_MOCK_DATA) {
        // Return mock data with artificial delay
        await new Promise((resolve) => setTimeout(resolve, 350));
        return mockPersonas;
      }
      const response = await apiV2Client.get<{ data: Persona[] }>("/personas");
      return response.data.data;
    },
  });
}

// Get personas by type
export function usePersonasByType(
  personaType: "customer" | "front_stage" | "back_stage" | "system"
) {
  return useQuery({
    queryKey: personaKeys.byType(personaType),
    queryFn: async (): Promise<Persona[]> => {
      if (USE_MOCK_DATA) {
        // Return filtered mock data with artificial delay
        await new Promise((resolve) => setTimeout(resolve, 300));
        return mockPersonas.filter((persona) => persona.persona_type === personaType);
      }
      const response = await apiV2Client.get<{ data: Persona[] }>(`/personas/type/${personaType}`);
      return response.data.data;
    },
  });
}

// Get persona details
export function usePersona(personaId: string) {
  return useQuery({
    queryKey: personaKeys.detail(personaId),
    queryFn: async (): Promise<PersonaDetails> => {
      const response = await apiV2Client.get<{ data: PersonaDetails }>(`/personas/${personaId}`);
      return response.data.data;
    },
    enabled: !!personaId,
  });
}

// Create persona mutation
export function useCreatePersona() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (personaData: PersonaCreate): Promise<Persona> => {
      const response = await apiV2Client.post<{ data: Persona }>("/personas", personaData);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Invalidate personas list and type-specific list
      queryClient.invalidateQueries({ queryKey: personaKeys.lists() });
      queryClient.invalidateQueries({ queryKey: personaKeys.byType(data.persona_type) });
    },
  });
}

// Update persona mutation
export function useUpdatePersona() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      personaId,
      updates,
    }: {
      personaId: string;
      updates: PersonaUpdate;
    }): Promise<Persona> => {
      const response = await apiV2Client.put<{ data: Persona }>(`/personas/${personaId}`, updates);
      return response.data.data;
    },
    onSuccess: (data) => {
      // Update cached data
      queryClient.invalidateQueries({ queryKey: personaKeys.lists() });
      queryClient.invalidateQueries({ queryKey: personaKeys.detail(data.persona_id) });
      queryClient.invalidateQueries({ queryKey: personaKeys.byType(data.persona_type) });
    },
  });
}

// Delete persona mutation
export function useDeletePersona() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (personaId: string): Promise<void> => {
      await apiV2Client.delete(`/personas/${personaId}`);
    },
    onSuccess: (_, personaId) => {
      // Remove from cache
      queryClient.invalidateQueries({ queryKey: personaKeys.lists() });
      queryClient.removeQueries({ queryKey: personaKeys.detail(personaId) });

      // Invalidate all type-specific queries (since we don't know the type)
      queryClient.invalidateQueries({ queryKey: personaKeys.all });
    },
  });
}

import type {
  ActivitiesByType,
  Component,
  CustomerGoal,
  Map,
  MapDetails,
  MomentDetails,
  Persona,
  Phase,
  UserProfile,
  UserReference,
  Action,
} from "../../../types/api/client";

// Mock user data
export const mockUser: UserProfile = {
  user_id: "usr_john_doe_001",
  organization_id: "acme-retail-2024",
  profile: {
    email: "<EMAIL>",
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>",
    display_name: "<PERSON>",
    avatar_url: "https://cdn.acmeretail.com/avatars/john_doe.jpg",
  },
  access_control: {
    role: "writer",
    department: "Product Management",
    permissions: ["create_maps", "edit_personas", "view_reports"],
  },
};

// Mock user references
const mockUserRef: UserReference = {
  user_id: "usr_sarah_johnson_001",
  name: "<PERSON>",
  profile_image_url: "https://cdn.acmefinancial.com/avatars/sarah_johnson.jpg",
  timestamp: "2024-01-15T14:30:00Z",
};

// Mock customer goals
export const mockCustomerGoals: CustomerGoal[] = [
  {
    customer_goal_id: "cg_secure_home_loan_2024",
    organization_id: "acme-financial-2024",
    name: "Secure Home Loan",
    description:
      "Successfully obtain a home loan with favorable terms and minimal hassle",
    category: "Financial Services",
    priority: "high",
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
];

// Mock personas
export const mockPersonas: Persona[] = [
  {
    persona_id: "persona_home_buyer",
    organization_id: "acme-financial-2024",
    name: "Alex Chen - First Time Home Buyer",
    persona_type: "customer",
    image_url: "https://cdn.acmefinancial.com/personas/alex_chen.jpg",
    tagline: "Looking to purchase first home",
    age: 32,
    role: "Software Engineer",
    income: "$95,000 - $120,000",
    location: "San Francisco, CA",
    status_description: "Tech professional ready to buy first home",
    motivations: [
      "Secure favorable loan terms",
      "Understand the mortgage process",
      "Find reliable lender",
    ],
    frustrations: ["Complex paperwork", "Unclear requirements", "Long approval times"],
    goals: [
      "Get pre-approved for mortgage",
      "Complete application quickly",
      "Secure competitive interest rate",
    ],
    preferences: [
      "Digital application process",
      "Clear communication",
      "Fast response times",
    ],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    persona_id: "persona_loan_specialist",
    organization_id: "acme-financial-2024",
    name: "Sarah Martinez - Loan Specialist",
    persona_type: "front_stage",
    image_url: "https://cdn.acmefinancial.com/personas/sarah_martinez.jpg",
    tagline: "Helping customers navigate the loan process",
    age: 29,
    role: "Senior Loan Specialist",
    income: "$55,000 - $75,000",
    location: "San Francisco, CA",
    status_description: "Experienced loan officer with customer focus",
    motivations: [
      "Help customers achieve homeownership",
      "Provide excellent service",
      "Meet loan processing targets",
    ],
    frustrations: ["Incomplete applications", "System delays", "Complex regulations"],
    goals: [
      "Process applications efficiently",
      "Maintain high customer satisfaction",
      "Meet monthly loan targets",
    ],
    preferences: [
      "Streamlined workflow tools",
      "Clear customer communication",
      "Automated status updates",
    ],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    persona_id: "persona_underwriter",
    organization_id: "acme-financial-2024",
    name: "Michael Thompson - Senior Underwriter",
    persona_type: "back_stage",
    image_url: "https://cdn.acmefinancial.com/personas/michael_thompson.jpg",
    tagline: "Ensuring loan quality and compliance",
    age: 45,
    role: "Senior Underwriter & Risk Analyst",
    income: "$85,000 - $110,000",
    location: "San Francisco, CA",
    status_description: "Expert in loan assessment and risk management",
    motivations: [
      "Maintain loan quality standards",
      "Ensure regulatory compliance",
      "Support business growth",
    ],
    frustrations: ["Incomplete documentation", "Tight deadlines", "Changing regulations"],
    goals: [
      "Process loans accurately",
      "Meet turnaround time targets",
      "Minimize loan defaults",
    ],
    preferences: [
      "Comprehensive documentation",
      "Risk assessment tools",
      "Clear approval guidelines",
    ],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    persona_id: "persona_loan_system",
    organization_id: "acme-financial-2024",
    name: "Encompass Loan Origination System",
    persona_type: "system",
    image_url: "https://via.placeholder.com/80x80?text=LOS",
    tagline: "Comprehensive loan origination platform",
    age: 0,
    role: "Loan Origination System",
    income: "N/A",
    location: "Cloud Infrastructure",
    status_description: "Primary system for loan processing, document management, and compliance",
    motivations: ["Process loans efficiently", "Maintain compliance", "Ensure data accuracy"],
    frustrations: ["System downtime", "Data inconsistencies", "Integration issues"],
    goals: ["99.9% uptime", "Automated processing", "Regulatory compliance"],
    preferences: ["API integrations", "Real-time processing", "Secure data handling"],
    description:
      "Comprehensive loan origination platform that manages the entire mortgage process",
    category: "Loan Management",
    vendor: "ICE Mortgage Technology",
    platform: "Cloud Infrastructure",
    availability: "99.9% uptime SLA",
    notes: "Primary system for loan processing, document management, and compliance",
    usage_frequency: 24,
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    persona_id: "persona_busy_parent_customer",
    organization_id: "acme-retail-2024",
    name: "Maria Rodriguez - Busy Parent",
    persona_type: "customer",
    image_url: "https://via.placeholder.com/80x80?text=MR",
    tagline: "Juggling work and family life",
    age: 38,
    role: "Marketing Manager & Mother of 2",
    income: "$75,000 - $90,000",
    location: "Austin, TX",
    status_description: "Working mother looking for convenient shopping solutions",
    motivations: [
      "Save time on shopping",
      "Find best deals for family",
      "Efficient grocery planning",
    ],
    frustrations: ["Long checkout lines", "Out of stock items", "Limited shopping time"],
    goals: [
      "Complete shopping in under 30 minutes",
      "Stay within weekly budget",
      "Find healthy family meal options",
    ],
    preferences: [
      "Mobile app ordering",
      "Curbside pickup",
      "Digital coupons",
    ],
    description: "Busy working mother who values efficiency and convenience in her shopping experience",
    category: "Primary Shopper",
    vendor: "N/A",
    platform: "N/A",
    availability: "Evenings and weekends",
    notes: "Prefers digital solutions that save time",
    usage_frequency: 5,
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    persona_id: "persona_store_manager",
    organization_id: "acme-retail-2024",
    name: "David Kim - Store Manager",
    persona_type: "front_stage",
    image_url: "https://via.placeholder.com/80x80?text=DK",
    tagline: "Ensuring smooth store operations",
    age: 42,
    role: "Store Manager",
    income: "$60,000 - $80,000",
    location: "Austin, TX",
    status_description: "Experienced retail manager focused on customer satisfaction",
    motivations: [
      "Provide excellent customer service",
      "Meet sales targets",
      "Maintain efficient operations",
    ],
    frustrations: ["Staff shortages", "System outages", "Customer complaints"],
    goals: [
      "Achieve monthly sales goals",
      "Maintain customer satisfaction above 95%",
      "Optimize staff scheduling",
    ],
    preferences: [
      "Real-time inventory data",
      "Staff communication tools",
      "Customer feedback systems",
    ],
    description: "Dedicated store manager who ensures customers have a positive shopping experience",
    category: "Store Operations",
    vendor: "N/A",
    platform: "N/A",
    availability: "Business hours",
    notes: "Focuses on operational efficiency and customer satisfaction",
    usage_frequency: 8,
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    persona_id: "persona_inventory_coordinator",
    organization_id: "acme-retail-2024",
    name: "Lisa Chen - Inventory Coordinator",
    persona_type: "back_stage",
    image_url: "https://via.placeholder.com/80x80?text=LC",
    tagline: "Keeping shelves stocked and organized",
    age: 31,
    role: "Inventory Coordinator",
    income: "$45,000 - $55,000",
    location: "Austin, TX",
    status_description: "Detail-oriented professional managing inventory levels",
    motivations: [
      "Prevent stockouts",
      "Optimize inventory turnover",
      "Maintain accurate records",
    ],
    frustrations: ["Supplier delays", "Forecasting inaccuracies", "Manual inventory counts"],
    goals: [
      "Maintain 98% stock availability",
      "Reduce inventory waste",
      "Improve demand forecasting",
    ],
    preferences: [
      "Automated inventory tracking",
      "Real-time supplier updates",
      "Predictive analytics tools",
    ],
    description: "Meticulous inventory professional who ensures products are available when customers need them",
    category: "Supply Chain",
    vendor: "N/A",
    platform: "N/A",
    availability: "Business hours",
    notes: "Uses data-driven approaches to inventory management",
    usage_frequency: 7,
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    persona_id: "persona_pos_system",
    organization_id: "acme-retail-2024",
    name: "Square POS System",
    persona_type: "system",
    image_url: "https://via.placeholder.com/80x80?text=POS",
    tagline: "Processing transactions seamlessly",
    age: 0,
    role: "Point of Sale System",
    income: "N/A",
    location: "In-store terminals",
    status_description: "Reliable POS system handling customer transactions",
    motivations: ["Fast transaction processing", "Accurate payment handling", "Uptime reliability"],
    frustrations: ["Network connectivity issues", "Payment failures", "Hardware malfunctions"],
    goals: ["Sub-second transaction times", "99.99% uptime", "Zero payment errors"],
    preferences: ["Stable network connection", "Regular software updates", "Hardware maintenance"],
    description: "Advanced point-of-sale system that processes customer transactions efficiently",
    category: "Payment Processing",
    vendor: "Square Inc.",
    platform: "Cloud-based POS",
    availability: "24/7 during store hours",
    notes: "Integrates with inventory and customer management systems",
    usage_frequency: 12,
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
];

// Mock components
export const mockComponents: Component[] = [
  {
    component_id: "comp_mobile_app_003",
    organization_id: "acme-retail-2024",
    numbering: "APP-003",
    name: "Mobile Shopping Application",
    description: "Customer-facing mobile application for product browsing and purchasing",
    best_practices: "Ensure responsive design and fast loading times",
    count: 15,
    level: "Critical",
    framework: "customer",
    phases_used: ["phase_research_products", "phase_purchase_decision"],
    moments_used: ["moment_compare_prices_online", "moment_add_items_to_cart"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    component_id: "comp_inventory_system_001",
    organization_id: "acme-retail-2024",
    numbering: "INV-001",
    name: "Real-time Inventory System",
    description: "Backend system for tracking product availability",
    best_practices: "Maintain 99.9% uptime and sub-second response times",
    count: 8,
    level: "Essential",
    framework: "business",
    phases_used: ["phase_purchase_decision"],
    moments_used: ["moment_add_items_to_cart"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
];

// Mock phases
export const mockPhases: Phase[] = [
  {
    phase_id: "phase_explore",
    organization_id: "acme-financial-2024",
    map_id: "map_home_loan_journey",
    name: "EXPLORE",
    description: "Customer explores loan options and gathers information",
    sequence: 1,
    components: ["comp_loan_calculator", "comp_website"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    phase_id: "phase_apply",
    organization_id: "acme-financial-2024",
    map_id: "map_home_loan_journey",
    name: "APPLY",
    description: "Customer submits loan application and provides documentation",
    sequence: 2,
    components: ["comp_application_portal", "comp_document_system"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
  {
    phase_id: "phase_decision",
    organization_id: "acme-financial-2024",
    map_id: "map_home_loan_journey",
    name: "DECISION",
    description: "Loan is reviewed, assessed and decision is communicated",
    sequence: 3,
    components: ["comp_underwriting_system", "comp_approval_workflow"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
];

// Mock actions
const mockActions: Action[] = [
  {
    action_id: "action_open_app",
    organization_id: "acme-retail-2024",
    activity_id: "act_browse_product_catalog",
    name: "Open Mobile App",
    description: "Customer opens the mobile shopping application",
    sequence: 1,
    category: "Customer Interaction",
    link: "https://acmeretail.atlassian.net/browse/APP-123",
    supported_by: "Mobile Development Team",
    reference_screen: "App Launch Screen v2.1",
    delivery_epic: "Mobile Experience Enhancement Q1 2024",
    additional_document_references: "https://acmeretail.confluence.com/mobile-app-guide",
    priority: "medium",
    estimated_effort: "30 seconds",
    usage_count: 1247,
    last_used: "2024-01-15T10:30:00Z",
    impact_score: 8.5,
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
];

// Mock activities by type
export const mockActivitiesByType: ActivitiesByType = {
  customer: [
    {
      activity_id: "act_browse_product_catalog",
      organization_id: "acme-retail-2024",
      moment_id: "moment_compare_prices_online",
      name: "Browse Product Catalog",
      description: "Customer browses products using mobile app",
      activity_type: "customer",
      estimated_duration: "3-5 minutes",
      complexity_level: "medium",
      personas: ["persona_busy_parent_shopper"],
      sequence: 1,
      status: "active",
      created_by: mockUserRef,
      updated_by: mockUserRef,
      action_details: mockActions,
    },
  ],
  front_stage: [],
  back_stage: [],
  system: [],
};

// Mock moment details
export const mockMomentDetails: MomentDetails[] = [
  {
    moment_id: "moment_marketing_home_loan_offer",
    organization_id: "acme-financial-2024",
    phase_id: "phase_explore",
    name: "Marketing & Home loan offer",
    description: "The customer sees a home loan promotion on social media and begins considering the idea of buying a home overseas.",
    image_url: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop",
    sequence: 1,
    components: ["comp_marketing_campaign", "comp_social_media"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
    activities: {
      customer: [
        {
          activity_id: "act_discover_home_loan_offer",
          organization_id: "acme-financial-2024",
          moment_id: "moment_marketing_home_loan_offer",
          name: "Discover home loan offer",
          description: "Customer finds out more about the home loan product",
          activity_type: "customer",
          estimated_duration: "5-10 minutes",
          complexity_level: "low",
          personas: ["persona_home_buyer"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      front_stage: [
        {
          activity_id: "act_staff_visit",
          organization_id: "acme-financial-2024",
          moment_id: "moment_marketing_home_loan_offer",
          name: "All staff visits a landing page with contextual product information and a CTA to speak with someone",
          description: "Front stage staff assists with initial customer inquiries",
          activity_type: "front_stage",
          estimated_duration: "15-30 minutes",
          complexity_level: "medium",
          personas: ["persona_loan_specialist"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      back_stage: [
        {
          activity_id: "act_support_staff_checks",
          organization_id: "acme-financial-2024",
          moment_id: "moment_marketing_home_loan_offer",
          name: "Support staff checks staff availability and ensures the customer is in an appropriate time slot",
          description: "Back stage operations ensure smooth customer experience",
          activity_type: "back_stage",
          estimated_duration: "5-10 minutes",
          complexity_level: "medium",
          personas: ["persona_underwriter"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      system: [
        {
          activity_id: "act_marketing_automation",
          organization_id: "acme-financial-2024",
          moment_id: "moment_marketing_home_loan_offer",
          name: "Marketing automation targeting the customer engagement and creating campaigns",
          description: "System manages automated marketing campaigns",
          activity_type: "system",
          estimated_duration: "< 1 second",
          complexity_level: "high",
          personas: ["persona_loan_system"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
    },
  },
  {
    moment_id: "moment_visiting_branch",
    organization_id: "acme-financial-2024",
    phase_id: "phase_explore",
    name: "Visiting branch",
    description: "After visiting online, the customer wants a more trading option to speak directly with someone",
    image_url: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop",
    sequence: 2,
    components: ["comp_branch_management"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
    activities: {
      customer: [
        {
          activity_id: "act_discover_home_options",
          organization_id: "acme-financial-2024",
          moment_id: "moment_visiting_branch",
          name: "Discover home options and online rates",
          description: "Customer explores available home loan options and rates",
          activity_type: "customer",
          estimated_duration: "30-45 minutes",
          complexity_level: "medium",
          personas: ["persona_home_buyer"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      front_stage: [
        {
          activity_id: "act_branch_specialist_greets",
          organization_id: "acme-financial-2024",
          moment_id: "moment_visiting_branch",
          name: "Branch specialist greets the customer and schedules them to an available loan specialist",
          description: "Staff provides personal consultation",
          activity_type: "front_stage",
          estimated_duration: "5-10 minutes",
          complexity_level: "low",
          personas: ["persona_loan_specialist"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      back_stage: [
        {
          activity_id: "act_staff_reviews_availability",
          organization_id: "acme-financial-2024",
          moment_id: "moment_visiting_branch",
          name: "Staff reviews the customer's existing investments and reports some inconsistency",
          description: "Back office reviews customer profile",
          activity_type: "back_stage",
          estimated_duration: "10-15 minutes",
          complexity_level: "medium",
          personas: ["persona_underwriter"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      system: [
        {
          activity_id: "act_staff_booking_system",
          organization_id: "acme-financial-2024",
          moment_id: "moment_visiting_branch",
          name: "Staff booking system",
          description: "System manages staff scheduling and appointments",
          activity_type: "system",
          estimated_duration: "< 1 second",
          complexity_level: "medium",
          personas: ["persona_loan_system"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
    },
  },
  {
    moment_id: "moment_booking_appointment",
    organization_id: "acme-financial-2024",
    phase_id: "phase_apply",
    name: "Booking appointment with loan specialist",
    description: "After a brief conversation, the customer books a full consultation.",
    image_url: "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=400&h=300&fit=crop",
    sequence: 1,
    components: ["comp_appointment_system"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
    activities: {
      customer: [
        {
          activity_id: "act_customer_provides_details",
          organization_id: "acme-financial-2024",
          moment_id: "moment_booking_appointment",
          name: "Customer provides preliminary financial details",
          description: "Customer shares basic financial information",
          activity_type: "customer",
          estimated_duration: "15-20 minutes",
          complexity_level: "medium",
          personas: ["persona_home_buyer"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      front_stage: [
        {
          activity_id: "act_loan_specialist_confirms",
          organization_id: "acme-financial-2024",
          moment_id: "moment_booking_appointment",
          name: "Loan specialist confirms the appointment and shares a list of required documents",
          description: "Specialist prepares customer for full application",
          activity_type: "front_stage",
          estimated_duration: "10-15 minutes",
          complexity_level: "low",
          personas: ["persona_loan_specialist"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      back_stage: [
        {
          activity_id: "act_legal_prepares_documents",
          organization_id: "acme-financial-2024",
          moment_id: "moment_booking_appointment",
          name: "Legal prepares the loan in line products and shares legal/compliance info",
          description: "Legal team prepares necessary documentation",
          activity_type: "back_stage",
          estimated_duration: "30-45 minutes",
          complexity_level: "high",
          personas: ["persona_underwriter"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      system: [
        {
          activity_id: "act_appointment_system",
          organization_id: "acme-financial-2024",
          moment_id: "moment_booking_appointment",
          name: "Appointment system logs the booking and alerts the calendar system",
          description: "System manages appointment scheduling",
          activity_type: "system",
          estimated_duration: "< 1 second",
          complexity_level: "medium",
          personas: ["persona_loan_system"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
    },
  },
  {
    moment_id: "moment_attending_appointment",
    organization_id: "acme-financial-2024",
    phase_id: "phase_apply",
    name: "Attending loan information",
    description: "The customer attends the appointment to acquire eligibility and loan options.",
    image_url: "https://images.unsplash.com/photo-1521791136064-7986c2920216?w=400&h=300&fit=crop",
    sequence: 2,
    components: ["comp_loan_calculator", "comp_document_system"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
    activities: {
      customer: [
        {
          activity_id: "act_customer_provides_documents",
          organization_id: "acme-financial-2024",
          moment_id: "moment_attending_appointment",
          name: "Customer provides all required documents and discusses the loan options",
          description: "Customer submits required documentation",
          activity_type: "customer",
          estimated_duration: "45-60 minutes",
          complexity_level: "medium",
          personas: ["persona_home_buyer"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      front_stage: [
        {
          activity_id: "act_loan_specialist_explains",
          organization_id: "acme-financial-2024",
          moment_id: "moment_attending_appointment",
          name: "Loan specialist explains the best fit loan products and shares information with the customer",
          description: "Specialist guides customer through options",
          activity_type: "front_stage",
          estimated_duration: "60-90 minutes",
          complexity_level: "high",
          personas: ["persona_loan_specialist"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      back_stage: [
        {
          activity_id: "act_system_calculates_additional",
          organization_id: "acme-financial-2024",
          moment_id: "moment_attending_appointment",
          name: "System calculates additional and the risk factors for further review",
          description: "Back office processes application details",
          activity_type: "back_stage",
          estimated_duration: "30-45 minutes",
          complexity_level: "high",
          personas: ["persona_underwriter"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      system: [
        {
          activity_id: "act_assessment_tool",
          organization_id: "acme-financial-2024",
          moment_id: "moment_attending_appointment",
          name: "Assessment tool estimates borrowing capacity and guides the loan specialist",
          description: "System performs automated assessment",
          activity_type: "system",
          estimated_duration: "< 5 seconds",
          complexity_level: "high",
          personas: ["persona_loan_system"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
    },
  },
  {
    moment_id: "moment_submitting_application",
    organization_id: "acme-financial-2024",
    phase_id: "phase_apply",
    name: "Submitting home loan application",
    description: "Satisfied with the consultation, the customer proceeds with a formal application.",
    image_url: "https://images.unsplash.com/photo-1450101499163-c8848c66ca85?w=400&h=300&fit=crop",
    sequence: 3,
    components: ["comp_application_portal", "comp_document_system"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
    activities: {
      customer: [
        {
          activity_id: "act_customer_completes_application",
          organization_id: "acme-financial-2024",
          moment_id: "moment_submitting_application",
          name: "Customer uploads additional documents and finalizes the online application",
          description: "Customer finalizes application submission",
          activity_type: "customer",
          estimated_duration: "30-45 minutes",
          complexity_level: "medium",
          personas: ["persona_home_buyer"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      front_stage: [
        {
          activity_id: "act_loan_specialist_updates",
          organization_id: "acme-financial-2024",
          moment_id: "moment_submitting_application",
          name: "Loan specialist updates documents and finalizes the official application",
          description: "Specialist processes final application",
          activity_type: "front_stage",
          estimated_duration: "45-60 minutes",
          complexity_level: "medium",
          personas: ["persona_loan_specialist"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      back_stage: [
        {
          activity_id: "act_system_transfers_compliance",
          organization_id: "acme-financial-2024",
          moment_id: "moment_submitting_application",
          name: "System transfers data to the operations team for further review",
          description: "Back office initiates review process",
          activity_type: "back_stage",
          estimated_duration: "15-30 minutes",
          complexity_level: "medium",
          personas: ["persona_underwriter"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      system: [
        {
          activity_id: "act_risk_engine_processes",
          organization_id: "acme-financial-2024",
          moment_id: "moment_submitting_application",
          name: "Risk engine processes the application and verifies CRM with the customer data",
          description: "System performs risk assessment",
          activity_type: "system",
          estimated_duration: "1-5 minutes",
          complexity_level: "high",
          personas: ["persona_loan_system"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
    },
  },
  {
    moment_id: "moment_loan_assessment",
    organization_id: "acme-financial-2024",
    phase_id: "phase_decision",
    name: "Loan assessment",
    description: "The bank evaluates the customer's eligibility and creditworthiness.",
    image_url: "https://images.unsplash.com/photo-**********-6726b3ff858f?w=400&h=300&fit=crop",
    sequence: 1,
    components: ["comp_underwriting_system", "comp_credit_check"],
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
    activities: {
      customer: [
        {
          activity_id: "act_customer_receives_updates",
          organization_id: "acme-financial-2024",
          moment_id: "moment_loan_assessment",
          name: "Customer receives all required updates via the digital upload or in-branch",
          description: "Customer stays informed of assessment progress",
          activity_type: "customer",
          estimated_duration: "5-10 minutes",
          complexity_level: "low",
          personas: ["persona_home_buyer"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      front_stage: [
        {
          activity_id: "act_loan_specialist_shares",
          organization_id: "acme-financial-2024",
          moment_id: "moment_loan_assessment",
          name: "Loan specialist shares updates and requests how info if needed",
          description: "Specialist maintains customer communication",
          activity_type: "front_stage",
          estimated_duration: "15-30 minutes",
          complexity_level: "low",
          personas: ["persona_loan_specialist"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      back_stage: [
        {
          activity_id: "act_system_conducts_risk",
          organization_id: "acme-financial-2024",
          moment_id: "moment_loan_assessment",
          name: "System conducts a risk review and provides the assessment for approval",
          description: "Back office completes comprehensive assessment",
          activity_type: "back_stage",
          estimated_duration: "2-5 business days",
          complexity_level: "high",
          personas: ["persona_underwriter"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
      system: [
        {
          activity_id: "act_loan_system_conducts",
          organization_id: "acme-financial-2024",
          moment_id: "moment_loan_assessment",
          name: "Loan system conducts a risk review and provides the guidelines for approval",
          description: "System performs automated risk analysis",
          activity_type: "system",
          estimated_duration: "1-2 hours",
          complexity_level: "high",
          personas: ["persona_loan_system"],
          sequence: 1,
          status: "active",
          created_by: mockUserRef,
          updated_by: mockUserRef,
          action_details: [],
        },
      ],
    },
  },
];

// Mock maps
export const mockMaps: Map[] = [
  {
    map_id: "map_grocery_journey_v3",
    organization_id: "acme-retail-2024",
    customer_goal: mockCustomerGoals[0],
    name: "In-Store Shopping Experience",
    description: "Complete journey mapping for customers shopping in physical grocery stores",
    division: "Retail Operations",
    state: "published",
    status: "active",
    created_by: mockUserRef,
    updated_by: mockUserRef,
  },
];

// Mock map details
export const mockMapDetails: MapDetails = {
  map_id: "map_home_loan_journey",
  organization_id: "acme-financial-2024",
  customer_goal: mockCustomerGoals[0],
  name: "Home Loan Application Journey",
  description: "Complete customer journey for home loan application process",
  division: "Financial Services",
  state: "published",
  status: "active",
  created_by: mockUserRef,
  updated_by: mockUserRef,
  phases: [
    {
      ...mockPhases[0],
      moment_ids: ["moment_marketing_home_loan_offer", "moment_visiting_branch"],
    },
    {
      ...mockPhases[1],
      moment_ids: ["moment_booking_appointment", "moment_attending_appointment", "moment_submitting_application"],
    },
    {
      ...mockPhases[2],
      moment_ids: ["moment_loan_assessment"],
    },
  ],
  moments: mockMomentDetails,
  personas: mockPersonas.map((persona) => ({
    ...persona,
    moments_used: [
      {
        moment_id: "moment_marketing_home_loan_offer",
        moment_name: "Marketing & Home loan offer",
        map_name: "Home Loan Application Journey",
        activity_count: 1,
      },
    ],
  })),
  components: [
    {
      component_id: "comp_loan_calculator",
      organization_id: "acme-financial-2024",
      numbering: "CALC-001",
      name: "Home Loan Calculator",
      description: "Interactive calculator for home loan estimates",
      best_practices: "Ensure accurate calculations and clear display of results",
      count: 3,
      level: "Critical",
      framework: "customer",
      phases_used: ["phase_explore", "phase_apply"],
      moments_used: ["moment_marketing_home_loan_offer", "moment_attending_appointment"],
      status: "active",
      created_by: mockUserRef,
      updated_by: mockUserRef,
      usage_details: [
        {
          entity_type: "moment" as const,
          entity_id: "moment_marketing_home_loan_offer",
          entity_name: "Marketing & Home loan offer",
          map_name: "Home Loan Application Journey",
        },
      ],
    },
    {
      component_id: "comp_application_portal",
      organization_id: "acme-financial-2024",
      numbering: "APP-002",
      name: "Online Application Portal",
      description: "Digital platform for loan application submission",
      best_practices: "Maintain 99.9% uptime and secure data handling",
      count: 5,
      level: "Essential",
      framework: "business",
      phases_used: ["phase_apply"],
      moments_used: ["moment_submitting_application"],
      status: "active",
      created_by: mockUserRef,
      updated_by: mockUserRef,
      usage_details: [
        {
          entity_type: "moment" as const,
          entity_id: "moment_submitting_application",
          entity_name: "Submitting home loan application",
          map_name: "Home Loan Application Journey",
        },
      ],
    },
  ],
  statistics: {
    total_phases: 3,
    total_moments: 6,
    total_actions: 24,
    total_personas: 4,
    total_components: 2,
  },
};

// Export all mock data
export const mockData = {
  user: mockUser,
  customerGoals: mockCustomerGoals,
  personas: mockPersonas,
  components: mockComponents,
  maps: mockMaps,
  mapDetails: mockMapDetails,
  phases: mockPhases,
  moments: mockMomentDetails,
};

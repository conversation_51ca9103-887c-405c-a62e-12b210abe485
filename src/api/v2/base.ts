import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import type { ApiError } from "@/types/api/client";

// Custom API Client with enhanced error handling and authentication
class ApiV2Client {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_V2_BASE_URL || "https://z0p4210n29.execute-api.ap-southeast-2.amazonaws.com/Prod";
    
    this.api = axios.create({
      baseURL: this.baseURL,
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Only initialize on client-side
    if (typeof window !== "undefined") {
      this.initializeAuthToken();
      this.setupInterceptors();
    }
  }

  private initializeAuthToken() {
    if (typeof window !== "undefined") {
      const token = localStorage.getItem("authToken");
      if (token) {
        this.setAuthToken(token);
      }
    }
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem("authToken");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error: AxiosError<ApiError>) => {
        // Handle common API errors
        if (error.response?.status === 401) {
          // Unauthorized - clear token and redirect to login
          this.clearAuthToken();
          window.location.href = '/login';
        }
        
        // Transform error for consistent handling
        const apiError: ApiError = {
          error: error.response?.data?.error || 'unknown_error',
          code: error.response?.data?.code || 'HTTP_' + error.response?.status,
          message: error.response?.data?.message || error.message,
          timestamp: error.response?.data?.timestamp || new Date().toISOString(),
        };
        
        return Promise.reject(apiError);
      }
    );
  }

  // Helper to set the Bearer token
  setAuthToken(token: string) {
    this.api.defaults.headers.common["Authorization"] = `Bearer ${token}`;
    if (typeof window !== "undefined") {
      localStorage.setItem("authToken", token);
    }
  }

  // Clear the Bearer token
  clearAuthToken() {
    delete this.api.defaults.headers.common["Authorization"];
    if (typeof window !== "undefined") {
      localStorage.removeItem("authToken");
      localStorage.removeItem("username");
    }
  }

  // Set organization override header (for super admin users)
  setOrganizationOverride(orgId?: string) {
    if (orgId) {
      this.api.defaults.headers.common["X-Organization-Id"] = orgId;
    } else {
      delete this.api.defaults.headers.common["X-Organization-Id"];
    }
  }

  // Basic GET request
  async get<T>(url: string, params?: Record<string, any>): Promise<AxiosResponse<T>> {
    return this.api.get<T>(url, { params });
  }

  // Basic POST request
  async post<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, data);
  }

  // Basic PUT request
  async put<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.put<T>(url, data);
  }

  // Basic PATCH request
  async patch<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.patch<T>(url, data);
  }

  // Basic DELETE request
  async delete<T>(url: string): Promise<AxiosResponse<T>> {
    return this.api.delete<T>(url);
  }

  // Form data POST (for file uploads)
  async postFormData<T>(url: string, formData: FormData): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
}

// Export singleton instance
export const apiV2Client = new ApiV2Client();
export default apiV2Client;
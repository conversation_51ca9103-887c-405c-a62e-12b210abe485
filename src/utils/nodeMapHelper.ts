import { HierarchicalNode, NodeMapData } from "@/types/components";
import { SimulationNodeDatum } from "d3";

export interface Node extends SimulationNodeDatum {
  id: string;
  group: number;
  value: number;
  level: number;
  framework: string;
  numbering: string;
  name: string;
  description: string;
  children: string[];
}

export interface ExpandableNode extends Node {
  isExpanded?: boolean;
}

export interface Link {
  source: string;
  target: string;
  value: number;
}

export const getNodePath = (nodeId: string): string[] => {
  const parts = nodeId.split("-");
  if (parts[0] === "root") return ["root"];
  const numbering = parts[1].split(".");
  return numbering.map(
    (_, index) => `${parts[0]}-${numbering.slice(0, index + 1).join(".")}`
  );
};

export const getAllDescendants = (
  nodeId: string,
  nodes: ExpandableNode[]
): string[] => {
  // Handle root node
  if (nodeId === "root") {
    return nodes.filter((node) => node.level === 1).map((node) => node.id);
  }

  // Find the target node
  const node = nodes.find((n) => n.id === nodeId);
  if (!node) return [];

  // Get immediate children
  const descendants: string[] = [...node.children];

  // Recursively get children of children
  node.children.forEach((childId) => {
    descendants.push(...getAllDescendants(childId, nodes));
  });

  return descendants;
};

export const getVisibleNodesAndLinks = (
  nodes: ExpandableNode[],
  links: Link[],
  expandedNodes: Set<string>
) => {
  const visibleNodes = nodes.filter((node) => {
    if (node.level === 0) return true;
    return expandedNodes.has(node.id);
  });

  const visibleNodeIds = new Set(visibleNodes.map((n) => n.id));

  const isPhaseExpanded = (nodeId: string) => {
    return expandedNodes.has(nodeId);
  };

  const visibleLinks = links.filter((link) => {
    const sourceNode = nodes.find(n => n.id === link.source);
    const targetNode = nodes.find(n => n.id === link.target);

    if (sourceNode?.framework === 'map' && 
        (targetNode?.framework === 'customer' || targetNode?.framework === 'business')) {
      
      // If source is a map node
      if (sourceNode.id.startsWith('map-map')) {
        return !isPhaseExpanded(sourceNode.id) &&
               visibleNodeIds.has(link.source as string) && 
               visibleNodeIds.has(link.target as string);
      }
    }

    return visibleNodeIds.has(link.source as string) && 
           visibleNodeIds.has(link.target as string);
  });

  return { visibleNodes, visibleLinks };
};

// Update transformDataToNodesAndLinks
export const transformDataToNodesAndLinks = (
  nodeMapData: NodeMapData,
  view: "customer" | "business" | "all"
) => {
  const nodes: ExpandableNode[] = [];
  const links: Link[] = [];

  const processNode = (
    node: HierarchicalNode,
    framework: string,
    level: number,
    parentId: string | null
  ) => {
    if (view !== "all" && framework !== view) {
      return;
    }

    const nodeId = `${framework}-${node.numbering || node.id}`;
    if (nodes.some((n) => n.id === nodeId)) {
      return;
    }

    nodes.push({
      id: nodeId,
      group: level,
      value: 1,
      level,
      framework,
      isExpanded: false,
      numbering: node.numbering || "",
      name: node.name,
      description: node.description || "",
      children: node.children.map(
        (child) => `${framework}-${child.numbering || child.id}`
      ),
    });

    // Add parent-child links
    if (parentId) {
      links.push({
        source: parentId,
        target: nodeId,
        value: 1,
      });
    }

    // Track component relationships for map phases
    if (framework === 'map' && 'components' in node) {
      const phaseComponents = (node as any).components || [];
      phaseComponents.forEach((comp: { id: number; numbering: string }) => {
        // Add links to both customer and business frameworks
        if (view === 'all') {
          const customerCompId = `customer-${comp.numbering}`;
          const businessCompId = `business-${comp.numbering}`;
          
          links.push(
            {
              source: nodeId,
              target: customerCompId,
              value: 1,
            },
            {
              source: nodeId,
              target: businessCompId,
              value: 1,
            }
          );
        }
      });
    }

    // Process children
    if (Array.isArray(node.children)) {
      node.children.forEach((child) => {
        processNode(child, framework, level + 1, nodeId);
      });
    }
  };

  // Process nodes
  if (nodeMapData.map && view === "all") {
    nodeMapData.map.forEach((node) => {
      processNode(node, "map", 0, null);
    });
  }

  if (nodeMapData.customer && (view === "customer" || view === "all")) {
    processNode(nodeMapData.customer, "customer", 0, null);
  }

  if (nodeMapData.business && (view === "business" || view === "all")) {
    processNode(nodeMapData.business, "business", 0, null);
  }

  return { nodes, links };
};

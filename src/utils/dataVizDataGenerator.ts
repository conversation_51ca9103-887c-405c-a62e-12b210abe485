interface DataNode {
    id: string;
    count: number;
    level: number;
    parent: string | null;
  }
  
  interface HierarchicalData {
    [key: number]: DataNode[];
  }
  
 export interface FrameworkData {
    framework: 'customer' | 'business';
    data: HierarchicalData;
  }
  
  export function generateHierarchicalDataByLevel(levelsArray: number[]): HierarchicalData {
    const data: HierarchicalData = {};
  
    // Helper function to generate random count value between 0 and 20
    const getRandomCount = (min: number, max: number) =>
      Math.floor(Math.random() * (max - min + 1)) + min;
  
    // Generate level 3 data with correct hierarchical IDs (1.1.1.x)
    const generateLevel3Data = () => {
      const level3Data: DataNode[] = [];
      for (let parent1 = 1; parent1 <= levelsArray[0]; parent1++) {
        for (let parent2 = 1; parent2 <= levelsArray[1]; parent2++) {
          for (let parent3 = 1; parent3 <= levelsArray[2]; parent3++) {
            for (let i = 1; i <= levelsArray[3]; i++) {
              const id = `${parent1}.${parent2}.${parent3}.${i}`;
              const node: DataNode = {
                id: id,
                count: getRandomCount(0, 20), // Random count for level 3
                level: 3,
                parent: `${parent1}.${parent2}.${parent3}`, // Correct parent ID
              };
              level3Data.push(node);
            }
          }
        }
      }
      return level3Data;
    };
  
    // Function to generate parent levels based on child data
    const generateParentLevelData = (childLevel: number, currentLevel: number) => {
      const childData = data[childLevel];
      const parentDataMap: { [key: string]: DataNode } = {};
  
      childData.forEach((child) => {
        const parentId = child.parent!;
        if (!parentDataMap[parentId]) {
          parentDataMap[parentId] = {
            id: parentId,
            count: 0,
            level: currentLevel,
            parent: parentId.includes(".") ? parentId.split(".").slice(0, -1).join(".") : null, // Set parent for higher levels
          };
        }
        parentDataMap[parentId].count += child.count; // Sum up the children's counts
      });
  
      return Object.values(parentDataMap); // Return the array of parent nodes
    };
  
    // Generate level 3 data first
    data[3] = generateLevel3Data();
  
    // Generate level 2 by summing level 3 data
    data[2] = generateParentLevelData(3, 2);
  
    // Generate level 1 by summing level 2 data
    data[1] = generateParentLevelData(2, 1);
  
    // Generate level 0 by summing level 1 data
    data[0] = generateParentLevelData(1, 0); 

    return data;
  }
  
  export function generateHierarchicalDataByView(levelsArray: number[]): FrameworkData[] {
    return [
      {
        framework: 'customer',
        data: generateHierarchicalDataByLevel(levelsArray)
      },
      {
        framework: 'business',
        data: generateHierarchicalDataByLevel(levelsArray)
      }
    ];
  }

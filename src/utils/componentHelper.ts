import { ComponentData, HierarchicalNode } from "@/types/components";
import { MapDetails } from "@/types/map";
import * as XLSX from "xlsx";

// Define the expected structure of each row
export interface ComponentModelRow {
  ID: string;
  Name: string;
  Definition: string;
  Examples: string;
}

// Define the allowed headers
const VALID_HEADERS: string[] = ["ID", "Name", "Definition", "Examples"];

// Utility function to check if headers match the expected format
const isValidHeader = (headerRow: unknown[]): headerRow is string[] => {
  if (headerRow.length !== VALID_HEADERS.length) return false;
  return headerRow.every((header, index) => header === VALID_HEADERS[index]);
};

export const parseComponentData = (data: ComponentData[]): ComponentModelRow[] => {
  return data
    .filter((item) => item.is_active)
    .map((item) => ({
      ID: item.numbering,
      Name: item.name,
      Definition: item.description,
      Examples: item.examples || "", // Default to empty string if not provided
    }));
};

export const parseComponentModelExcelFile = async (file: File): Promise<ComponentModelRow[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      const arrayBuffer = event.target?.result;

      try {
        // Read the ArrayBuffer into a binary string
        const data = new Uint8Array(arrayBuffer as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });

        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];

        // Parse the Excel file
        const parsedData = XLSX.utils.sheet_to_json(sheet, { header: 1 });

        // Ensure parsedData has content and the first row is the header row
        if (Array.isArray(parsedData) && parsedData.length > 0) {
          const headerRow = parsedData[0] as unknown[]; // Explicitly cast the first row as an array

          // Validate the headers
          if (!isValidHeader(headerRow)) {
            return reject(
              new Error(
                "Invalid headers. The file must contain ID, Name, Definition, and Examples headers."
              )
            );
          }

          // Map the remaining rows to the ComponentModelRow type
          const dataRows: ComponentModelRow[] = parsedData.slice(1).map((row) => {
            const rowArray = row as unknown[]; // Explicitly cast each row as an array of unknown
            return {
              ID: String(rowArray[0]),
              Name: String(rowArray[1]),
              Definition: String(rowArray[2]),
              Examples: String(rowArray[3]),
            };
          });

          resolve(dataRows);
        } else {
          reject(new Error("The Excel file is empty or improperly formatted."));
        }
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (error) => reject(error);

    // Use `readAsArrayBuffer` instead of `readAsBinaryString`
    reader.readAsArrayBuffer(file);
  });
};

export const parseComponentDataToNodeMap = (data: ComponentData[]): HierarchicalNode => {
  const root: HierarchicalNode = {
    id: -1,
    numbering: "root",
    name: "root",
    description: "click to expand",
    parent: null,
    children: [],
  };

  // Build hierarchical tree
  const buildTree = (items: ComponentData[], parentId: number | null): HierarchicalNode[] => {
    return items
      .filter((item) => item.parent === parentId)
      .map((item) => ({
        id: item.id,
        numbering: item.numbering,
        name: item.name,
        description: item.description,
        parent: item.parent ? item.parent : -1,
        children: buildTree(items, item.id),
      }));
  };

  root.children = buildTree(data, null);
  console.log(root);

  return root;
};

export const parseMapData = (mapDetails: MapDetails[]): HierarchicalNode[] => {
  return mapDetails.map((map) => {
    const rootNode: HierarchicalNode = {
      id: `map-${map.id}`,
      name: map.name,
      description: map.desc,
      parent: null,
      components: map.phases.flatMap((phase) => phase.components),
      children: map.phases.map((phase) => ({
        id: `phase-${phase.id}`,
        name: phase.name,
        description: phase.desc,
        parent: `map-${map.id}`,
        components: phase.components,
        children: map.building_blocks
          .filter((block) => block.phase === phase.id)
          .map((block) => ({
            id: `block-${block.id}`,
            name: block.name,
            description: block.desc,
            parent: `phase-${phase.id}`,
            children: [],
          })),
      })),
    };
    return rootNode;
  });
};

/**
 * Helper function to get the order index of a building block type
 * @param typeName The name of the building block type
 * @returns The order index (lower values appear first)
 */
export const getBuildingBlockTypeOrder = (typeName: string): number => {
  // Define the order based on the toggle menu
  const orderMap: Record<string, number> = {
    Painpoints: 1,
    Opportunity: 2,
    "Voice of customer": 3,
    NPS: 3, // Voice of Customer subtype
    CES: 3, // Voice of Customer subtype
    CSAT: 3, // Voice of Customer subtype
    "Operation context": 4,
    Analytics: 5,
    // Add any other types with higher numbers
  };

  // Return the order index if found, otherwise return a high number to place it at the end
  return orderMap[typeName] || 999;
};

import { BuildingBlockType, RoleType } from "@/types/common";
import { Action, BuildingBlock, MapRole, Phase } from "@/types/map";

type ActionsByTypeRoleEntry = {
  type?: string;
  role?: number;
  minHeight: number;
  phases: { [phaseId: number]: Action[] };
};

export type ActionsByTypeRole = {
  [key: string]: ActionsByTypeRoleEntry;
};
type BuildingBlocksByTypeEntry = {
  type: number;
  subType?: number;
  typeName: string;
  phases: { [phaseId: number]: BuildingBlock[] };
  minHeight: number;
};

export type BuildingBlocksByType = {
  [key: string | number]: BuildingBlocksByTypeEntry;
};

/**
 * Processes actions into a dictionary with type-role as keys and calculates maxHeight.
 * @param phases - The list of phases from state.
 * @param actions - The list of actions from state.
 * @returns The actionsByTypeRole dictionary.
 */
export const processActionsByTypeRole = (
  actions: Action[],
  _roles: MapRole[], // Kept for interface compatibility
  roleTypes: RoleType[]
): ActionsByTypeRole => {
  const actionsByTypeRole: ActionsByTypeRole = {};
  roleTypes.forEach((roleType) => {
    actionsByTypeRole[roleType.name] = {
      type: roleType.name,
      minHeight: 20,
      phases: {},
    };
  });

  actions.forEach((action) => {
    const roleType = action.role?.type;
    if (actionsByTypeRole[roleType]) {
      actionsByTypeRole[roleType].phases[action.phase] = [
        ...(actionsByTypeRole[roleType].phases[action.phase] || []),
        action,
      ];
    }
  });

  return actionsByTypeRole;
};

/**
 * Processes building block types for the toggle menu in CanvasControlToolbar
 * For "Insights" type, it adds each subtype as a separate entry
 * @param buildingBlockTypes The original building block types
 * @returns Processed building block types for the toggle menu
 */
export const processToggleMenuBuildingBlockTypes = (
  buildingBlockTypes: BuildingBlockType[]
): BuildingBlockType[] => {
  // Create separate arrays for Insights subtypes and other types
  const insightsSubtypes: BuildingBlockType[] = [];
  const otherTypes: BuildingBlockType[] = [];

  buildingBlockTypes.forEach((blockType) => {
    // Special handling for "Insights" type
    if (blockType.name === "Insights" && (blockType as any).sub_types) {
      // Add each subtype as a separate entry
      const subTypes = (blockType as any).sub_types || [];
      subTypes.forEach((subType: any) => {
        // Create a new entry for each subtype
        // Create a new object with all required properties
        // Use type assertion to add custom properties
        const newEntry = {
          ...blockType, // Copy all properties from the parent
          id: parseInt(`${blockType.id}${subType.id}`), // Create a unique ID
          name: subType.name, // Use subtype name
          desc: subType.desc || blockType.desc,
          order: subType.order || blockType.order,
          // Add custom properties for reference using a custom property
          _parent_type_id: blockType.id,
          _sub_type_id: subType.id,
        } as unknown as BuildingBlockType;

        // Add to the Insights subtypes array
        insightsSubtypes.push(newEntry);
      });
    } else {
      // Normal handling for other types
      otherTypes.push(blockType);
    }
  });

  // Combine the arrays with Insights subtypes first
  return [...insightsSubtypes, ...otherTypes];
};

export const processBuildingBlocksByType = (
  phases: Phase[],
  buildingBlocks: BuildingBlock[],
  buildingBlockTypes: BuildingBlockType[]
): BuildingBlocksByType => {
  // Initialize with all building block types
  const buildingBlocksByType: BuildingBlocksByType = {};

  // Create an ordered array to control the order of building block types
  const orderedEntries: [string | number, BuildingBlocksByTypeEntry][] = [];

  // First, initialize all building block types with default values
  buildingBlockTypes.forEach((blockType) => {
    // Special handling for "Insights" type
    if (blockType.name === "Insights" && (blockType as any).sub_types) {
      // Add each subtype as a separate entry
      const subTypes = (blockType as any).sub_types || [];
      subTypes.forEach((subType: any) => {
        const compositeKey = `${blockType.id}-${subType.id}`;
        const entry: BuildingBlocksByTypeEntry = {
          type: blockType.id,
          subType: subType.id,
          typeName: subType.name, // Use subtype name instead of type name
          phases: {},
          minHeight: 20, // Default minimum height
        };

        // Add to ordered entries with a special prefix to ensure they come first
        // Use a prefix like "A_" to ensure they sort before other entries
        orderedEntries.push([`A_${compositeKey}`, entry]);
      });
    } else {
      // Normal handling for other types
      const entry: BuildingBlocksByTypeEntry = {
        type: blockType.id,
        typeName: blockType.name,
        phases: {},
        minHeight: 20, // Default minimum height
      };

      // Add to ordered entries with a regular prefix
      orderedEntries.push([`B_${blockType.id}`, entry]);
    }
  });

  // Sort the entries to ensure Insights subtypes come first
  orderedEntries.sort((a, b) => {
    const keyA = a[0].toString();
    const keyB = b[0].toString();
    return keyA.localeCompare(keyB);
  });

  // Convert the ordered entries back to the buildingBlocksByType object
  // but use the original keys without the sorting prefixes
  orderedEntries.forEach(([key, entry]) => {
    // Extract the original key by removing the sorting prefix
    const originalKey = key.toString().substring(2);
    buildingBlocksByType[originalKey] = entry;
  });

  // Then process actual building blocks
  // For building blocks that don't have phase information, we'll distribute them across all phases
  // or assign them to the first phase as a default behavior
  buildingBlocks.forEach((block) => {
    // Extract type ID from block (handle both number and object types)
    const typeId = typeof block.type === "object" ? block.type.id : block.type;
    const subTypeId = typeof block.sub_type === "object" ? block.sub_type.id : block.sub_type;

    // Initialize key as string to avoid type issues
    let key: string | number = typeId;

    // Find the corresponding building block type
    const blockType = buildingBlockTypes.find((type) => type.id === typeId);

    // Special handling for "Insights" type
    if (blockType && blockType.name === "Insights" && subTypeId) {
      // Use composite key for Insights subtypes
      key = `${typeId}-${subTypeId}`;
    }

    // Skip if the key doesn't exist in our initialized types
    if (!buildingBlocksByType[key]) {
      return;
    }

    // Determine which phases to add this block to
    let targetPhases: Phase[] = [];

    if (block.phase !== undefined && block.phase !== null) {
      // If block has phase information, use it
      const targetPhase = phases.find((p) => p.id === block.phase);
      if (targetPhase) {
        targetPhases = [targetPhase];
      }
    } else {
      // If no phase information, add to all phases or first phase
      // For now, let's add to the first phase as default
      if (phases.length > 0) {
        targetPhases = [phases[0]];
      }
    }

    // Add the block to the target phases
    targetPhases.forEach((phase) => {
      // Initialize the phase array if it doesn't exist
      if (!buildingBlocksByType[key].phases[phase.id]) {
        buildingBlocksByType[key].phases[phase.id] = [];
      }

      // Add the block to the appropriate phase with phase information
      const blockWithPhase = {
        ...block,
        phase: phase.id,
      };

      buildingBlocksByType[key].phases[phase.id].push(blockWithPhase);

      // Calculate max height based on number of blocks
      buildingBlocksByType[key].minHeight = Math.max(
        buildingBlocksByType[key].minHeight,
        Math.ceil(buildingBlocksByType[key].phases[phase.id].length / 3) * 12 + 4
      );
    });
  });

  return buildingBlocksByType;
};

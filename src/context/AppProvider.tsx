// src/context/AppContext.tsx
import { useAuth } from "@/context/AuthProvider";
import appService from "@/services/AppService";
import { CommonData } from "@/types/common";
import React, { createContext, ReactNode, useContext, useEffect, useMemo, useState } from "react";

interface AppContextType {
  commonData: CommonData;
  isLoading: boolean;
  error: string | null;
  refetchCommonData: () => Promise<void>;
}

const defaultCommonData: CommonData = {
  age_ranges: [],
  building_block_types: [],
  channels: [],
  genders: [],
  income_types: [],
  line_of_business: [],
  role_statuses: [],
  role_types: [],
  roles: [],
  map_states: [],
  customer_goals: [],
};

const AppContext = createContext<AppContextType>({
  commonData: defaultCommonData,
  isLoading: true,
  error: null,
  refetchCommonData: async () => {}, // Default empty function
});

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [commonData, setCommonData] = useState<CommonData>(defaultCommonData);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, userInfo } = useAuth();

  // Function to fetch common data that can be called from outside
  const fetchCommonData = async () => {
    try {
      setIsLoading(true);
      const data = await appService.getCommonData();
      setCommonData(data);
      setError(null);
    } catch (err) {
      setError("Failed to fetch common data");
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch common data when the component mounts if user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchCommonData();
    }
  }, [isAuthenticated]);

  // Refetch common data when user info changes (after login)
  useEffect(() => {
    if (userInfo) {
      fetchCommonData();
    }
  }, [userInfo]);

  // Listen for the custom login event
  useEffect(() => {
    const handleUserLogin = () => {
      fetchCommonData();
    };

    window.addEventListener("user-login", handleUserLogin);
    return () => window.removeEventListener("user-login", handleUserLogin);
  }, []);

  const value = useMemo(
    () => ({
      commonData,
      isLoading,
      error,
      refetchCommonData: fetchCommonData,
    }),
    [commonData, isLoading, error]
  );

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

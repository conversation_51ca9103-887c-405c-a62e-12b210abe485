import mapService from "@/services/MapService";
import { Action, BuildingBlock, BuildingBlockType, MapRole, Phase } from "@/types/map";
import React, { createContext, ReactNode, useContext, useReducer } from "react";

type Connection = {
  source: string;
  target: string;
};

interface MapCanvasState {
  phases: Phase[];
  actions: Action[];
  roles: MapRole[];
  buildingBlocks: BuildingBlock[];
  buildingBlockTypes: BuildingBlockType[];
  connections: Connection[];
  isStoryMode: boolean;
  visibleRoleTypes: string[];
  visibleBuildingBlockTypes: string[];
}

// Define an enum for action types
export enum MapCanvasActionType {
  ADD_PHASE = "ADD_PHASE",
  EDIT_PHASE = "EDIT_PHASE",
  DELETE_PHASE = "DELETE_PHASE",
  SET_DATA = "SET_DATA",
  ADD_CONNECTION = "ADD_CONNECTION",
  UPDATE_CONNECTIONS = "UPDATE_CONNECTIONS",
  REORDER_PHASES = "REORDER_PHASES",
  ADD_ACTION = "ADD_ACTION",
  EDIT_ACTION = "EDIT_ACTION",
  DELETE_ACTION = "DELETE_ACTION",
  ADD_BUILDING_BLOCK = "ADD_BUILDING_BLOCK",
  EDIT_BUILDING_BLOCK = "EDIT_BUILDING_BLOCK",
  DELETE_BUILDING_BLOCK = "DELETE_BUILDING_BLOCK",
  TOGGLE_STORY_MODE = "TOGGLE_STORY_MODE",
  TOGGLE_ROLE_TYPE = "TOGGLE_ROLE_TYPE",
  SET_VISIBLE_ROLE_TYPES = "SET_VISIBLE_ROLE_TYPES",
  SET_VISIBLE_BUILDING_BLOCK_TYPES = "SET_VISIBLE_BUILDING_BLOCK_TYPES",
  TOGGLE_BUILDING_BLOCK_TYPE = "TOGGLE_BUILDING_BLOCK_TYPE",
  SET_BUILDING_BLOCKS = "SET_BUILDING_BLOCKS",
}

// Use the enum in the MapCanvasAction type
type MapCanvasAction =
  | { type: MapCanvasActionType.ADD_PHASE; payload: { phase: Phase } }
  | { type: MapCanvasActionType.DELETE_BUILDING_BLOCK; payload: { id: number } }
  | {
      type: MapCanvasActionType.EDIT_PHASE;
      payload: { phase: Phase };
    }
  | { type: MapCanvasActionType.DELETE_PHASE; payload: { id: number } }
  | {
      type: MapCanvasActionType.SET_DATA;
      payload: {
        actions: Action[];
        phases: Phase[];
        building_blocks: BuildingBlock[];
        building_block_types: BuildingBlockType[];
        roles: MapRole[];
        id?: string; // Map ID for fetching building blocks
      };
    }
  | {
      type: MapCanvasActionType.ADD_CONNECTION;
      payload: { connection: Connection };
    }
  | {
      type: MapCanvasActionType.UPDATE_CONNECTIONS;
      payload: { connections: Connection[] };
    }
  | {
      type: MapCanvasActionType.REORDER_PHASES;
      payload: { order: number; id: number; name: string }[];
    }
  | { type: MapCanvasActionType.ADD_ACTION; payload: { action: Action } }
  | {
      type: MapCanvasActionType.EDIT_ACTION;
      payload: { id: string; updatedFields: Partial<Action> };
    }
  | { type: MapCanvasActionType.DELETE_ACTION; payload: { id: string } }
  | {
      type: MapCanvasActionType.ADD_BUILDING_BLOCK;
      payload: { buildingBlock: BuildingBlock };
    }
  | {
      type: MapCanvasActionType.EDIT_BUILDING_BLOCK;
      payload: { id: number; updatedFields: Partial<BuildingBlock> };
    }
  | { type: MapCanvasActionType.TOGGLE_STORY_MODE; payload: boolean }
  | { type: MapCanvasActionType.TOGGLE_ROLE_TYPE; payload: { roleType: string } }
  | { type: MapCanvasActionType.SET_VISIBLE_ROLE_TYPES; payload: string[] }
  | { type: MapCanvasActionType.TOGGLE_BUILDING_BLOCK_TYPE; payload: { buildingBlockType: string } }
  | { type: MapCanvasActionType.SET_VISIBLE_BUILDING_BLOCK_TYPES; payload: string[] }
  | { type: MapCanvasActionType.SET_BUILDING_BLOCKS; payload: BuildingBlock[] };

const initialState: MapCanvasState = {
  phases: [],
  actions: [],
  roles: [],
  buildingBlocks: [],
  buildingBlockTypes: [],
  connections: [],
  isStoryMode: false,
  visibleRoleTypes: [],
  visibleBuildingBlockTypes: [],
};

function mapCanvasReducer(state: MapCanvasState, action: MapCanvasAction): MapCanvasState {
  switch (action.type) {
    case MapCanvasActionType.ADD_PHASE:
      return {
        ...state,
        phases: [...state.phases, action.payload.phase],
      };

    case MapCanvasActionType.EDIT_PHASE:
      return {
        ...state,
        phases: state.phases.map((phase) =>
          phase.id === action.payload.phase.id ? { ...action.payload.phase } : phase
        ),
      };

    case MapCanvasActionType.DELETE_PHASE:
      return {
        ...state,
        phases: state.phases
          .filter((phase) => phase.id !== action.payload.id)
          .map((phase, index) => ({ ...phase, order: index })),
        actions: state.actions.filter((item) => item.phase !== action.payload.id),
      };

    case MapCanvasActionType.ADD_ACTION:
      return {
        ...state,
        actions: [...state.actions, action.payload.action],
      };

    case MapCanvasActionType.EDIT_ACTION:
      return {
        ...state,
        actions: state.actions.map((item) =>
          item.id === action.payload.id ? { ...item, ...action.payload.updatedFields } : item
        ),
      };

    case MapCanvasActionType.DELETE_ACTION:
      return {
        ...state,
        actions: state.actions.filter((item) => item.id !== action.payload.id),
      };

    case MapCanvasActionType.ADD_BUILDING_BLOCK:
      return {
        ...state,
        buildingBlocks: [...state.buildingBlocks, action.payload.buildingBlock],
      };

    case MapCanvasActionType.EDIT_BUILDING_BLOCK:
      return {
        ...state,
        buildingBlocks: state.buildingBlocks.map((item) =>
          item.id === action.payload.id ? { ...item, ...action.payload.updatedFields } : item
        ),
      };

    case MapCanvasActionType.DELETE_BUILDING_BLOCK:
      return {
        ...state,
        buildingBlocks: state.buildingBlocks.filter((item) => item.id !== action.payload.id),
      };

    case MapCanvasActionType.SET_DATA:
      // Process building blocks to normalize the structure and add phase information
      const processedBuildingBlocks = action.payload.building_blocks.map((block) => {
        // Extract type and sub_type IDs from the nested objects
        const typeId = typeof block.type === "object" ? block.type.id : block.type;
        const subTypeId = typeof block.sub_type === "object" ? block.sub_type.id : block.sub_type;

        // For building blocks from the map details API, we need to assign them to phases
        // Since the API doesn't provide phase information directly, we'll need to handle this
        // based on the connection or other logic. For now, we'll assign to the first phase
        // or handle this in the component level

        return {
          ...block,
          type: typeId,
          sub_type: subTypeId,
          // Keep the original nested objects for reference if needed
          typeObject: typeof block.type === "object" ? block.type : undefined,
          subTypeObject: typeof block.sub_type === "object" ? block.sub_type : undefined,
        };
      });

      // If map ID is provided, fetch building blocks using the API
      if (action.payload.id) {
        // Note: This is a temporary solution due to API limitations
        // We're calling the API here to get the building blocks with nested block information
        // In the future, this should be handled by the API directly
        mapService.getBuildingBlocks(action.payload.id).then((buildingBlocks) => {
          // Dispatch a separate action to update building blocks once they're fetched
          if (buildingBlocks.length > 0) {
            action.payload.building_blocks = buildingBlocks;
          }
        });
      }

      return {
        ...state,
        actions: action.payload.actions,
        phases: action.payload.phases,
        buildingBlocks: processedBuildingBlocks,
        buildingBlockTypes: action.payload.building_block_types,
        roles: action.payload.roles,
      };

    case MapCanvasActionType.SET_BUILDING_BLOCKS:
      return {
        ...state,
        buildingBlocks: action.payload,
      };

    case MapCanvasActionType.ADD_CONNECTION:
      return {
        ...state,
        connections: [...state.connections, action.payload.connection],
      };

    case MapCanvasActionType.UPDATE_CONNECTIONS:
      return {
        ...state,
        connections: action.payload.connections,
      };

    case MapCanvasActionType.REORDER_PHASES:
      const orderedActions = action.payload.flatMap((phase) =>
        state.actions
          .filter((action) => action.phase === phase.id)
          .map((action) => ({ ...action, phase: phase.id }))
      );

      return {
        ...state,
        phases: action.payload,
        actions: orderedActions,
      };

    case MapCanvasActionType.TOGGLE_STORY_MODE:
      return {
        ...state,
        isStoryMode: action.payload,
      };

    case MapCanvasActionType.TOGGLE_ROLE_TYPE:
      const { roleType } = action.payload;
      return {
        ...state,
        visibleRoleTypes: state.visibleRoleTypes.includes(roleType)
          ? state.visibleRoleTypes.filter((name) => name !== roleType)
          : [...state.visibleRoleTypes, roleType],
      };

    case MapCanvasActionType.SET_VISIBLE_ROLE_TYPES:
      return {
        ...state,
        visibleRoleTypes: action.payload,
      };

    case MapCanvasActionType.TOGGLE_BUILDING_BLOCK_TYPE:
      const { buildingBlockType } = action.payload;
      return {
        ...state,
        visibleBuildingBlockTypes: state.visibleBuildingBlockTypes.includes(buildingBlockType)
          ? state.visibleBuildingBlockTypes.filter((name) => name !== buildingBlockType)
          : [...state.visibleBuildingBlockTypes, buildingBlockType],
      };

    case MapCanvasActionType.SET_VISIBLE_BUILDING_BLOCK_TYPES:
      return {
        ...state,
        visibleBuildingBlockTypes: action.payload,
      };

    default:
      return state;
  }
}

const MapCanvasContext = createContext<{
  state: MapCanvasState;
  dispatch: React.Dispatch<MapCanvasAction>;
}>({
  state: initialState,
  dispatch: () => undefined,
});

export const useMapCanvas = () => useContext(MapCanvasContext);

export const MapCanvasProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(mapCanvasReducer, initialState);

  return (
    <MapCanvasContext.Provider value={{ state, dispatch }}>{children}</MapCanvasContext.Provider>
  );
};

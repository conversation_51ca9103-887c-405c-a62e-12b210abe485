import { useLogin, useLogout, useCurrentUser, useIsAuthenticated } from "@/api/v2/hooks/useAuth";
import { apiV2Client } from "@/api/v2/base";
import type { UserProfile, LoginRequest } from "@/types/api/client";
import { useRouter } from "next/navigation";
import React, { createContext, useCallback, useContext, useEffect } from "react";

const SESSION_TIMER_THRESHOLD = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
const CHECK_INTERVAL = 1000 * 60 * 10; // 10 minutes interval

interface AuthContextType {
  isAuthenticated: boolean;
  userInfo: UserProfile | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const router = useRouter();
  
  // Use the new API v2 hooks
  const loginMutation = useLogin();
  const logoutMutation = useLogout();
  const { data: currentUser } = useCurrentUser();
  const isAuthenticated = useIsAuthenticated();
  

  const checkSessionValidity = useCallback(() => {
    const storedLastLoginTime = localStorage.getItem("lastLoginTime");
    const currentTime = Date.now();

    if (storedLastLoginTime) {
      const timeSinceLastLogin = currentTime - Number(storedLastLoginTime);
      if (timeSinceLastLogin > SESSION_TIMER_THRESHOLD) {
        logoutMutation.mutate();
      }
    } else if (isAuthenticated) {
      logoutMutation.mutate(); // If no last login time is found but user appears authenticated, force logout
    }
  }, [logoutMutation, isAuthenticated]);

  const setLogoutTimer = useCallback(() => {
    // Periodically check session validity
    const interval = setInterval(checkSessionValidity, CHECK_INTERVAL);
    return () => clearInterval(interval); // Cleanup on unmount
  }, [checkSessionValidity]);

  useEffect(() => {
    const initializeAuth = () => {
      const authToken = localStorage.getItem("authToken");
      const storedLastLoginTime = localStorage.getItem("lastLoginTime");

      if (authToken && storedLastLoginTime) {
        apiV2Client.setAuthToken(authToken);
        checkSessionValidity();
        return setLogoutTimer();
      } else if (!authToken && typeof window !== 'undefined') {
        // Only redirect to login if we're on the client side and not already on login page
        if (window.location.pathname !== '/login') {
          router.push("/login");
        }
      }
    };

    const cleanup = initializeAuth();
    return () => {
      cleanup?.();
    };
  }, [router, setLogoutTimer, checkSessionValidity]);

  const login = async (username: string, password: string) => {
    const credentials: LoginRequest = {
      username,
      password
    };

    try {
      const response = await loginMutation.mutateAsync(credentials);
      
      // Store login time for session management
      const loginTime = Date.now();
      localStorage.setItem("lastLoginTime", loginTime.toString());

      // Dispatch a custom event to notify other components about the login
      const loginEvent = new CustomEvent("user-login", {
        detail: { user: response.user },
      });
      window.dispatchEvent(loginEvent);

      checkSessionValidity();
      setLogoutTimer();
      router.push("/");
    } catch (error) {
      // Error handling is already done in the useLogin hook
      throw error;
    }
  };

  const logout = useCallback(() => {
    logoutMutation.mutate();
    localStorage.removeItem("lastLoginTime");
  }, [logoutMutation]);

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      userInfo: currentUser, 
      login, 
      logout,
      isLoading: loginMutation.isPending || logoutMutation.isPending
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook for consuming the AuthContext
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

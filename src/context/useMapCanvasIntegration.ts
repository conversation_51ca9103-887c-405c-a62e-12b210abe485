import { useCallback } from 'react';
import { useMapCanvas, mapCanvasActions } from './MapCanvasProvider';
import { 
  useCreatePhase, 
  useUpdatePhase, 
  useDeletePhase 
} from '@/api/v2/hooks/usePhases';
import { 
  useCreateMoment, 
  useUpdateMoment, 
  useDeleteMoment 
} from '@/api/v2/hooks/useMoments';
import { 
  useCreateActivity, 
  useUpdateActivity, 
  useDeleteActivity 
} from '@/api/v2/hooks/useActivities';
import type { 
  PhaseCreate, 
  PhaseUpdate, 
  MomentCreate, 
  MomentUpdate, 
  ActivityCreate, 
  ActivityUpdate 
} from '@/types/api/client';

/**
 * Custom hook that integrates MapCanvas state management with API calls
 * This provides a unified interface for updating both local state and server state
 */
export const useMapCanvasIntegration = () => {
  const { state, dispatch } = useMapCanvas();
  
  // API mutation hooks
  const createPhaseMutation = useCreatePhase();
  const updatePhaseMutation = useUpdatePhase();
  const deletePhaseMutation = useDeletePhase();
  
  const createMomentMutation = useCreateMoment();
  const updateMomentMutation = useUpdateMoment();
  const deleteMomentMutation = useDeleteMoment();
  
  const createActivityMutation = useCreateActivity();
  const updateActivityMutation = useUpdateActivity();
  const deleteActivityMutation = useDeleteActivity();

  // Phase operations
  const createPhase = useCallback(async (mapId: string, phaseData: PhaseCreate) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      const newPhase = await createPhaseMutation.mutateAsync({ mapId, phaseData });
      
      // Convert Phase to PhaseDetails (add moment_ids)
      const phaseDetails = {
        ...newPhase,
        moment_ids: []
      };
      
      dispatch(mapCanvasActions.createPhase(phaseDetails));
      dispatch(mapCanvasActions.setError(null));
      
      return newPhase;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create phase';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [createPhaseMutation, dispatch]);

  const updatePhase = useCallback(async (phaseId: string, updates: PhaseUpdate) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      const updatedPhase = await updatePhaseMutation.mutateAsync({ phaseId, updates });
      
      // Update local state optimistically
      dispatch(mapCanvasActions.updatePhase(phaseId, updates));
      dispatch(mapCanvasActions.setError(null));
      
      return updatedPhase;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update phase';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [updatePhaseMutation, dispatch]);

  const deletePhase = useCallback(async (phaseId: string, mapId: string) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      await deletePhaseMutation.mutateAsync({ phaseId, mapId });
      
      dispatch(mapCanvasActions.deletePhase(phaseId));
      dispatch(mapCanvasActions.setError(null));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete phase';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [deletePhaseMutation, dispatch]);

  // Moment operations
  const createMoment = useCallback(async (
    mapId: string, 
    phaseId: string, 
    momentData: MomentCreate
  ) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      const newMoment = await createMomentMutation.mutateAsync({ 
        mapId, 
        phaseId, 
        momentData 
      });
      
      // Convert Moment to MomentDetails (add activities)
      const momentDetails = {
        ...newMoment,
        activities: {
          customer: [],
          front_stage: [],
          back_stage: [],
          system: []
        }
      };
      
      dispatch(mapCanvasActions.createMoment(momentDetails));
      
      // Update the phase to include this moment ID
      dispatch(mapCanvasActions.updatePhase(phaseId, {
        moment_ids: [...(state.phases.find(p => p.phase_id === phaseId)?.moment_ids || []), newMoment.moment_id]
      }));
      
      dispatch(mapCanvasActions.setError(null));
      
      return newMoment;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create moment';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [createMomentMutation, dispatch, state.phases]);

  const updateMoment = useCallback(async (momentId: string, updates: MomentUpdate) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      const updatedMoment = await updateMomentMutation.mutateAsync({ momentId, updates });
      
      dispatch(mapCanvasActions.updateMoment(momentId, updates));
      dispatch(mapCanvasActions.setError(null));
      
      return updatedMoment;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update moment';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [updateMomentMutation, dispatch]);

  const deleteMoment = useCallback(async (momentId: string) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      await deleteMomentMutation.mutateAsync(momentId);
      
      dispatch(mapCanvasActions.deleteMoment(momentId));
      dispatch(mapCanvasActions.setError(null));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete moment';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [deleteMomentMutation, dispatch]);

  // Activity operations
  const createActivity = useCallback(async (
    momentId: string, 
    activityData: ActivityCreate
  ) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      const newActivity = await createActivityMutation.mutateAsync({ 
        momentId, 
        activityData 
      });
      
      // Convert Activity to ActivityDetails (add action_details)
      const activityDetails = {
        ...newActivity,
        action_details: []
      };
      
      dispatch(mapCanvasActions.createActivity(momentId, activityDetails));
      dispatch(mapCanvasActions.setError(null));
      
      return newActivity;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create activity';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [createActivityMutation, dispatch]);

  const updateActivity = useCallback(async (activityId: string, updates: ActivityUpdate) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      const updatedActivity = await updateActivityMutation.mutateAsync({ activityId, updates });
      
      dispatch(mapCanvasActions.updateActivity(activityId, updates));
      dispatch(mapCanvasActions.setError(null));
      
      return updatedActivity;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update activity';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [updateActivityMutation, dispatch]);

  const deleteActivity = useCallback(async (activityId: string, momentId: string) => {
    try {
      dispatch(mapCanvasActions.setLoading(true));
      
      await deleteActivityMutation.mutateAsync({ activityId, momentId });
      
      dispatch(mapCanvasActions.deleteActivity(activityId));
      dispatch(mapCanvasActions.setError(null));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete activity';
      dispatch(mapCanvasActions.setError(errorMessage));
      throw error;
    } finally {
      dispatch(mapCanvasActions.setLoading(false));
    }
  }, [deleteActivityMutation, dispatch]);

  return {
    // State
    state,
    dispatch,
    
    // Phase operations
    createPhase,
    updatePhase,
    deletePhase,
    
    // Moment operations
    createMoment,
    updateMoment,
    deleteMoment,
    
    // Activity operations
    createActivity,
    updateActivity,
    deleteActivity,
    
    // Loading states
    isCreatingPhase: createPhaseMutation.isPending,
    isUpdatingPhase: updatePhaseMutation.isPending,
    isDeletingPhase: deletePhaseMutation.isPending,
    
    isCreatingMoment: createMomentMutation.isPending,
    isUpdatingMoment: updateMomentMutation.isPending,
    isDeletingMoment: deleteMomentMutation.isPending,
    
    isCreatingActivity: createActivityMutation.isPending,
    isUpdatingActivity: updateActivityMutation.isPending,
    isDeletingActivity: deleteActivityMutation.isPending,
  };
};
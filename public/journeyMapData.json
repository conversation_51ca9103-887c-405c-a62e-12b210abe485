{"map_id": "Home lending", "actions": [{"id": "1", "phase": 1, "desc": "I look for information on home loans.", "component_models": null, "channels": ["Digital - Public website"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "2"}]}, {"id": "2", "phase": 1, "desc": "I talk to a Banker about my Home Loan needs.", "component_models": null, "channels": ["Branch"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "3"}]}, {"id": "3", "phase": 2, "desc": "I provide information on my financial situation.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "4"}]}, {"id": "4", "phase": 2, "desc": "I provide East bank with information they require to confirm my identity and begin application process.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "5"}]}, {"id": "5", "phase": 3, "desc": "I talk to my banker about the possible products and select my preferred option.", "component_models": null, "channels": ["Branch"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "6"}]}, {"id": "6", "phase": 3, "desc": "I fill in an application either at home or in a Branch, with the Banker.", "component_models": null, "channels": ["Digital - Online banking"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "7"}]}, {"id": "7", "phase": 3, "desc": "I provide the Banker with information and documents on my income and expenses.", "component_models": null, "channels": ["Digital - Online banking"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "8"}]}, {"id": "8", "phase": 3, "desc": "Send my Banker information on property I am using as security on the loan.", "component_models": null, "channels": ["Email"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "9"}]}, {"id": "9", "phase": 3, "desc": "I agree to a valuation.", "component_models": null, "channels": ["Digital - Online banking"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "10"}]}, {"id": "10", "phase": 4, "desc": "I authorise my Banker to conduct a Credit Check.", "component_models": null, "channels": ["Digital - Online banking"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "11"}]}, {"id": "11", "phase": 4, "desc": "I negotiate fees, rate, and benefits with my Banker.", "component_models": null, "channels": ["Branch"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "12"}]}, {"id": "12", "phase": 4, "desc": "I sign the contract with East bank for a Home Loan.", "component_models": null, "channels": ["Branch"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "13"}]}, {"id": "13", "phase": "Approval", "desc": "My Offer on a home is accepted - I inform East bank of the details, including when settlement is required.", "component_models": null, "channels": ["Branch"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "14"}]}, {"id": "14", "phase": 5, "desc": "My solicitor and East bank arrange settlement.", "component_models": null, "channels": [null], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "15"}]}, {"id": "15", "phase": 6, "desc": "I add my Home Loan to my East bank One account.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "16"}]}, {"id": "16", "phase": 7, "desc": "I access my Home Loan through my East bank One account.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "17"}]}, {"id": "17", "phase": 7, "desc": "I make payments on my Home Loan.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "18"}]}, {"id": "18", "phase": 7, "desc": "I drawdown on my construction loan.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "19"}]}, {"id": "19", "phase": 8, "desc": "My floating interest rate changes.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "20"}]}, {"id": "20", "phase": 8, "desc": "My fixed rate is due to expire, I authorise a fixed rate rollover.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "21"}]}, {"id": "21", "phase": 8, "desc": "I seek a Home Loan top-up from East bank.", "component_models": null, "channels": ["Branch"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": "22"}]}, {"id": "22", "phase": 8, "desc": "I request statements from East bank.", "component_models": null, "channels": ["Digital - Mobile App"], "supporting_system": [null], "type": "Customer", "role": "Generic Existing Customer", "transitions": [{"to": null}]}, {"id": "23", "phase": 1, "desc": "Make contract with a customer to progress a lead.", "component_models": null, "channels": [null], "supporting_system": [null], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "24"}]}, {"id": "24", "phase": 2, "desc": "Meet with customer and discuss their lending needs.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "25"}]}, {"id": "25", "phase": 3, "desc": "Prepare a submission for Credit.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "26"}]}, {"id": "26", "phase": 4, "desc": "I prepare documents for Credit Review.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "27"}]}, {"id": "27", "phase": 4, "desc": "I check with customer on any missing information and document any further requirements.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "28"}]}, {"id": "28", "phase": 4, "desc": "I submit documents to Credit and await response.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "29"}]}, {"id": "29", "phase": 5, "desc": "I prepare a contract and discuss terms with customer.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "30"}]}, {"id": "30", "phase": 5, "desc": "Prepare for settlement.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "31"}]}, {"id": "31", "phase": 6, "desc": "Conduct settlement.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "32"}]}, {"id": "32", "phase": 6, "desc": "Follow up on customer satisfaction post-settlement.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "33"}]}, {"id": "33", "phase": 6, "desc": "Manage customer relationships for future opportunities.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "34"}]}, {"id": "34", "phase": 7, "desc": "I review loan statements with the customer.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "35"}]}, {"id": "35", "phase": 7, "desc": "Provide updates on interest rates to the customer.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "36"}]}, {"id": "36", "phase": 7, "desc": "Discuss options for refinancing with the customer.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "37"}]}, {"id": "37", "phase": 8, "desc": "Offer additional banking products to the customer.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Frontstage", "role": "Relationship Manager", "transitions": [{"to": "38"}]}, {"id": "38", "phase": 8, "desc": "Request feedback on the overall lending experience.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Backstage", "role": "Operations Team", "transitions": [{"to": "39"}]}, {"id": "39", "phase": 8, "desc": "Address any ongoing customer concerns or issues.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Backstage", "role": "Operations Team", "transitions": [{"to": "40"}]}, {"id": "40", "phase": 8, "desc": "Ensure compliance with banking regulations throughout the process.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Backstage", "role": "Operations Team", "transitions": [{"to": null}]}, {"id": "41", "phase": 8, "desc": "Maintain regular communication with the customer.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Backstage", "role": "Operations Team", "transitions": [{"to": null}]}, {"id": "42", "phase": 8, "desc": "Provide the customer with ongoing support and assistance.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Backstage", "role": "Operations Team", "transitions": [{"to": null}]}, {"id": "43", "phase": 8, "desc": "Evaluate customer feedback to improve processes.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Backstage", "role": "Operations Team", "transitions": [{"to": null}]}, {"id": "44", "phase": 8, "desc": "Implement changes based on feedback received.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Backstage", "role": "Operations Team", "transitions": [{"to": null}]}, {"id": "45", "phase": 8, "desc": "Continuously monitor industry trends and customer needs.", "component_models": null, "channels": [null], "supporting_system": ["8"], "type": "Backstage", "role": "Operations Team", "transitions": [{"to": null}]}], "phases": [{"id": 1, "label": "Research", "order": 1, "desc": "Brainstorming and identifying user needs or market demands.\r\nGathering requirements from stakeholders, including customers, internal teams, and market research.\r\nDefining the problem statement and the key value proposition.", "image": null, "components": []}, {"id": 2, "label": "Consultation", "order": 2, "desc": "Customer consultation and information gathering phase", "image": null, "components": []}, {"id": 3, "label": "Application", "order": 3, "desc": "Application submission and documentation phase", "image": null, "components": []}, {"id": 4, "label": "Approval", "order": 4, "desc": "Application review and approval process", "image": null, "components": []}, {"id": 5, "label": "Settlement", "order": 5, "desc": "Loan settlement and documentation phase", "image": null, "components": []}, {"id": 6, "label": "Post-settlement", "order": 6, "desc": "Post-settlement activities and account setup", "image": null, "components": []}, {"id": 7, "label": "Account Management", "order": 7, "desc": "Ongoing account management and maintenance", "image": null, "components": []}, {"id": 8, "label": "Rate & Top-up", "order": 8, "desc": "Rate changes, top-ups and ongoing loan management", "image": null, "components": []}], "building_blocks": [{"id": "1", "phase": "Research", "building_block_types": "painpoints", "desc": "painpoints1.", "role": "generic_existing customer"}, {"id": "2", "phase": "Research", "building_block_types": "painpoints", "desc": "painpoints2.", "role": "generic_existing customer"}, {"id": "3", "phase": "Research", "building_block_types": "painpoints", "desc": "painpoints3.", "role": "relationship_manager"}, {"id": "4", "phase": "Research", "building_block_types": "opportunity", "desc": "Opportunity1.", "role": "generic_existing customer"}, {"id": "5", "phase": "Research", "building_block_types": "opportunity", "desc": "Opportunity2.", "role": "generic_existing customer"}, {"id": "6", "phase": "Research", "building_block_types": "opportunity", "desc": "Opportunity3.", "role": "generic_existing customer"}]}